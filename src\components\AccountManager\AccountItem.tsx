/**
 * @file Individual account item component for the account manager
 */
import { Edit, Trash2, <PERSON><PERSON>, AlertCircle, Undo2 } from 'lucide-react';
import React from 'react';

import { useAccountStore } from '../../shared/store/accountStore';
import { useMainSettingsStore } from '../../shared/store/mainSettingsStore';
import type { Account } from '../../shared/types/account';
import { Avatar, AvatarFallback } from '../../shared/ui';
import { Button } from '../../shared/ui/button';
import { Card, CardContent } from '../../shared/ui/card';
import { cn } from '../../shared/utils/utils';

interface AccountItemProps {
  account: Account;
  isSelected: boolean;
  isRecentlyDeleted: boolean;
  onSelect: (_accountId: string) => void;
  onEdit: (_account: Account) => void;
  onDelete: (_accountId: string) => void;
  onUndo: (_accountId: string) => void;
  onCopy: (_account: Account) => void;
  onContextMenu: (_e: React.MouseEvent, _accountId: string) => void;
}

// Helper functions
const getInitials = (email: string): string => {
  return email.substring(0, 2).toUpperCase();
};

const getConnectionStatus = (status: string): { color: string; label: string } => {
  switch (status) {
    case 'connected':
      return { color: 'bg-green-500', label: 'Connected' };
    case 'connecting':
      return { color: 'bg-yellow-500', label: 'Connecting' };
    case 'error':
      return { color: 'bg-red-500', label: 'Error' };
    default:
      return { color: 'bg-gray-500', label: 'Disconnected' };
  }
};

// Deleted account component
const DeletedAccountItem: React.FC<{
  account: Account;
  onUndo: (_accountId: string) => void;
}> = ({ account, onUndo }): React.JSX.Element => (
  <Card className="border-red-500/50 bg-red-500/10">
    <CardContent className="p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <AlertCircle className="h-5 w-5 text-red-500" />
          <div>
            <p className="font-medium text-red-400">Account Deleted</p>
            <p className="text-sm text-red-300">{account.email}</p>
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onUndo(account.id)}
          className="gap-2 border-red-500/50 text-red-400 hover:bg-red-500/20"
        >
          <Undo2 size={14} />
          Undo
        </Button>
      </div>
    </CardContent>
  </Card>
);

/**
 * Individual account item component
 */
const AccountItem: React.FC<AccountItemProps> = ({
  account,
  isSelected,
  isRecentlyDeleted,
  onSelect,
  onEdit,
  onDelete,
  onUndo,
  onCopy,
  onContextMenu,
}): React.JSX.Element => {
  const { settings } = useMainSettingsStore();
  const { accounts } = useAccountStore();
  const status = getConnectionStatus(account.connectionStatus ?? 'disconnected');

  if (isRecentlyDeleted) {
    return <DeletedAccountItem account={account} onUndo={onUndo} />;
  }

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md",
        isSelected
          ? "border-primary bg-primary/10 shadow-lg"
          : "border-border hover:border-primary/50"
      )}
      onClick={() => onSelect(account.id)}
      onContextMenu={(e) => onContextMenu(e, account.id)}
    >
      <CardContent className={cn("p-4", settings.compactAccountView === true && "py-2 px-3")}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            {settings.compactAccountView !== true ? (
              <Avatar className="h-10 w-10">
                <AvatarFallback className="bg-primary/20 text-primary font-medium">
                  {getInitials(account.email)}
                </AvatarFallback>
              </Avatar>
            ) : (
              <div className="flex items-center gap-1.5">
                <span
                  className={cn(
                    "text-xs font-mono w-5 h-5 rounded-full flex items-center justify-center",
                    status.color === "bg-green-500" ? "bg-green-500/20 text-green-400" :
                    status.color === "bg-yellow-500" ? "bg-yellow-500/20 text-yellow-400" :
                    "bg-red-500/20 text-red-400"
                  )}
                  title={`Account ${accounts.findIndex((a: Account) => a.id === account.id) + 1} - ${status.label}`}
                >
                  {accounts.findIndex((a: Account) => a.id === account.id) + 1}
                </span>
              </div>
            )}

            <div className="min-w-0 flex-1">
              {settings.compactAccountView === true ? (
                <p className="text-foreground truncate">
                  {account.displayName ?? account.email}
                </p>
              ) : (
                <>
                  <div className="flex items-center gap-2">
                    <p className="font-medium text-foreground truncate">
                      {account.displayName ?? account.email}
                    </p>
                    <div
                      className={cn("w-2 h-2 rounded-full", status.color)}
                      title={status.label}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground truncate">
                    {account.email}
                  </p>
                </>
              )}
            </div>
          </div>

          <div className="flex items-center gap-1 ml-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onEdit(account);
              }}
              className="h-8 w-8 p-0 hover:bg-primary/20"
              title="Edit account"
            >
              <Edit size={14} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onCopy(account);
              }}
              className="h-8 w-8 p-0 hover:bg-primary/20"
              title="Copy credentials"
            >
              <Copy size={14} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(account.id);
              }}
              className="h-8 w-8 p-0 hover:bg-red-500/20 text-red-400"
              title="Delete account"
            >
              <Trash2 size={14} />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AccountItem;
