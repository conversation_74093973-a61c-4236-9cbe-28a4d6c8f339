import React, { useEffect } from 'react';
import { Toaster } from 'sonner';

import ErrorBoundary from './components/ErrorBoundary';
import Layout from './components/Layout';
import { useAppInitialization } from './shared/hooks/useAppInitialization';
import { useAccountStore } from './shared/store/accountStore';
import { useLogStore } from './shared/store/logStore';
import { ThemeProvider } from './shared/ui/theme-provider';



const App = (): React.JSX.Element => {
  const { addLog } = useLogStore();
  const { setAccountConnectionStatus } = useAccountStore();

  // Initialize the application (load accounts, proxy settings, etc.)
  useAppInitialization();

  useEffect(() => {
    try {
      // Listen for log messages from the main process
      window.ipcApi.on('log:add', ({ level, message }: { level: 'info' | 'success' | 'error', message: string }) => {
        addLog(message, level);
      });

      // Listen for account connection status updates
      window.ipcApi.on('account:connection-status', ({ accountId, status }: { accountId: string, status: 'connected' | 'connecting' | 'disconnected' }) => {
        setAccountConnectionStatus(accountId, status);
      });

      // Notify the main process that the renderer is ready
      void window.ipcApi.rendererReady();
    } catch (error) {
      addLog(`Failed to initialize application: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
    }
  }, [addLog, setAccountConnectionStatus]);

  return (
    <ThemeProvider defaultTheme="dark" storageKey="imapviewer-ui-theme">
      <ErrorBoundary>
        <Layout />
        <Toaster
          position="top-right"
          richColors
          closeButton
          toastOptions={{
            style: {
              background: 'hsl(var(--background))',
              border: '1px solid hsl(var(--border))',
              color: 'hsl(var(--foreground))',
            },
          }}
        />
      </ErrorBoundary>
    </ThemeProvider>
  );
};

export default App;