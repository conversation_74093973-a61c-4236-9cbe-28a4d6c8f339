/**
 * @file Panel that contains the list of mailboxes.
 */
import {
  ChevronDown,
  RefreshCw, Loader2, Mailbox
} from 'lucide-react';
import React from 'react';

import { useMailboxManager } from '../shared/hooks/useMailboxManager';
import { useAccountStore } from '../shared/store/accountStore';
import { Button } from '../shared/ui/button';

interface EmailListPanelProps {
  searchQuery?: string;
}

/**
 * Component for displaying and managing email folders
 */
const EmailListPanel: React.FC<EmailListPanelProps> = React.memo(({ searchQuery = '' }) => {
  const {
    selectedAccountId,
    selectedMailbox,
    selectMailbox,
  } = useAccountStore();

  const {
    isLoading,
    isRefreshing,
    showFolders,
    setShowFolders,
    renderedFolders,
    handleRefresh,
  } = useMailboxManager();



  if (isLoading) {
    return (
      <div className="flex flex-col h-full items-center justify-center p-6 bg-background text-foreground">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 rounded-full flex items-center justify-center bg-muted">
            <Loader2 size={28} className="text-primary animate-spin" />
          </div>
          <div className="text-center">
            <h3 className="text-lg font-medium mb-2">Loading Folders</h3>
            <p className="text-sm text-muted-foreground">Please wait a moment</p>
          </div>
        </div>
      </div>
    );
  }

  if ((selectedAccountId?.length ?? 0) === 0) {
    return (
      <div className="flex flex-col h-full items-center justify-center p-6 bg-background text-foreground">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 rounded-full flex items-center justify-center bg-muted">
            <Mailbox size={28} className="text-muted-foreground" />
          </div>
          <div className="text-center">
            <h3 className="text-lg font-medium mb-2">No Account Selected</h3>
            <p className="text-sm text-gray-400">Select an account to view your emails</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <nav className="flex flex-col h-full bg-background text-foreground" aria-label="Email folders">
      {/* Folders section */}
      <div className="p-3 flex-grow overflow-auto custom-scrollbar">
        <div className="flex items-center justify-between pb-2 mb-1">
          <button
            className="flex items-center gap-2 cursor-pointer py-2 px-1 focus:outline-none focus:ring-2 focus:ring-ring rounded"
            onClick={() => setShowFolders(!showFolders)}
            aria-expanded={showFolders}
            aria-controls="folders-list"
          >
            <ChevronDown
              size={16}
              className={`text-muted-foreground transition-transform duration-200 ${showFolders ? 'transform rotate-0' : 'transform -rotate-90'}`}
              aria-hidden="true"
            />
            <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wider">Folders</h3>
          </button>

          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => { void handleRefresh(); }}
              title="Refresh folders"
              aria-label="Refresh folder list"
              disabled={isRefreshing}
              className="rounded-full h-8 w-8"
            >
              <RefreshCw size={16} className={isRefreshing ? "animate-spin" : ""} />
            </Button>
          </div>
        </div>

        {showFolders && (
          <ul id="folders-list" className="space-y-0.5 pl-2" role="list">
            {renderedFolders
              .filter(mailbox => mailbox.label.toLowerCase().includes(searchQuery.toLowerCase()))
              .map((mailbox) => {
              const Icon = mailbox.icon;
              const isSelected = selectedMailbox === mailbox.name;

              return (
                <li key={mailbox.name} role="none">
                  <button
                    onClick={() => selectMailbox(mailbox.name)}
                    className={`
                      w-full flex items-center gap-3 px-3 py-2 rounded-full transition-all text-sm font-medium focus:outline-none focus:ring-2 focus:ring-ring
                      ${isSelected
                        ? 'bg-primary/20 text-primary-foreground'
                        : 'hover:bg-muted text-muted-foreground hover:text-foreground'}
                    `}
                    aria-current={isSelected ? 'page' : undefined}
                    aria-label={`Select ${mailbox.label} folder`}
                  >
                    <Icon size={18} className={isSelected ? 'text-primary' : 'text-muted-foreground'} aria-hidden="true" />
                    <span className="truncate">{mailbox.label}</span>
                    {typeof mailbox.count === 'number' && mailbox.count > 0 && (
                      <span className="ml-auto text-xs bg-muted text-muted-foreground px-2 py-0.5 rounded-full min-w-[1.5rem] text-center">
                        {mailbox.count}
                      </span>
                    )}
                  </button>
                </li>
              );
            })}
          </ul>
        )}
      </div>
    </nav>
  );
});

export default EmailListPanel;