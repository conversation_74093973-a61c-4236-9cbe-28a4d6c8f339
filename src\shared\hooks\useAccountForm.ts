/**
 * @file Hook for managing account form state and logic with improved validation.
 */
import { zodResolver } from '@hookform/resolvers/zod';
import { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { ClipboardService } from '../../services/clipboardService';
import { accountSchema, type Account } from '../types/account';
import type { DiscoveredConfig } from '../types/protocol';

import { useEmailDiscovery } from './useEmailDiscovery';

// New form schema with separate host and port for better validation and UX
const formSchema = accountSchema.omit({ id: true, incoming: true, outgoing: true, connectionStatus: true, useProxy: true, proxy: true }).extend({
  incoming: z.object({
    protocol: z.enum(['imap', 'pop3']),
    host: z.string().min(1, 'Host cannot be empty'),
    port: z.number().min(1, 'Port cannot be empty'),
    useTls: z.boolean(),
  }),
  outgoing: z.object({
    protocol: z.literal('smtp'),
    host: z.string().min(1, 'Host cannot be empty'),
    port: z.number().min(1, 'Port cannot be empty'),
    useTls: z.boolean(),
  }).optional(),
});

export type AccountFormType = z.infer<typeof formSchema>;

interface UseAccountFormProps {
  accountToEdit?: Account | null;
  initialData?: { email: string; password: string } | null;
  onSave?: (data: Omit<Account, 'id'>) => Promise<void>;
}
interface UseAccountFormReturn {
    form: ReturnType<typeof useForm<AccountFormType>>;
    isPasswordVisible: boolean;
    setIsPasswordVisible: (visible: boolean) => void;
    error: string | null;
    setError: (error: string | null) => void;
    showProviderSuggestions: boolean;
    setShowProviderSuggestions: (show: boolean) => void;
    discovery: ReturnType<typeof useEmailDiscovery>;
    handleProviderSelect: (config: DiscoveredConfig) => void;
    handleManualDiscovery: () => Promise<void>;
    handleEmailBlur: (e: React.FocusEvent<HTMLInputElement>) => Promise<void>;
    handleSubmit: (e?: React.BaseSyntheticEvent) => Promise<void>;
    parseCredentialsString: (text: string) => Promise<boolean>;
}


export const useAccountForm = (props: UseAccountFormProps): UseAccountFormReturn => {
  const { accountToEdit, initialData, onSave } = props;
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showProviderSuggestions, setShowProviderSuggestions] = useState(false);

  const discovery = useEmailDiscovery();
  const { discoverEmailSettings } = discovery;

  const form = useForm<AccountFormType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      displayName: '',
      email: '',
      password: '',
      incoming: {
        protocol: 'imap',
        host: 'imap.example.com',
        port: 993,
        useTls: true,
      },
    },
  });

  const { reset, setValue, trigger, handleSubmit: formHandleSubmit, getValues } = form;

  useEffect(() => {
    if (accountToEdit) {
      reset(accountToEdit);
      return;
    }
    if (initialData) {
      setValue('email', initialData.email, { shouldValidate: true });
      setValue('password', initialData.password, { shouldValidate: true });
      setTimeout(() => {
        void discoverEmailSettings(initialData.email, false, setValue);
      }, 500);
      return;
    }
    reset({
        displayName: '',
        email: '',
        password: '',
        incoming: { protocol: 'imap', host: '', port: 993, useTls: true },
    });
  }, [accountToEdit, initialData, reset, setValue, discoverEmailSettings]);

  const handleProviderSelect = useCallback((config: DiscoveredConfig) => {
    discovery.applyDiscoveredConfig(config, setValue);
    setShowProviderSuggestions(false);
  }, [discovery, setValue]);

  const handleManualDiscovery = useCallback(async () => {
    const email = getValues('email');
    if (!email || !/^\S+@\S+\.\S+$/.test(email)) {
      setError('Please enter a valid email to run discovery.');
      return;
    }
    setError(null);
    console.log(`Forcing manual discovery for ${email}... (bypassing all caches)`);
    // The `true` parameter is crucial to force a new DNS lookup,
    // ignoring any cached results from previous discoveries.
    await discovery.discoverEmailSettings(email, true, setValue);

    // After discovery, the form's internal state is updated via `setValue`.
    // We must now forcefully synchronize the UI with the new state.
    // `reset` is more reliable for this than `trigger`.
    const newValues = getValues();
    reset(newValues);
  }, [discovery, getValues, setValue, setError, reset]);

  const parseCredentialsString = useCallback(async (text: string): Promise<boolean> => {
    const result = ClipboardService.parseCredentialsString(text);
    if (result.success && result.credentials) {
      setValue('email', result.credentials.email, { shouldValidate: true });
      setValue('password', result.credentials.password, { shouldValidate: true });
      // Run discovery when credentials are detected from clipboard (only for new accounts)
      if (!accountToEdit && /^\S+@\S+\.\S+$/.test(result.credentials.email)) {
        console.log('Running discovery for credentials from clipboard:', result.credentials.email);
        await discovery.discoverEmailSettings(result.credentials.email, false, setValue);
      }
      return true;
    }
    return false;
  }, [setValue, discovery, accountToEdit]);

  // No automatic discovery - only manual discovery via button or clipboard detection

  const handleEmailBlur = useCallback(async (e: React.FocusEvent<HTMLInputElement>) => {
    const email = e.target.value.trim();
    // Only parse credentials from clipboard, no automatic discovery
    await parseCredentialsString(email);
  }, [parseCredentialsString]);

  const handleSubmit = useCallback(async (e?: React.BaseSyntheticEvent) => {
    if (e) e.preventDefault();

    console.log('handleSubmit called');

    return formHandleSubmit(async (data: AccountFormType) => {
      console.log('Form data:', data);
      console.log('Incoming data:', JSON.stringify(data.incoming, null, 2));
      setError(null);

      // Only run discovery for new accounts with empty/example hosts
      // Don't run discovery when editing existing accounts - respect user's manual changes
      const isNewAccount = !accountToEdit;
      const needsDiscovery = isNewAccount && (
        !data.incoming.host ||
        data.incoming.host.includes('example.com') ||
        data.incoming.host === 'imap.example.com' ||
        data.incoming.host === ''
      );

      if (needsDiscovery && /^\S+@\S+\.\S+$/.test(data.email)) {
        console.log('Running discovery for new account...');
        try {
          await discovery.discoverEmailSettings(data.email, true, setValue);

          // Use updated data if discovery was successful
          if (discovery.discoveryStatus === 'found') {
            // Trigger validation to update the form's internal state and UI
            await trigger();
            const updatedData = getValues();
            console.log('Updated data after discovery:', updatedData);
            Object.assign(data, updatedData);
          }
        } catch (discoveryError) {
          console.warn('Discovery failed, proceeding with manual settings:', discoveryError);
        }
      } else if (accountToEdit) {
        console.log('Editing existing account - using manual settings without discovery');
      }

      const finalData: Omit<Account, 'id'> = {
        ...data,
        displayName: data.displayName || data.email.split('@')[0],
      };

      console.log('Final data to save:', finalData);
      console.log('Final incoming data:', JSON.stringify(finalData.incoming, null, 2));

      try {
        await onSave?.(finalData);
        console.log('Account saved successfully');
      } catch (e: unknown) {
        console.error('Failed to save account:', e);
        const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
        setError(errorMessage);
      }
    })(e);
  }, [formHandleSubmit, onSave, discovery, setValue, getValues, accountToEdit]);

  return {
    form,
    isPasswordVisible,
    setIsPasswordVisible,
    error,
    setError,
    showProviderSuggestions,
    setShowProviderSuggestions,
    discovery,
    handleProviderSelect,
    handleManualDiscovery,
    handleEmailBlur,
    handleSubmit,
    parseCredentialsString,
  };
};