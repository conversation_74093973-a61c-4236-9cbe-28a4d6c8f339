import { Imap<PERSON><PERSON> } from 'imapflow';
import { simple<PERSON>arser } from 'mailparser';

// Proxy agents - imported but not yet implemented
// import { HttpsProxyAgent } from 'https-proxy-agent';
// import { SocksProxyAgent } from 'socks-proxy-agent';

import type { Account } from '../shared/types/account';
import type { <PERSON><PERSON><PERSON>eader } from '../shared/types/email';


import { validateMailbox, calculateMessageRange, createEmailHeader } from './utils/emailProcessing';
import { logImapError, createImapConfig, configureProxy } from './utils/imapErrorHandling';

// Interface for IMAP mailbox object
interface ImapMailboxInfo {
  exists: number;
  recent: number;
  flags: Set<string>;
  permanentFlags: Set<string>;
  uidValidity: number;
  uidNext: number;
}

/**
 * @file Service for handling IMAP connections and operations using ImapFlow.
 */

/**
 * Connects to an IMAP server using the provided account details.
 * @param {Account} account - The account to connect with.
 * @param {(message: string, level?: 'info' | 'error' | 'success') => void} logCallback - Optional callback for logging.
 * @returns {Promise<ImapFlow>} A promise that resolves with the connected ImapFlow instance.
 */
export function connectToAccount(
  account: Account,
  logCallback: (message: string, level?: 'info' | 'error' | 'success') => void = () => {}
): Promise<{ imap: ImapFlow; proxyUsed: boolean }> {
  return new Promise((resolve, reject) => {
    // We can only connect if the configured protocol is IMAP.
    if (account.incoming.protocol !== 'imap') {
      return reject(new Error(`Account is configured for ${account.incoming.protocol}, not IMAP.`));
    }

    // Proxy configuration
    const { proxy, proxyUsed } = configureProxy(account, logCallback);

    const imap = new ImapFlow(createImapConfig(account, proxy));

    // Настройка обработчиков событий
    imap.on('error', (err) => {
      logImapError(err, account.email, account.incoming.host, logCallback);
      reject(err);
    });

    // Подключаемся
    // eslint-disable-next-line no-console
    console.log(`Attempting to connect to ${account.incoming.host} for ${account.email}...`);
    imap.connect()
      .then(() => {
        // eslint-disable-next-line no-console
        console.log(`IMAP connection ready for ${account.email}`);
        resolve({ imap, proxyUsed });
      })
      .catch(reject);
  });
}

/**
 * Fetches the list of mailboxes from a connected IMAP instance.
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @returns {Promise<Array>} A promise that resolves with the mailboxes.
 */
export async function getMailboxes(imap: ImapFlow): Promise<unknown[]> {
  const list = await imap.list();
  return list;
}

/**
 * Fetches a paginated list of email headers from a mailbox.
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @param {string} mailboxName - The name of the mailbox to fetch from.
 * @param {number} offset - The number of emails to skip from the newest.
 * @param {number} limit - The maximum number of emails to fetch.
 * @returns {Promise<EmailHeader[]>} A promise that resolves with an array of email headers.
 */
export async function fetchEmails(imap: ImapFlow, mailboxName: string, offset: number, limit: number): Promise<EmailHeader[]> {
  // Открываем почтовый ящик
  const lock = await imap.getMailboxLock(mailboxName);
  try {
    validateMailbox(imap);

    const totalMessages = (imap.mailbox as unknown as ImapMailboxInfo).exists ?? 0;
    const { start, end } = calculateMessageRange(totalMessages, offset, limit);

    if (start > end) {
      return [];
    }

    const headers: EmailHeader[] = [];
    for await (const message of imap.fetch(`${start}:${end}`, { envelope: true, flags: true })) {
      if (message.envelope && message.flags) {
        // Адаптируем FetchMessageObject к ожидаемому типу
        const adaptedMessage = {
          uid: message.uid,
          envelope: {
            from: message.envelope.from,
            subject: message.envelope.subject,
            date: message.envelope.date
          },
          flags: message.flags
        };
        headers.push(createEmailHeader(adaptedMessage));
      }
    }

    return headers.reverse(); // Реверс для показа новых первыми
  } finally {
    lock.release();
  }
}

/**
 * Fetches the full body of a specific email.
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @param {string} mailboxName - The name of the mailbox.
 * @param {number} uid - The UID of the email to fetch.
 * @returns {Promise<any>} A promise that resolves with the parsed email body.
 */
export async function fetchEmailBody(
  imap: ImapFlow,
  mailboxName: string,
  uid: number,
  logCallback: (message: string, level?: 'info' | 'error' | 'success') => void = () => {}
): Promise<unknown> {
  const lock = await imap.getMailboxLock(mailboxName);
  try {
    logCallback(`UID ${uid}: Fetching metadata...`);
    const metadataPromise = imap.fetchOne(String(uid), { envelope: true, flags: true }, { uid: true });

    logCallback(`UID ${uid}: Downloading content...`);
    const { content } = await imap.download(String(uid), undefined, { uid: true });
    
    if (content === null || content === undefined) {
      throw new Error(`Could not download content for message UID ${uid}`);
    }

    logCallback(`UID ${uid}: Parsing content and waiting for metadata...`);
    const [parsed, messageMeta] = await Promise.all([
      simpleParser(content),
      metadataPromise
    ]);

    if (messageMeta === null || messageMeta === undefined) {
      throw new Error(`Could not fetch metadata for message UID ${uid}`);
    }

    logCallback(`UID ${uid}: Assembling final email object...`);
    const finalDate = parsed.date ?? messageMeta.envelope?.date;

    return {
      ...parsed,
      date: finalDate ? finalDate.toISOString() : new Date().toISOString(),
      flags: Array.from(messageMeta.flags ?? []),
      uid: messageMeta.uid,
    };
  } finally {
    lock.release();
  }
}

/**
 * Marks a specific email for deletion.
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @param {string} mailboxName - The name of the mailbox.
 * @param {number} uid - The UID of the email to delete.
 * @returns {Promise<void>} A promise that resolves when the email is marked as deleted.
 */
export async function deleteEmail(imap: ImapFlow, mailboxName: string, uid: number): Promise<void> {
  const lock = await imap.getMailboxLock(mailboxName, { readonly: false });
  try {
    await imap.messageDelete(String(uid), { uid: true });
  } finally {
    lock.release();
  }
}

/**
 * Marks an email as read (seen).
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @param {string} mailboxName - The name of the mailbox.
 * @param {number} uid - The UID of the email.
 * @returns {Promise<void>}
 */
export async function markAsSeen(imap: ImapFlow, mailboxName: string, uid: number): Promise<void> {
  const lock = await imap.getMailboxLock(mailboxName, { readonly: false });
  try {
    await imap.messageFlagsAdd(String(uid), ['\\Seen'], { uid: true });
  } finally {
    lock.release();
  }
}

/**
 * Marks an email as unread (unseen).
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @param {string} mailboxName - The name of the mailbox.
 * @param {number} uid - The UID of the email.
 * @returns {Promise<void>}
 */
export async function markAsUnseen(imap: ImapFlow, mailboxName: string, uid: number): Promise<void> {
  const lock = await imap.getMailboxLock(mailboxName, { readonly: false });
  try {
    await imap.messageFlagsRemove(String(uid), ['\\Seen'], { uid: true });
  } finally {
    lock.release();
  }
}

/**
 * Marks multiple emails for deletion.
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @param {string} mailboxName - The name of the mailbox.
 * @param {number[]} uids - Array of UIDs to delete.
 * @returns {Promise<void>}
 */
export async function deleteEmails(imap: ImapFlow, mailboxName: string, uids: number[]): Promise<void> {
  if (uids.length === 0) return;
  
  const lock = await imap.getMailboxLock(mailboxName, { readonly: false });
  try {
    // ImapFlow принимает строку или массив строк для UID
    const uidString = uids.map(uid => String(uid)).join(',');
    await imap.messageDelete(uidString, { uid: true });
  } finally {
    lock.release();
  }
} 