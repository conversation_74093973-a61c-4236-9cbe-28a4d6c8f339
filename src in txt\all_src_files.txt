// FILE: components.json

{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "new-york",
  "rsc": false,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.js",
    "css": "src/index.css",
    "baseColor": "neutral",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils",
    "ui": "@/components/ui",
    "lib": "@/lib",
    "hooks": "@/hooks"
  },
  "iconLibrary": "lucide"
}


---

// FILE: package.json

{
  "name": "imapviewer",
  "productName": "imapviewer",
  "version": "1.0.0",
  "description": "My Electron application description",
  "main": ".vite/build/main.js",
  "scripts": {
    "start": "electron-forge start",
    "package": "electron-forge package",
    "make": "electron-forge make",
    "make:debug": "cross-env DEBUG=electron-forge:*,electron-packager,appx,make-squirrel electron-forge make --verbose",
    "publish": "electron-forge publish",
    "lint": "eslint --ext .ts,.tsx ."
  },
  "keywords": [],
  "author": "Comp",
  "license": "MIT",
  "devDependencies": {
    "@electron-forge/cli": "^7.8.1",
    "@electron-forge/maker-deb": "^7.8.1",
    "@electron-forge/maker-rpm": "^7.8.1",
    "@electron-forge/maker-squirrel": "^7.8.1",
    "@electron-forge/maker-zip": "^7.8.1",
    "@electron-forge/plugin-auto-unpack-natives": "^7.8.1",
    "@electron-forge/plugin-fuses": "^7.8.1",
    "@electron-forge/plugin-vite": "^7.8.1",
    "@electron/fuses": "^1.8.0",
    "@tailwindcss/forms": "^0.5.10",
    "@tailwindcss/postcss": "^4.1.10",
    "@tailwindcss/typography": "^0.5.16",
    "@tailwindcss/vite": "^4.1.10",
    "@types/dompurify": "^3.0.5",
    "@types/fs-extra": "^11.0.4",
    "@types/imapflow": "^1.0.22",
    "@types/mailparser": "^3.4.6",
    "@types/node": "^24.0.3",
    "@types/react": "^18.2.73",
    "@types/react-dom": "^18.2.22",
    "@types/uuid": "^10.0.0",
    "@typescript-eslint/eslint-plugin": "^7.18.0",
    "@typescript-eslint/parser": "^7.18.0",
    "autoprefixer": "^10.4.16",
    "cross-env": "^7.0.3",
    "electron": "^31.0.1",
    "electron-vite": "^3.1.0",
    "eslint": "^9.8.0",
    "eslint-plugin-import": "^2.32.0",
    "eslint-plugin-react": "^7.37.5",
    "eslint-plugin-react-hooks": "^5.2.0",
    "fs-extra": "^11.3.0",
    "postcss": "^8.4.31",
    "tailwindcss": "^4.0.0-alpha",
    "ts-node": "^10.9.2",
    "typescript": "^5.8.3",
    "vite": "^6.3.5"
  },
  "dependencies": {
    "@headlessui/react": "^2.2.4",
    "@hookform/resolvers": "^5.1.1",
    "@radix-ui/react-avatar": "^1.1.10",
    "@radix-ui/react-checkbox": "^1.3.2",
    "@radix-ui/react-dialog": "^1.1.14",
    "@radix-ui/react-label": "^2.1.7",
    "@radix-ui/react-progress": "^1.1.7",
    "@radix-ui/react-select": "^2.2.5",
    "@radix-ui/react-slot": "^1.2.3",
    "@radix-ui/react-switch": "^1.2.5",
    "@radix-ui/react-tabs": "^1.1.12",
    "@radix-ui/react-toggle": "^1.1.9",
    "@radix-ui/react-toggle-group": "^1.1.10",
    "@radix-ui/react-tooltip": "^1.2.7",
    "@vitejs/plugin-react": "^4.5.2",
    "axios": "^1.10.0",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "dompurify": "^3.2.6",
    "electron-squirrel-startup": "^1.0.1",
    "electron-store": "^10.1.0",
    "fast-xml-parser": "^5.2.5",
    "framer-motion": "^12.23.6",
    "https-proxy-agent": "^7.0.6",
    "imapflow": "^1.0.188",
    "lucide-react": "^0.516.0",
    "mailparser": "^3.7.3",
    "next-themes": "^0.4.6",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-hook-form": "^7.58.1",
    "react-resizable-panels": "^3.0.3",
    "socks": "^2.8.5",
    "socks-proxy-agent": "^8.0.5",
    "sonner": "^2.0.6",
    "tailwind-merge": "^3.3.1",
    "uuid": "^11.1.0",
    "zod": "^3.25.67",
    "zustand": "^5.0.5"
  },
  "overrides": {
    "glob": "^10.4.5",
    "rimraf": "^6.0.1",
    "eslint": "^9.8.0",
    "@npmcli/move-file": "^3.0.0",
    "xterm": "npm:@xterm/xterm@^5.5.0",
    "xterm-addon-fit": "npm:@xterm/addon-fit@^0.10.0",
    "xterm-addon-search": "npm:@xterm/addon-search@^0.15.0"
  }
}


---

// FILE: src\app.tsx

import React, { useEffect } from 'react';
import { Toaster } from 'sonner';

import ErrorBoundary from './components/ErrorBoundary';
import Layout from './components/Layout';
import { useAppInitialization } from './shared/hooks/useAppInitialization';
import { useAccountStore } from './shared/store/accountStore';
import { useLogStore } from './shared/store/logStore';
import { ThemeProvider } from './shared/ui/theme-provider';



const App = (): React.JSX.Element => {
  const { addLog } = useLogStore();
  const { setAccountConnectionStatus } = useAccountStore();

  // Initialize the application (load accounts, proxy settings, etc.)
  useAppInitialization();

  useEffect(() => {
    try {
      // Listen for log messages from the main process
      window.ipcApi.on('log:add', ({ level, message }: { level: 'info' | 'success' | 'error', message: string }) => {
        addLog(message, level);
      });

      // Listen for account connection status updates
      window.ipcApi.on('account:connection-status', ({ accountId, status }: { accountId: string, status: 'connected' | 'connecting' | 'disconnected' }) => {
        setAccountConnectionStatus(accountId, status);
      });

      // Notify the main process that the renderer is ready
      void window.ipcApi.rendererReady();
    } catch (error) {
      addLog(`Failed to initialize application: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
    }
  }, [addLog, setAccountConnectionStatus]);

  return (
    <ThemeProvider defaultTheme="dark" storageKey="imapviewer-ui-theme">
      <ErrorBoundary>
        <Layout />
        <Toaster
          position="top-right"
          richColors
          closeButton
          toastOptions={{
            style: {
              background: 'hsl(var(--background))',
              border: '1px solid hsl(var(--border))',
              color: 'hsl(var(--foreground))',
            },
          }}
        />
      </ErrorBoundary>
    </ThemeProvider>
  );
};

export default App;

---

// FILE: src\components\AccountManager\AccountContextMenu.tsx

/**
 * @file Context menu component for account actions
 */
import { Edit, Trash2, Copy } from 'lucide-react';
import React from 'react';

import type { Account } from '../../shared/types/account';
import { Button } from '../../shared/ui/button';

interface AccountContextMenuProps {
  position: { x: number; y: number };
  account: Account;
  onEdit: (_account: Account) => void;
  onDelete: (_accountId: string) => void;
  onCopy: (_account: Account) => void;
  onClose: () => void;
}

/**
 * Context menu for account actions
 */
const AccountContextMenu: React.FC<AccountContextMenuProps> = ({
  position,
  account,
  onEdit,
  onDelete,
  onCopy,
  onClose,
}): React.JSX.Element => {
  React.useEffect(() => {
    const handleClickOutside = (): void => {
      onClose();
    };

    const handleEscape = (e: KeyboardEvent): void => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleEscape);

    return (): void => {
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  return (
    <div
      className="fixed z-50 bg-popover border border-border rounded-md shadow-lg py-1 min-w-[160px]"
      style={{
        left: position.x,
        top: position.y,
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <Button
        variant="ghost"
        size="sm"
        className="w-full justify-start gap-2 px-3 py-2 h-auto font-normal"
        onClick={() => {
          onEdit(account);
          onClose();
        }}
      >
        <Edit size={14} />
        Edit Account
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        className="w-full justify-start gap-2 px-3 py-2 h-auto font-normal"
        onClick={() => {
          onCopy(account);
          onClose();
        }}
      >
        <Copy size={14} />
        Copy Credentials
      </Button>
      
      <div className="border-t border-border my-1" />
      
      <Button
        variant="ghost"
        size="sm"
        className="w-full justify-start gap-2 px-3 py-2 h-auto font-normal text-red-400 hover:bg-red-500/20"
        onClick={() => {
          onDelete(account.id);
          onClose();
        }}
      >
        <Trash2 size={14} />
        Delete Account
      </Button>
    </div>
  );
};

export default AccountContextMenu;


---

// FILE: src\components\AccountManager\AccountForm.tsx

/**
 * @file Refactored account form component for a better user experience.
 */

import { ArrowLeft, Save, X, RefreshCw, Eye, EyeOff, AlertCircle, Settings } from 'lucide-react';
import React, { useEffect } from 'react';
import { Controller } from 'react-hook-form';

import { useAccountForm } from '../../shared/hooks/useAccountForm';
import { imapProviders } from '../../shared/store/imapProviders';
import type { Account } from '../../shared/types/account';
import { Button } from '../../shared/ui/button';
import { Input } from '../../shared/ui/input';
import { Label } from '../../shared/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../shared/ui/select';
import { ToggleGroup, ToggleGroupItem } from '../../shared/ui/toggle-group';
import { cn } from '../../shared/utils/utils';

// ... (interface definition remains the same)
interface AccountFormProps {
    accountToEdit?: Account | null;
    onCancel: () => void;
    onSuccess: (_data: Omit<Account, 'id'>) => Promise<void>;
    initialData?: {
      email: string;
      password: string;
    } | null;
}

const AccountForm: React.FC<AccountFormProps> = ({ accountToEdit, onCancel, onSuccess, initialData }) => {
  const {
    form,
    isPasswordVisible,
    setIsPasswordVisible,
    error,
    showProviderSuggestions,
    setShowProviderSuggestions,
    discovery,
    handleProviderSelect,
    handleManualDiscovery,
    handleEmailBlur,
    handleSubmit,
  } = useAccountForm({ accountToEdit, initialData, onSave: onSuccess });

  const { register, formState: { errors, isSubmitting }, control, watch, setValue } = form;
  const watchedEmail = watch('email');

  useEffect(() => {
    if (discovery.discoveryStatus === 'failed') {
      setShowProviderSuggestions(true);
    } else {
      setShowProviderSuggestions(false);
    }
  }, [discovery.discoveryStatus, setShowProviderSuggestions]);

  return (
    <div className="h-full flex flex-col bg-background text-foreground w-full">
      {/* Header */}
      <div className="flex items-center gap-3 px-4 border-b border-border flex-shrink-0 h-14">
        <Button variant="ghost" size="icon" onClick={onCancel} className="rounded-full">
          <ArrowLeft size={20} />
        </Button>
        <h2 className="text-xl font-semibold truncate">
          {accountToEdit ? 'Edit Account' : 'Add New Account'}
        </h2>
      </div>

      {/* Form */}
      <div className="flex-grow overflow-y-auto p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Credentials */}
          <div className="space-y-4">
            <FormField id="email" label="Email Address" error={errors.email?.message}>
              <Input {...register('email')} type="email" placeholder="<EMAIL>" onBlur={handleEmailBlur} />
            </FormField>
            <FormField id="password" label="Password" error={errors.password?.message}>
              <div className="relative">
                <Input {...register('password')} type={isPasswordVisible ? 'text' : 'password'} placeholder="••••••••" />
                <Button type="button" variant="ghost" size="icon" className="absolute right-2 top-1/2 -translate-y-1/2 h-7 w-7" onClick={() => setIsPasswordVisible(!isPasswordVisible)}>
                  {isPasswordVisible ? <EyeOff size={16} /> : <Eye size={16} />}
                </Button>
              </div>
            </FormField>
          </div>

          {/* Discovery Status & Manual Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Server Settings</h3>
                <Button type="button" variant="ghost" size="sm" onClick={handleManualDiscovery} disabled={discovery.isDiscovering || !watchedEmail}>
                    <RefreshCw size={14} className={cn(discovery.isDiscovering && 'animate-spin', 'mr-2')} />
                    Auto-Discover
                </Button>
            </div>

            {discovery.isDiscovering && (
                <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>Discovering email settings...</span>
                        <span className="text-xs">This may take up to 60 seconds</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{width: '100%'}}></div>
                    </div>
                </div>
            )}

            {discovery.discoveryStatus === 'failed' && (
                <div className="p-3 rounded-lg bg-yellow-50 border border-yellow-200 text-yellow-800">
                    <p className="text-sm">{discovery.discoveryMessage}</p>
                    <p className="text-xs mt-1">Please select a provider or enter settings manually.</p>
                </div>
            )}

            {showProviderSuggestions && (
                <Select onValueChange={(name) => handleProviderSelect(imapProviders.find(p => p.name === name)!.config)}>
                    <SelectTrigger><SelectValue placeholder="Select a common provider..." /></SelectTrigger>
                    <SelectContent>{imapProviders.map(p => <SelectItem key={p.name} value={p.name}>{p.name}</SelectItem>)}</SelectContent>
                </Select>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField id="incoming.protocol" label="Protocol">
                    <Controller control={control} name="incoming.protocol" render={({ field }) => (
                        <ToggleGroup type="single" value={field.value} onValueChange={field.onChange} variant="outline" className="w-full">
                            <ToggleGroupItem value="imap" className="flex-1">IMAP</ToggleGroupItem>
                            <ToggleGroupItem value="pop3" className="flex-1">POP3</ToggleGroupItem>
                        </ToggleGroup>
                    )} />
                </FormField>
                <FormField id="incoming.useTls" label="Security">
                    <Controller control={control} name="incoming.useTls" render={({ field }) => (
                        <Button type="button" onClick={() => field.onChange(!field.value)} variant={field.value ? 'secondary' : 'outline'} className="w-full">
                            {field.value ? 'SSL/TLS Enabled' : 'No SSL/TLS'}
                        </Button>
                    )} />
                </FormField>
            </div>
            <div className="grid grid-cols-3 gap-4">
                <div className="col-span-2">
                    <FormField id="incoming.host" label="Server Host" error={errors.incoming?.host?.message}>
                        <Input {...register('incoming.host')} placeholder="imap.example.com" />
                    </FormField>
                </div>
                <div>
                    <FormField id="incoming.port" label="Port" error={errors.incoming?.port?.message}>
                        <Input {...register('incoming.port', { valueAsNumber: true })} type="number" placeholder="993" />
                    </FormField>
                </div>
            </div>
          </div>

          {error && (
            <div className="p-3 rounded-lg bg-destructive/10 border border-destructive/20 text-destructive-foreground">
              <AlertCircle size={18} className="inline mr-2" /> {error}
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center gap-4 pt-4 border-t border-border">
            <Button type="submit" disabled={isSubmitting} className="flex-1">
              {isSubmitting ? <RefreshCw size={18} className="animate-spin" /> : <Save size={18} />}<span className="ml-2">{accountToEdit ? 'Save Changes' : 'Add Account'}</span>
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>Cancel</Button>
          </div>
        </form>
      </div>
    </div>
  );
};

// A simple reusable form field component to reduce repetition
const FormField: React.FC<{ id: string, label: string, error?: string, children: React.ReactNode }> = ({ id, label, error, children }) => (
    <div className="space-y-2">
        <Label htmlFor={id}>{label}</Label>
        {children}
        {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
);

export default AccountForm;

---

// FILE: src\components\AccountManager\AccountItem.tsx

/**
 * @file Individual account item component for the account manager
 */
import { Edit, Trash2, Copy, AlertCircle, Undo2 } from 'lucide-react';
import React from 'react';

import { useAccountStore } from '../../shared/store/accountStore';
import { useMainSettingsStore } from '../../shared/store/mainSettingsStore';
import type { Account } from '../../shared/types/account';
import { Avatar, AvatarFallback } from '../../shared/ui';
import { Button } from '../../shared/ui/button';
import { Card, CardContent } from '../../shared/ui/card';
import { cn } from '../../shared/utils/utils';

interface AccountItemProps {
  account: Account;
  isSelected: boolean;
  isRecentlyDeleted: boolean;
  onSelect: (_accountId: string) => void;
  onEdit: (_account: Account) => void;
  onDelete: (_accountId: string) => void;
  onUndo: (_accountId: string) => void;
  onCopy: (_account: Account) => void;
  onContextMenu: (_e: React.MouseEvent, _accountId: string) => void;
}

// Helper functions
const getInitials = (email: string): string => {
  return email.substring(0, 2).toUpperCase();
};

const getConnectionStatus = (status: string): { color: string; label: string } => {
  switch (status) {
    case 'connected':
      return { color: 'bg-green-500', label: 'Connected' };
    case 'connecting':
      return { color: 'bg-yellow-500', label: 'Connecting' };
    case 'error':
      return { color: 'bg-red-500', label: 'Error' };
    default:
      return { color: 'bg-gray-500', label: 'Disconnected' };
  }
};

// Deleted account component
const DeletedAccountItem: React.FC<{
  account: Account;
  onUndo: (_accountId: string) => void;
}> = ({ account, onUndo }): React.JSX.Element => (
  <Card className="border-red-500/50 bg-red-500/10">
    <CardContent className="p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <AlertCircle className="h-5 w-5 text-red-500" />
          <div>
            <p className="font-medium text-red-400">Account Deleted</p>
            <p className="text-sm text-red-300">{account.email}</p>
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onUndo(account.id)}
          className="gap-2 border-red-500/50 text-red-400 hover:bg-red-500/20"
        >
          <Undo2 size={14} />
          Undo
        </Button>
      </div>
    </CardContent>
  </Card>
);

/**
 * Individual account item component
 */
const AccountItem: React.FC<AccountItemProps> = ({
  account,
  isSelected,
  isRecentlyDeleted,
  onSelect,
  onEdit,
  onDelete,
  onUndo,
  onCopy,
  onContextMenu,
}): React.JSX.Element => {
  const { settings } = useMainSettingsStore();
  const { accounts } = useAccountStore();
  const status = getConnectionStatus(account.connectionStatus ?? 'disconnected');

  if (isRecentlyDeleted) {
    return <DeletedAccountItem account={account} onUndo={onUndo} />;
  }

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md",
        isSelected
          ? "border-primary bg-primary/10 shadow-lg"
          : "border-border hover:border-primary/50"
      )}
      onClick={() => onSelect(account.id)}
      onContextMenu={(e) => onContextMenu(e, account.id)}
    >
      <CardContent className={cn("p-4", settings.compactAccountView === true && "py-2 px-3")}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            {settings.compactAccountView !== true ? (
              <Avatar className="h-10 w-10">
                <AvatarFallback className="bg-primary/20 text-primary font-medium">
                  {getInitials(account.email)}
                </AvatarFallback>
              </Avatar>
            ) : (
              <div className="flex items-center gap-1.5">
                <span
                  className={cn(
                    "text-xs font-mono w-5 h-5 rounded-full flex items-center justify-center",
                    status.color === "bg-green-500" ? "bg-green-500/20 text-green-400" :
                    status.color === "bg-yellow-500" ? "bg-yellow-500/20 text-yellow-400" :
                    "bg-red-500/20 text-red-400"
                  )}
                  title={`Account ${accounts.findIndex((a: Account) => a.id === account.id) + 1} - ${status.label}`}
                >
                  {accounts.findIndex((a: Account) => a.id === account.id) + 1}
                </span>
              </div>
            )}

            <div className="min-w-0 flex-1">
              {settings.compactAccountView === true ? (
                <p className="text-foreground truncate">
                  {account.displayName ?? account.email}
                </p>
              ) : (
                <>
                  <div className="flex items-center gap-2">
                    <p className="font-medium text-foreground truncate">
                      {account.displayName ?? account.email}
                    </p>
                    <div
                      className={cn("w-2 h-2 rounded-full", status.color)}
                      title={status.label}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground truncate">
                    {account.email}
                  </p>
                </>
              )}
            </div>
          </div>

          <div className="flex items-center gap-1 ml-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onEdit(account);
              }}
              className="h-8 w-8 p-0 hover:bg-primary/20"
              title="Edit account"
            >
              <Edit size={14} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onCopy(account);
              }}
              className="h-8 w-8 p-0 hover:bg-primary/20"
              title="Copy credentials"
            >
              <Copy size={14} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(account.id);
              }}
              className="h-8 w-8 p-0 hover:bg-red-500/20 text-red-400"
              title="Delete account"
            >
              <Trash2 size={14} />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AccountItem;


---

// FILE: src\components\AccountManager\AccountManagerPanel.tsx

/**
 * @file Panel for managing email accounts with a modern dark design
 */
import { PlusCircle, Upload, X, Copy, Edit, Trash2, AlertCircle, Undo2 } from 'lucide-react';
import React from 'react';

import { useAccountManager } from '../../shared/hooks/useAccountManager';
import { useAccountStore } from '../../shared/store/accountStore';
import { useMainSettingsStore } from '../../shared/store/mainSettingsStore';
import { Avatar, AvatarFallback } from '../../shared/ui';
import { Button } from '../../shared/ui/button';
import { Card, CardContent } from '../../shared/ui/card';
import { cn } from '../../shared/utils/utils';
import { ImportDialog } from '../ImportDialog';

import AccountForm from './AccountForm';

const AccountManagerPanel: React.FC<{
  collapsed: boolean;
}> = React.memo(({ collapsed }) => {
  const [isDragOver, setIsDragOver] = React.useState(false);
  const [recentlyDeletedId, setRecentlyDeletedId] = React.useState<string | null>(null);
  const [contextMenu, setContextMenu] = React.useState<{
    x: number;
    y: number;
    accountId: string;
  } | null>(null);

  const {
    accounts,
    selectedAccountId,
    selectAccount,
  } = useAccountStore();

  const { settings } = useMainSettingsStore();

  const {
    view,
    editingAccount,
    error,
    prefillData,
    handleSave,
    handleAddNew,
    handleEdit,
    handleDelete,
    handleCancel,
    handleCopyCredentials,
    handleImport,
    isImportDialogOpen,
    setIsImportDialogOpen,
    handleImportComplete,
    deletedAccounts,
    handleUndoDelete,
    handleDismissUndo,
  } = useAccountManager();

  // Drag and drop handlers
  const handleDragOver = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = React.useCallback((e: React.DragEvent): void => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const textFile = files.find(file =>
      file.type === 'text/plain' ||
      file.name.endsWith('.txt') ||
      file.name.endsWith('.csv')
    );

    if (textFile) {
      void (async (): Promise<void> => {
        try {
          // Read file content using FileReader
          const fileContent = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e): void => resolve(e.target?.result as string);
            reader.onerror = reject;
            reader.readAsText(textFile);
          });

          // Create a temporary file and import
          const result = await window.ipcApi.importFromFileContent(fileContent);
          if (typeof result.error === 'undefined' || result.error === null) {
            handleImportComplete(result);
          }
        } catch {
          // Handle error silently or use proper error reporting
        }
      })();
    }
  }, [handleImportComplete]);

  // Context menu handlers
  const handleContextMenu = React.useCallback((e: React.MouseEvent, accountId: string) => {
    e.preventDefault();
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      accountId
    });
  }, []);

  const closeContextMenu = React.useCallback(() => {
    setContextMenu(null);
  }, []);

  // Close context menu on click outside
  React.useEffect(() => {
    const handleClickOutside = (): void => closeContextMenu();
    if (contextMenu) {
      document.addEventListener('click', handleClickOutside);
      return (): void => { document.removeEventListener('click', handleClickOutside); };
    }
  }, [contextMenu, closeContextMenu]);

  // Enhanced delete handler with hover protection
  const handleDeleteWithProtection = React.useCallback(async (accountId: string) => {
    // Mark as recently deleted to prevent hover on next item
    setRecentlyDeletedId(accountId);

    // Clear the protection after a longer delay for better UX
    setTimeout(() => {
      setRecentlyDeletedId(null);
    }, 1000);

    await handleDelete(accountId);
  }, [handleDelete]);

  // Collapsed sidebar view
  if (collapsed) {
    return (
      <aside
        className={`bg-[#121212] h-full flex flex-col items-center py-3 w-full transition-colors ${
          isDragOver ? 'bg-[#1a1a1a] border-r-2 border-primary' : ''
        }`}
        aria-label="Account manager (collapsed)"
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center gap-4 w-full">
          <nav className={cn(
            "flex flex-col items-center w-full px-2",
            settings.compactAccountView === true ? "gap-2" : "gap-3"
          )} aria-label="Account list">
            {accounts.map((acc, index) => (
              <button
                key={acc.id}
                onClick={() => selectAccount(acc.id)}
                title={`${(acc.displayName?.length ?? 0) > 0 ? acc.displayName : acc.email} - ${acc.connectionStatus ?? 'disconnected'}`}
                className={cn(
                  "flex items-center justify-center text-xs font-mono focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all",
                  settings.compactAccountView === true ? "w-8 h-6 rounded-lg" : "w-8 h-8 rounded-full",
                  selectedAccountId === acc.id ? "ring-2 ring-primary" : "",
                  acc.connectionStatus === 'connected' ? "bg-green-500/20 text-green-400" :
                  acc.connectionStatus === 'connecting' ? "bg-yellow-500/20 text-yellow-400" :
                  "bg-red-500/20 text-red-400"
                )}
                aria-label={`Select account ${(acc.displayName?.length ?? 0) > 0 ? acc.displayName : acc.email}`}
              >
                {index + 1}
              </button>
            ))}
          </nav>
        </div>

        <div className="mt-auto border-t border-gray-800/50 pt-2 w-full">
          <div className="flex flex-col items-center gap-2 px-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => { void handleAddNew(); }}
              title="Add account"
              aria-label="Add new account"
              className="rounded-full w-8 h-8"
            >
              <PlusCircle size={16} />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => { void handleImport(); }}
              title={isDragOver ? "Drop file to import" : "Import account list"}
              aria-label="Import account list"
              className={`rounded-full w-8 h-8 transition-colors ${
                isDragOver ? 'bg-primary/20 text-primary' : ''
              }`}
            >
              <Upload size={16} />
            </Button>
          </div>
        </div>
      </aside>
    );
  }
  
  // Expanded view with account list or form
  return (
    <aside
      className={`h-full flex flex-col w-full border-l border-border transition-colors ${
        isDragOver ? 'bg-primary/5 border-l-primary' : ''
      }`}
      aria-label="Account manager"
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {view === 'form' ? (
        <AccountForm
          accountToEdit={editingAccount}
          onCancel={handleCancel}
          onSuccess={handleSave}
          initialData={prefillData}
        />
      ) : (
        <div className="h-full flex flex-col">
          {/* Account list */}
          <div className="flex-grow overflow-y-auto custom-scrollbar p-2 space-y-0.5">
            {accounts.map((acc, index) => {
              const displayLabel = acc.displayName ?? acc.email;
              const isSelected = selectedAccountId === acc.id;

              const isDefaultName = /^Account \d+ \w+$/.test(acc.displayName ?? '');
              const avatarName = isDefaultName ? acc.email : acc.displayName;
              
              return (
                <div
                  key={acc.id}
                  className={`
                    relative rounded-lg transition-all group overflow-hidden
                    ${isSelected ? 'bg-blue-900/30' : 'hover:bg-white/5'}
                  `}
                  onDoubleClick={() => handleEdit(acc)}
                  onContextMenu={(e) => handleContextMenu(e, acc.id)}
                >
                  <div className={cn("flex items-center", settings.compactAccountView === true ? "p-1.5" : "p-2")}>
                    <button
                      className="flex items-center flex-grow gap-3 min-w-0 text-left"
                      onClick={() => selectAccount(acc.id)}
                      title={acc.email}
                    >
                      {settings.compactAccountView === true ? (
                        <div className="flex items-center gap-2 min-w-0 flex-1">
                          <span
                            className={cn(
                              "text-xs font-mono w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0",
                              acc.connectionStatus === 'connected' ? "bg-green-500/20 text-green-400" :
                              acc.connectionStatus === 'connecting' ? "bg-yellow-500/20 text-yellow-400" :
                              "bg-red-500/20 text-red-400"
                            )}
                            title={`Account ${index + 1} - ${acc.connectionStatus ?? 'disconnected'}`}
                          >
                            {index + 1}
                          </span>
                          <span className="text-sm truncate">
                            {acc.displayName ?? acc.email}
                          </span>
                        </div>
                      ) : (
                        <>
                          <div className="relative">
                            <Avatar className={cn(
                              "w-10 h-10",
                              isSelected && "ring-2 ring-primary"
                            )}>
                              <AvatarFallback className={cn(
                                "font-medium text-sm",
                                isSelected ? "bg-primary text-primary-foreground" : "bg-muted"
                              )}>
                                {(avatarName?.length ?? 0) > 0 ?
                                  (avatarName ?? '').split(' ').map(part => part[0]).slice(0, 2).join('').toUpperCase() :
                                  acc.email[0]?.toUpperCase() ?? '?'
                                }
                              </AvatarFallback>
                            </Avatar>

                            {/* Index number */}
                            <div className="absolute -top-1 -left-1 rounded-full border-2 border-background bg-muted text-muted-foreground text-xs font-bold flex items-center justify-center w-5 h-5">
                              {index + 1}
                            </div>

                            {/* Status indicator */}
                            {acc.connectionStatus && (
                              <div className={cn(
                                "absolute -bottom-1 -right-1 rounded-full border-2 border-background w-3 h-3",
                                acc.connectionStatus === 'connected' && 'bg-green-500',
                                acc.connectionStatus === 'connecting' && 'bg-yellow-500',
                                acc.connectionStatus === 'disconnected' && 'bg-red-500'
                              )} />
                            )}
                          </div>

                          <div className="flex-grow min-w-0 overflow-hidden">
                            <p className={`truncate text-sm ${isSelected ? 'text-blue-200 font-medium' : 'text-gray-200'}`}>
                              {displayLabel}
                            </p>
                            <p className="truncate text-xs text-gray-400">{acc.email}</p>
                          </div>
                        </>
                      )}
                    </button>
                  </div>
                    
                  <div className={cn(
                    "absolute inset-y-0 right-0 flex items-center opacity-0 transition-opacity duration-75",
                    recentlyDeletedId !== acc.id && "group-hover:opacity-100"
                  )}>
                    <div className="flex h-full items-center gap-0.5 bg-card/95 backdrop-blur-sm px-2 py-1 rounded-l-full border-l border-t border-b border-border shadow-lg">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 rounded-full hover:bg-accent/50"
                        onClick={() => { void handleCopyCredentials(acc); }}
                        title="Copy credentials"
                      >
                        <Copy size={12} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 rounded-full hover:bg-accent/50"
                        onClick={() => handleEdit(acc)}
                        title="Edit account"
                      >
                        <Edit size={12} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 rounded-full text-destructive hover:text-destructive hover:bg-destructive/10"
                        onClick={() => { void handleDeleteWithProtection(acc.id); }}
                        title="Delete account"
                      >
                        <Trash2 size={12} />
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
          
          {(error?.length ?? 0) > 0 && (
            <Card className="mx-3 mb-3 border-destructive bg-destructive/10">
              <CardContent className="flex items-start gap-2 p-3">
                <AlertCircle size={18} className="text-destructive flex-shrink-0 mt-0.5" />
                <p className="text-sm text-destructive">{error}</p>
              </CardContent>
            </Card>
          )}

          {/* Undo notification - single latest deleted account */}
          {deletedAccounts.length > 0 && (
            <div className="border-t border-border p-2">
              {((): React.JSX.Element => {
                const latestEntry = deletedAccounts[0];
                const totalCount = deletedAccounts.length;
                return (
                  <div className="flex items-center gap-2 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg p-2">
                    <div className="flex-1 min-w-0">
                      <p className="text-xs text-orange-800 dark:text-orange-200 truncate">
                        Deleted "{latestEntry.account.displayName ?? latestEntry.account.email}"
                        {totalCount > 1 && ` (+${totalCount - 1} more)`}
                      </p>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 text-orange-600 hover:text-orange-700 hover:bg-orange-100 dark:hover:bg-orange-900/20"
                        onClick={() => { void handleUndoDelete(latestEntry.account.id); }}
                        title="Restore latest deleted account"
                      >
                        <Undo2 size={12} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 text-orange-600 hover:text-orange-700 hover:bg-orange-100 dark:hover:bg-orange-900/20"
                        onClick={() => handleDismissUndo(latestEntry.account.id)}
                        title="Dismiss"
                      >
                        <X size={12} />
                      </Button>
                    </div>
                  </div>
                );
              })()}
            </div>
          )}

          {/* Action buttons */}
          <div className="border-t border-border p-2">
            <div className="flex items-center gap-1 bg-muted/30 rounded-lg p-1">
              <Button
                onClick={() => { void handleAddNew(); }}
                className="flex-1 h-8 gap-1 rounded-md min-w-0"
                variant="secondary"
                size="sm"
                title="Add new account"
              >
                <PlusCircle size={14} className="flex-shrink-0" />
                <span className="truncate text-xs">Add</span>
              </Button>
              <Button
                onClick={() => { void handleImport(); }}
                className={`flex-1 h-8 gap-1 rounded-md min-w-0 transition-colors ${
                  isDragOver ? 'bg-primary/20 text-primary' : ''
                }`}
                variant="ghost"
                size="sm"
                title={isDragOver ? "Drop file to import" : "Import account list"}
              >
                <Upload size={14} className="flex-shrink-0" />
                <span className="truncate text-xs">Import</span>
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Context Menu */}
      {contextMenu && (
        <div
          className="fixed z-50 bg-card border border-border rounded-lg shadow-lg py-1 min-w-[120px]"
          style={{
            left: contextMenu.x,
            top: contextMenu.y,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <button
            className="w-full px-3 py-2 text-left text-sm hover:bg-accent flex items-center gap-2"
            onClick={() => {
              const account = accounts.find(acc => acc.id === contextMenu.accountId);
              if (account) handleEdit(account);
              closeContextMenu();
            }}
          >
            <Edit size={14} />
            Edit Account
          </button>
          <button
            className="w-full px-3 py-2 text-left text-sm hover:bg-accent flex items-center gap-2"
            onClick={() => {
              const account = accounts.find(acc => acc.id === contextMenu.accountId);
              if (account) void handleCopyCredentials(account);
              closeContextMenu();
            }}
          >
            <Copy size={14} />
            Copy Credentials
          </button>
          <div className="border-t border-border my-1" />
          <button
            className="w-full px-3 py-2 text-left text-sm hover:bg-destructive/10 text-destructive flex items-center gap-2"
            onClick={() => {
              void handleDeleteWithProtection(contextMenu.accountId);
              closeContextMenu();
            }}
          >
            <Trash2 size={14} />
            Delete Account
          </button>
        </div>
      )}

      {/* Import Dialog */}
      <ImportDialog
        isOpen={isImportDialogOpen}
        onClose={() => setIsImportDialogOpen(false)}
        onImportComplete={handleImportComplete}
      />
    </aside>
  );
});

export default AccountManagerPanel;

---

// FILE: src\components\AccountManager\ProviderSuggestions.tsx

/**
 * @file Provider suggestions component for account form
 */
import { RefreshCw } from 'lucide-react';
import React from 'react';

import type { DiscoveredConfig } from '../../shared/types/protocol';
import { Button } from '../../shared/ui/button';
import { Card, CardContent } from '../../shared/ui/card';

interface ProviderSuggestionsProps {
  discovery: {
    isLoading: boolean;
    data: DiscoveredConfig | null;
    error: string | null;
  };
  onProviderSelect: (_config: DiscoveredConfig) => void;
  onRetry: () => void;
}

/**
 * Provider suggestions component
 */
const ProviderSuggestions: React.FC<ProviderSuggestionsProps> = ({
  discovery,
  onProviderSelect,
  onRetry,
}): React.JSX.Element => {
  if (discovery.isLoading) {
    return (
      <Card className="border-blue-500/50 bg-blue-500/10">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <RefreshCw className="h-4 w-4 animate-spin text-blue-400" />
            <p className="text-sm text-blue-300">
              Discovering email settings...
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (discovery.error !== null) {
    return (
      <Card className="border-yellow-500/50 bg-yellow-500/10">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-yellow-300">
              Could not auto-discover settings: {discovery.error}
            </p>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="gap-2 border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/20"
            >
              <RefreshCw size={14} />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (discovery.data !== null) {
    const config = discovery.data;
    return (
      <Card className="border-green-500/50 bg-green-500/10">
        <CardContent className="p-4">
          <div className="space-y-3">
            <p className="text-sm text-green-300">
              Found email server settings
            </p>
            <div className="flex flex-wrap gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => onProviderSelect(config)}
                className="border-green-500/50 text-green-400 hover:bg-green-500/20"
              >
                Use These Settings
              </Button>
            </div>
            <div className="text-xs text-green-400/70 space-y-1">
              {config.imap && (
                <p>IMAP: {config.imap.host}:{config.imap.port}</p>
              )}
              {config.smtp && (
                <p>SMTP: {config.smtp.host}:{config.smtp.port}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return <div />;
};

export default ProviderSuggestions;


---

// FILE: src\components\AccountManager\ServerConfigSection.tsx

/**
 * @file Server configuration section for account form
 */
import { Eye, EyeOff, Settings } from 'lucide-react';
import React from 'react';
import { Controller, type UseFormReturn, type Control } from 'react-hook-form';

import type { AccountFormType } from '../../shared/hooks/useAccountForm';
import { Button } from '../../shared/ui/button';
import { Checkbox } from '../../shared/ui/checkbox';
import { FormField } from '../FormField';

interface ServerConfigSectionProps {
  form: UseFormReturn<AccountFormType>;
  isPasswordVisible: boolean;
  setIsPasswordVisible: (_visible: boolean) => void;
  showAdvanced: boolean;
  setShowAdvanced: (_show: boolean) => void;
}

/**
 * Basic account details component
 */
const BasicAccountDetails: React.FC<{
  control: Control<AccountFormType>;
  isPasswordVisible: boolean;
  setIsPasswordVisible: (_visible: boolean) => void;
}> = ({ control, isPasswordVisible, setIsPasswordVisible }) => (
  <div className="space-y-4">
    <h3 className="text-lg font-medium text-foreground">Account Details</h3>

    <Controller
      name="displayName"
      control={control}
      render={({ field, fieldState }) => (
        <FormField
          label="Display Name"
          id="displayName"
          value={field.value ?? ''}
          onChange={field.onChange}
          error={fieldState.error?.message}
          placeholder="My Email Account"
        />
      )}
    />

    <Controller
      name="email"
      control={control}
      render={({ field, fieldState }) => (
        <FormField
          label="Email Address"
          id="email"
          type="email"
          value={field.value ?? ''}
          onChange={field.onChange}
          error={fieldState.error?.message}
          placeholder="<EMAIL>"
          required
        />
      )}
    />

    <div className="relative">
      <Controller
        name="password"
        control={control}
        render={({ field, fieldState }) => (
          <FormField
            label="Password"
            id="password"
            type={isPasswordVisible ? 'text' : 'password'}
            value={field.value ?? ''}
            onChange={field.onChange}
            error={fieldState.error?.message}
            placeholder="Enter your password"
            required
          />
        )}
      />
      <Button
        type="button"
        variant="ghost"
        size="sm"
        className="absolute right-2 top-8 h-8 w-8 p-0"
        onClick={() => setIsPasswordVisible(!isPasswordVisible)}
        tabIndex={-1}
      >
        {isPasswordVisible ? <EyeOff size={16} /> : <Eye size={16} />}
      </Button>
    </div>
  </div>
);

/**
 * Advanced settings toggle component
 */
const AdvancedToggle: React.FC<{
  showAdvanced: boolean;
  setShowAdvanced: (_show: boolean) => void;
}> = ({ showAdvanced, setShowAdvanced }) => (
  <div className="border-t border-border pt-4">
    <Button
      type="button"
      variant="outline"
      onClick={() => setShowAdvanced(!showAdvanced)}
      className="gap-2"
    >
      <Settings size={16} />
      {showAdvanced ? 'Hide' : 'Show'} Advanced Settings
    </Button>
  </div>
);

/**
 * Advanced server configuration component
 */
const AdvancedServerConfig: React.FC<{
  control: Control<AccountFormType>;
}> = ({ control }) => (
  <div className="space-y-6 border-t border-border pt-6">
    <div className="space-y-4">
      <h4 className="text-md font-medium text-foreground">Incoming Server (IMAP)</h4>

      <Controller
        name="incoming.hostPort"
        control={control}
        render={({ field, fieldState }) => (
          <FormField
            label="IMAP Server"
            id="incoming.hostPort"
            value={field.value ?? ''}
            onChange={field.onChange}
            error={fieldState.error?.message}
            placeholder="imap.example.com:993"
            description="Format: hostname:port (e.g., imap.gmail.com:993)"
          />
        )}
      />

      <div className="flex items-center space-x-2">
        <Controller
          name="incoming.useTls"
          control={control}
          render={({ field }) => (
            <Checkbox
              id="incoming.useTls"
              checked={field.value ?? true}
              onCheckedChange={field.onChange}
            />
          )}
        />
        <label htmlFor="incoming.useTls" className="text-sm text-foreground cursor-pointer">
          Use TLS/SSL encryption
        </label>
      </div>
    </div>

    <div className="space-y-4">
      <h4 className="text-md font-medium text-foreground">Outgoing Server (SMTP)</h4>

      <Controller
        name="outgoing.hostPort"
        control={control}
        render={({ field, fieldState }) => (
          <FormField
            label="SMTP Server"
            id="outgoing.hostPort"
            value={field.value ?? ''}
            onChange={field.onChange}
            error={fieldState.error?.message}
            placeholder="smtp.example.com:587"
            description="Format: hostname:port (e.g., smtp.gmail.com:587)"
          />
        )}
      />

      <div className="flex items-center space-x-2">
        <Controller
          name="outgoing.useTls"
          control={control}
          render={({ field }) => (
            <Checkbox
              id="outgoing.useTls"
              checked={field.value ?? true}
              onCheckedChange={field.onChange}
            />
          )}
        />
        <label htmlFor="outgoing.useTls" className="text-sm text-foreground cursor-pointer">
          Use TLS/SSL encryption
        </label>
      </div>
    </div>
  </div>
);

/**
 * Server configuration section component
 */
const ServerConfigSection: React.FC<ServerConfigSectionProps> = ({
  form,
  isPasswordVisible,
  setIsPasswordVisible,
  showAdvanced,
  setShowAdvanced,
}): React.JSX.Element => {
  const { control } = form;

  return (
    <div className="space-y-6">
      <BasicAccountDetails
        control={control}
        isPasswordVisible={isPasswordVisible}
        setIsPasswordVisible={setIsPasswordVisible}
      />

      <AdvancedToggle
        showAdvanced={showAdvanced}
        setShowAdvanced={setShowAdvanced}
      />

      {showAdvanced && (
        <AdvancedServerConfig control={control} />
      )}
    </div>
  );
};

export default ServerConfigSection;


---

// FILE: src\components\AccountManager\components\ServerSettingsSection.tsx

/**
 * @file Server settings section component for account form
 */

import { RefreshCw, Settings, X } from 'lucide-react';
import React from 'react';
import type { Control, FieldErrors, UseFormRegister, UseFormSetValue, UseFormWatch } from 'react-hook-form';

import type { DiscoveryStatus } from '../../../shared/hooks/useEmailDiscovery';
import { imapProviders } from '../../../shared/store/imapProviders';
import { Button } from '../../../shared/ui/button';
import { Input } from '../../../shared/ui/input';
import { Label } from '../../../shared/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../shared/ui/select';
import { cn } from '../../../shared/utils/utils';

// Import components will be added later

interface FormData {
  incoming: {
    hostPort: string;
    protocol: string;
    useTls: boolean;
  };
  outgoing: {
    hostPort: string;
    protocol: string;
    useTls: boolean;
  };
  email: string;
}

interface ServerSettingsSectionProps {
  control: Control<FormData>;
  register: UseFormRegister<FormData>;
  setValue: UseFormSetValue<FormData>;
  watch: UseFormWatch<FormData>;
  errors: FieldErrors<FormData>;
  showProviderSuggestions: boolean;
  discovery: {
    discoveryStatus: DiscoveryStatus;
    discoveryMessage?: string;
    isDiscovering: boolean;
    handleRetryDiscovery: (email: string, setValue: UseFormSetValue<FormData>) => void;
    handleManualSetup: () => void;
    setDiscoveryStatus: (status: DiscoveryStatus) => void;
  };
  onProviderSelect: (config: unknown) => void;
}

const handleProviderSelection = (
  selectedProviderName: string,
  onProviderSelect: (config: unknown) => void
): void => {
  if (!selectedProviderName) return;
  const provider = imapProviders.find(p => p.name === selectedProviderName);
  if (provider) {
    onProviderSelect(provider.config);
  }
};

const handleManualProviderSelection = (
  value: string,
  setValue: UseFormSetValue<FormData>
): void => {
  if (value === 'custom') return;
  const provider = imapProviders.find(p => p.name === value);
  if (provider) {
    setValue('incoming.hostPort', `${provider.config.imap?.host}:${provider.config.imap?.port}`);
    setValue('incoming.protocol', 'imap');
    setValue('incoming.useTls', provider.config.imap?.secure ?? true);
    if (provider.config.smtp) {
      setValue('outgoing.hostPort', `${provider.config.smtp.host}:${provider.config.smtp.port}`);
      setValue('outgoing.protocol', 'smtp');
      setValue('outgoing.useTls', provider.config.smtp.secure ?? true);
    }
  }
};

const ServerSettingsSection: React.FC<ServerSettingsSectionProps> = ({
  register,
  setValue,
  watch,
  errors,
  showProviderSuggestions,
  discovery,
  onProviderSelect,
}) => {
  const watchedEmail = watch('email');
  const watchedIncomingHostPort = watch('incoming.hostPort');

  return (
    <div className="space-y-4">
      {showProviderSuggestions && (
        <div className="p-3 mb-3 rounded-lg bg-muted border border-border">
          <h4 className="text-sm font-medium text-foreground mb-2">
            Couldn't find settings automatically.
          </h4>
          <p className="text-xs text-muted-foreground mb-2">
            Try selecting a common provider from the list below:
          </p>
          <Select onValueChange={(value) => handleProviderSelection(value, onProviderSelect)}>
            <SelectTrigger>
              <SelectValue placeholder="Select a provider..." />
            </SelectTrigger>
            <SelectContent>
              {imapProviders.map((provider) => (
                <SelectItem key={provider.name} value={provider.name}>
                  {provider.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="space-y-4">
        
        <div className="space-y-2">
          <Label htmlFor="incoming.hostPort">Server Address</Label>
          <div className="relative">
            <Input
              id="incoming.hostPort"
              type="text"
              {...register('incoming.hostPort')}
              placeholder="imap.example.com:993 (IMAP 993/143, POP3 995/110)"
              className={cn(
                Boolean(errors.incoming?.hostPort) && "border-destructive",
                discovery.discoveryStatus === 'failed' && "border-orange-300 dark:border-orange-700"
              )}
            />
            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-full text-blue-600 hover:text-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900/20"
                onClick={() => discovery.handleRetryDiscovery(watchedEmail, setValue)}
                disabled={discovery.isDiscovering || !watchedEmail}
                title="Auto-discover server settings"
              >
                <RefreshCw size={12} className={discovery.isDiscovering ? 'animate-spin' : ''} />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-full text-gray-600 hover:text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-900/20"
                onClick={discovery.handleManualSetup}
                title="Configure manually"
              >
                <Settings size={12} />
              </Button>
              {Boolean(watchedIncomingHostPort) && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 rounded-full"
                  onClick={() => setValue('incoming.hostPort', '', { shouldValidate: true })}
                >
                  <X size={14} />
                </Button>
              )}
            </div>
          </div>
          {Boolean(errors.incoming?.hostPort) && (
            <p className="text-sm text-destructive">{String(errors.incoming?.hostPort?.message)}</p>
          )}
          {discovery.discoveryStatus === 'failed' && errors.incoming?.hostPort === undefined && (
            <p className="text-xs text-orange-600 dark:text-orange-400 truncate">
              {discovery.discoveryMessage}
            </p>
          )}
        </div>

        {discovery.discoveryStatus === 'manual' && (
          <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-blue-900 dark:text-blue-100">Manual Mode</span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => discovery.setDiscoveryStatus('idle')}
                className="h-6 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900/20"
              >
                Exit
              </Button>
            </div>
            <Select onValueChange={(value) => handleManualProviderSelection(value, setValue)}>
              <SelectTrigger className="h-8">
                <SelectValue placeholder="Select provider" />
              </SelectTrigger>
              <SelectContent>
                {imapProviders.map((provider) => (
                  <SelectItem key={provider.name} value={provider.name}>
                    {provider.name}
                  </SelectItem>
                ))}
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
    </div>
  );
};

export default ServerSettingsSection;


---

// FILE: src\components\AccountManager\index.ts

/**
 * @file Entry point for AccountManager components
 */
import AccountForm from './AccountForm';

export { default } from './AccountManagerPanel';

export { AccountForm }; 

---

// FILE: src\components\EmailListPanel.tsx

/**
 * @file Panel that contains the list of mailboxes.
 */
import {
  ChevronDown,
  RefreshCw, Loader2, Mailbox
} from 'lucide-react';
import React from 'react';

import { useMailboxManager } from '../shared/hooks/useMailboxManager';
import { useAccountStore } from '../shared/store/accountStore';
import { Button } from '../shared/ui/button';

interface EmailListPanelProps {
  searchQuery?: string;
}

/**
 * Component for displaying and managing email folders
 */
const EmailListPanel: React.FC<EmailListPanelProps> = React.memo(({ searchQuery = '' }) => {
  const {
    selectedAccountId,
    selectedMailbox,
    selectMailbox,
  } = useAccountStore();

  const {
    isLoading,
    isRefreshing,
    showFolders,
    setShowFolders,
    renderedFolders,
    handleRefresh,
  } = useMailboxManager();



  if (isLoading) {
    return (
      <div className="flex flex-col h-full items-center justify-center p-6 bg-background text-foreground">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 rounded-full flex items-center justify-center bg-muted">
            <Loader2 size={28} className="text-primary animate-spin" />
          </div>
          <div className="text-center">
            <h3 className="text-lg font-medium mb-2">Loading Folders</h3>
            <p className="text-sm text-muted-foreground">Please wait a moment</p>
          </div>
        </div>
      </div>
    );
  }

  if ((selectedAccountId?.length ?? 0) === 0) {
    return (
      <div className="flex flex-col h-full items-center justify-center p-6 bg-background text-foreground">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 rounded-full flex items-center justify-center bg-muted">
            <Mailbox size={28} className="text-muted-foreground" />
          </div>
          <div className="text-center">
            <h3 className="text-lg font-medium mb-2">No Account Selected</h3>
            <p className="text-sm text-gray-400">Select an account to view your emails</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <nav className="flex flex-col h-full bg-background text-foreground" aria-label="Email folders">
      {/* Folders section */}
      <div className="p-3 flex-grow overflow-auto custom-scrollbar">
        <div className="flex items-center justify-between pb-2 mb-1">
          <button
            className="flex items-center gap-2 cursor-pointer py-2 px-1 focus:outline-none focus:ring-2 focus:ring-ring rounded"
            onClick={() => setShowFolders(!showFolders)}
            aria-expanded={showFolders}
            aria-controls="folders-list"
          >
            <ChevronDown
              size={16}
              className={`text-muted-foreground transition-transform duration-200 ${showFolders ? 'transform rotate-0' : 'transform -rotate-90'}`}
              aria-hidden="true"
            />
            <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wider">Folders</h3>
          </button>

          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => { void handleRefresh(); }}
              title="Refresh folders"
              aria-label="Refresh folder list"
              disabled={isRefreshing}
              className="rounded-full h-8 w-8"
            >
              <RefreshCw size={16} className={isRefreshing ? "animate-spin" : ""} />
            </Button>
          </div>
        </div>

        {showFolders && (
          <ul id="folders-list" className="space-y-0.5 pl-2" role="list">
            {renderedFolders
              .filter(mailbox => mailbox.label.toLowerCase().includes(searchQuery.toLowerCase()))
              .map((mailbox) => {
              const Icon = mailbox.icon;
              const isSelected = selectedMailbox === mailbox.name;

              return (
                <li key={mailbox.name} role="none">
                  <button
                    onClick={() => selectMailbox(mailbox.name)}
                    className={`
                      w-full flex items-center gap-3 px-3 py-2 rounded-full transition-all text-sm font-medium focus:outline-none focus:ring-2 focus:ring-ring
                      ${isSelected
                        ? 'bg-primary/20 text-primary-foreground'
                        : 'hover:bg-muted text-muted-foreground hover:text-foreground'}
                    `}
                    aria-current={isSelected ? 'page' : undefined}
                    aria-label={`Select ${mailbox.label} folder`}
                  >
                    <Icon size={18} className={isSelected ? 'text-primary' : 'text-muted-foreground'} aria-hidden="true" />
                    <span className="truncate">{mailbox.label}</span>
                    {typeof mailbox.count === 'number' && mailbox.count > 0 && (
                      <span className="ml-auto text-xs bg-muted text-muted-foreground px-2 py-0.5 rounded-full min-w-[1.5rem] text-center">
                        {mailbox.count}
                      </span>
                    )}
                  </button>
                </li>
              );
            })}
          </ul>
        )}
      </div>
    </nav>
  );
});

export default EmailListPanel;

---

// FILE: src\components\EmailListSkeleton.tsx

/**
 * @file Component that shows a loading skeleton for the email list.
 */
import React from 'react';

interface EmailListSkeletonProps {
  count?: number;
}

/**
 * Skeleton loader component for the email list when emails are being fetched
 */
const EmailListSkeleton: React.FC<EmailListSkeletonProps> = ({ count = 10 }) => {
  return (
    <div className="animate-pulse bg-[#121212] text-white h-full" role="status" aria-label="Loading emails">
      {Array.from({ length: count }, (_, index) => (
        <div
          key={`skeleton-item-${index}`}
          className="p-3 border-b border-gray-800/20 flex items-start gap-3"
          aria-hidden="true"
        >
          {/* Checkbox placeholder */}
          <div className="flex items-center h-6 pt-0.5">
            <div className="w-4 h-4 rounded bg-gray-800/60" />
          </div>
          
          <div className="flex-grow min-w-0">
            {/* From & Date row */}
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded-full bg-gray-800/60" />
                <div className="h-4 w-32 bg-gray-800/60 rounded-full" />
              </div>
              <div className="flex items-center gap-2">
                <div className="h-3 w-16 bg-gray-800/60 rounded-full" />
              </div>
            </div>

            {/* Subject line */}
            <div className="h-4 bg-gray-800/60 rounded-full w-3/4 mb-2" />

            {/* Message preview */}
            <div className="h-3 bg-gray-800/40 rounded-full w-1/2" />
          </div>
        </div>
      ))}
      <span className="sr-only">Loading email list...</span>
    </div>
  );
};

export default EmailListSkeleton; 

---

// FILE: src\components\EmailListView.tsx

/**
 * @file Renders the list of emails with infinite scroll, search filtering, and modern design.
 */
import {
  Star, Paperclip, CheckCircle2, Trash2, MailX, Inbox
} from 'lucide-react';
import React from 'react';

import { useEmailList } from '../shared/hooks/useEmailList';
import { useKeyboardNavigation } from '../shared/hooks/useKeyboardNavigation';
import { useAccountStore } from '../shared/store/accountStore';
import type { EmailHeader } from '../shared/types/email';
import { Button } from '../shared/ui/button';

import EmailListSkeleton from './EmailListSkeleton';

interface EmailListViewProps {
  searchQuery?: string;
  showHeader?: boolean;
}

/**
 * Component to render a list of emails with selection, infinite scroll,
 * and bulk actions functionality
 */
const EmailListView: React.FC<EmailListViewProps> = ({ searchQuery = '' }) => {
  const {
    selectedAccountId,
    selectedMailbox,
    selectEmail,
    selectedEmailId,
  } = useAccountStore();

  const {
    isLoading,
    isFetchingMore,
    error,
    hasLoadedOnce,
    selectedUids,
    selectAll,
    isToolbarVisible,
    keyboardSelectedIndex,
    emailHeaders,
    filteredEmails,
    hasMoreEmails,
    lastEmailElementRef,
    handleSelectEmail,
    handleSelectAll,
    handleDeleteSelected,
    setKeyboardSelectedIndex,
    formatDate,
    hasAttachments,
    isStarred,
  } = useEmailList({ searchQuery });







  // Keyboard navigation
  useKeyboardNavigation({
    onArrowUp: () => {
      if (filteredEmails.length === 0) return;
      const newIndex = keyboardSelectedIndex > 0 ? keyboardSelectedIndex - 1 : filteredEmails.length - 1;
      setKeyboardSelectedIndex(newIndex);
    },
    onArrowDown: () => {
      if (filteredEmails.length === 0) return;
      const newIndex = keyboardSelectedIndex < filteredEmails.length - 1 ? keyboardSelectedIndex + 1 : 0;
      setKeyboardSelectedIndex(newIndex);
    },
    onEnter: () => {
      if (keyboardSelectedIndex >= 0 && keyboardSelectedIndex < filteredEmails.length) {
        selectEmail(filteredEmails[keyboardSelectedIndex].uid);
      }
    },
    onSpace: () => {
      if (keyboardSelectedIndex >= 0 && keyboardSelectedIndex < filteredEmails.length) {
        handleSelectEmail(filteredEmails[keyboardSelectedIndex].uid);
      }
    },
    onDelete: () => {
      if (selectedUids.length > 0) {
        void handleDeleteSelected();
      }
    },
    enabled: filteredEmails.length > 0
  });

  // Show appropriate message when no account or mailbox is selected
  if ((selectedAccountId?.length ?? 0) === 0 || (selectedMailbox?.length ?? 0) === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6 text-center bg-[#121212] text-white">
        <div className="bg-blue-900/20 p-6 rounded-xl border border-blue-800/30 max-w-sm backdrop-blur-lg">
          <div className="mx-auto mb-6 w-16 h-16 rounded-full flex items-center justify-center bg-blue-900/30">
            <Inbox size={28} className="text-blue-400" />
          </div>
          <h3 className="text-xl font-medium mb-3 text-blue-300">Select Account & Mailbox</h3>
          <p className="text-sm text-blue-300/80">
            {(selectedAccountId?.length ?? 0) === 0
              ? 'Choose an account from the right panel to view emails.'
              : 'Select a mailbox to view its emails.'
            }
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) return <EmailListSkeleton />;

  if ((error?.length ?? 0) > 0 && emailHeaders.length === 0) return (
    <div className="flex flex-col items-center justify-center h-full p-6 text-center bg-[#121212] text-white">
      <div className="bg-red-900/20 p-6 rounded-xl border border-red-800/30 max-w-sm backdrop-blur-lg">
        <div className="mx-auto mb-6 w-16 h-16 rounded-full flex items-center justify-center bg-red-900/30">
          <MailX size={28} className="text-red-400" />
        </div>
        <h3 className="text-xl font-medium mb-3 text-red-300">Error Loading Emails</h3>
        <p className="text-sm text-red-300/80">{error}</p>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col bg-[#121212] text-white">
      {/* Selection toolbar */}
      <div
        className={`flex items-center gap-3 px-4 py-2 sticky top-0 z-1 bg-gray-900/50 backdrop-blur-lg border-b border-gray-800/30 transition-all duration-300 ease-in-out ${isToolbarVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
        role="toolbar"
        aria-label="Email selection actions"
      >
        <div className="flex items-center h-6">
          <input
            type="checkbox"
            checked={selectAll}
            onChange={handleSelectAll}
            aria-label="Select all emails"
            className="w-4 h-4 rounded bg-transparent border border-gray-500 accent-blue-500 cursor-pointer focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <Button
          variant="destructive"
          onClick={() => { void handleDeleteSelected(); }}
          aria-label={`Delete ${selectedUids.length} selected emails`}
          className="rounded-full"
        >
          <Trash2 size={16} />
          Delete
        </Button>

        <span className="text-xs text-blue-400 ml-auto" aria-live="polite">
          {selectedUids.length} selected
        </span>
      </div>



      {filteredEmails.length === 0 && !isLoading && hasLoadedOnce && (
        <div className="flex flex-col items-center justify-center flex-grow p-6 text-center">
          <div className="max-w-xs">
            <div className="mx-auto mb-6 w-16 h-16 rounded-full flex items-center justify-center bg-gray-800/40">
              <CheckCircle2 size={28} className="text-gray-400" />
            </div>
            <h3 className="text-xl font-medium mb-3">No emails found</h3>
            <p className="text-sm text-gray-400">
              {searchQuery ? 'Try a different search query' : 'This folder is empty'}
            </p>
          </div>
        </div>
      )}
      
      <div className="divide-y divide-gray-800/20 flex-grow overflow-y-auto custom-scrollbar" role="list" aria-label="Email list">
        {filteredEmails.map((email: EmailHeader, index: number) => {
          const isSelected = selectedUids.includes(email.uid);
          const isCurrent = selectedEmailId === email.uid;
          const isKeyboardSelected = keyboardSelectedIndex === index;
          const emailHasAttachments = hasAttachments(email);
          const emailIsStarred = isStarred(email);

          return (
            <div
              key={email.uid}
              ref={index === filteredEmails.length - 1 ? lastEmailElementRef : null}
              onClick={() => selectEmail(email.uid)}
              className={`p-3 cursor-pointer transition-all hover:bg-gray-800/20 flex items-start gap-3 focus-within:ring-2 focus-within:ring-blue-500 ${
                isCurrent
                  ? 'bg-blue-900/20 border-l-4 border-l-blue-500'
                  : 'bg-transparent border-l-4 border-l-transparent'
              } ${isSelected ? 'bg-gray-800/40' : ''} ${isKeyboardSelected ? 'ring-2 ring-yellow-500' : ''}`}
              role="listitem"
              aria-selected={isCurrent}
              tabIndex={isKeyboardSelected ? 0 : -1}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  selectEmail(email.uid);
                }
              }}
            >
              <div
                className="flex items-center h-6 pt-0.5"
                onClick={(e) => e.stopPropagation()}
              >
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={(e) => handleSelectEmail(email.uid, e as unknown as React.MouseEvent)}
                  className="w-4 h-4 rounded bg-transparent border border-gray-500 accent-blue-500 cursor-pointer focus:ring-2 focus:ring-blue-500"
                  aria-label={`Select email from ${typeof email.from === 'string' ? email.from : email.from.text}`}
                />
              </div>
              
              <div className="flex-grow min-w-0">
                <div className="flex items-center justify-between">
                  <span className={`text-sm font-medium truncate ${!email.seen ? 'text-white' : 'text-gray-400'}`}>
                    {typeof email.from === 'string' ? email.from : email.from.text}
                  </span>
                  <span className={`text-xs flex-shrink-0 ml-4 ${!email.seen ? 'text-blue-400' : 'text-gray-500'}`}>
                    {formatDate(email.date)}
                  </span>
                </div>
                <p className={`text-sm mt-0.5 truncate ${!email.seen ? 'text-gray-200 font-semibold' : 'text-gray-400'}`}>
                  {email.subject || '(no subject)'}
                </p>
                <p className="text-xs mt-1.5 text-gray-500 line-clamp-2">
                  {email.snippet}
                </p>
              </div>

              <div className="flex flex-col items-center gap-2 pt-1">
                {emailIsStarred && <Star size={16} className="text-yellow-400 fill-current" />}
                {emailHasAttachments && <Paperclip size={16} className="text-gray-400" />}
              </div>
            </div>
          );
        })}
      </div>
      
      {/* Loading indicator for infinite scroll */}
      {hasMoreEmails && !searchQuery && <div ref={lastEmailElementRef} className="h-1" />}
      {isFetchingMore && <EmailListSkeleton count={3} />}
    </div>
  );
};

export default EmailListView;

---

// FILE: src\components\EmailViewPanel.tsx

/**
 * @file This panel acts as a container for either the email list view or the email content view.
 */
import { AnimatePresence, motion } from 'framer-motion';
import React from 'react';

import { useAccountStore } from '../shared/store/accountStore';
import { useUIStore } from '../shared/store/uiStore';

import EmailListView from './EmailListView';
import EmailViewer from './EmailViewer';
import SettingsView from './SettingsPanel/SettingsView';

interface EmailViewPanelProps {
  searchQuery?: string;
}

const EmailViewPanel: React.FC<EmailViewPanelProps> = React.memo(({ searchQuery = '' }) => {
  const selectedEmailId = useAccountStore((state) => state.selectedEmailId);
  const { isSettingsOpen } = useUIStore();

  return (
    <div className="h-full w-full bg-background relative">
      <AnimatePresence>
        {isSettingsOpen && (
          <motion.div
            key="settings"
            initial={{ opacity: 0, scale: 0.98 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.98 }}
            transition={{ duration: 0.2, ease: 'easeInOut' }}
            className="absolute inset-0 z-20 bg-card rounded-l-xl"
          >
            <SettingsView />
          </motion.div>
        )}
      </AnimatePresence>

      <div className="absolute inset-0 z-0">
        {(selectedEmailId ?? 0) > 0 ? <EmailViewer /> : <EmailListView searchQuery={searchQuery} />}
      </div>
    </div>
  );
});

export default EmailViewPanel;

---

// FILE: src\components\EmailViewer.tsx

import DOMPurify from 'dompurify';
import {
  Trash2, Archive, Reply, Forward, Star, Download,
  MoreVertical, RefreshCw, AlertCircle, CheckCheck, ArrowLeft, Clock, FileText, Code
} from 'lucide-react';
import React, { useState } from 'react';

import { useEmailViewer } from '../shared/hooks/useEmailViewer';
import { useAccountStore } from '../shared/store/accountStore';
import { useLogStore } from '../shared/store/logStore';
import { Button } from '../shared/ui/button';

import '../shared/styles/email-content.css';

// Safe HTML content component - uses DOMPurify to sanitize content before rendering
const SafeHtmlContent: React.FC<{ content: string }> = ({ content }): React.JSX.Element => {
  const sanitizedContent = DOMPurify.sanitize(content);
  // eslint-disable-next-line react/no-danger -- Content is sanitized with DOMPurify
  return <div dangerouslySetInnerHTML={{ __html: sanitizedContent }} />;
};

/**
 * Component for viewing the full content of an email
 * Provides actions for managing the email and displays the content safely
 */
const EmailViewer = (): React.JSX.Element => {
  const {
    selectedEmailId,
    currentEmail,
    selectEmail,
  } = useAccountStore();

  const addLog = useLogStore((state) => state.addLog);

  const {
    isLoading,
    error,
    emailContent,
    isStarred,
    handleDelete,
    handleStar,
    formatDate,
  } = useEmailViewer();

  // State for view mode - default to HTML to show images and formatting
  const [viewMode, setViewMode] = useState<'html' | 'text'>('html');

  // Get content based on view mode
  const getEmailContent = (): string => {
    if (!emailContent) return '<div style="color: white; padding: 20px;"><p>No email content available</p></div>';

    if (viewMode === 'html') {
      // Try HTML first
      if (typeof emailContent.html === 'string' && emailContent.html.length > 0) {
        return DOMPurify.sanitize(emailContent.html, {
          KEEP_CONTENT: true,
          ALLOW_UNKNOWN_PROTOCOLS: true
        });
      }
      // Fallback to textAsHtml for HTML mode
      if (typeof emailContent.textAsHtml === 'string' && emailContent.textAsHtml.length > 0) {
        return emailContent.textAsHtml;
      }
    } else {
      // Text mode - prefer textAsHtml
      if (typeof emailContent.textAsHtml === 'string' && emailContent.textAsHtml.length > 0) {
        return emailContent.textAsHtml;
      }
      // Fallback to plain text with formatting
      if (typeof emailContent.text === 'string' && emailContent.text.length > 0) {
        const sanitizedText = DOMPurify.sanitize(emailContent.text);

        // Convert URLs to clickable links
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const linkedText = sanitizedText.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer" style="color: #60a5fa;">$1</a>');

        // Convert email addresses to mailto links
        const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
        const finalText = linkedText.replace(emailRegex, '<a href="mailto:$1" style="color: #60a5fa;">$1</a>');

        return `<div style="white-space: pre-wrap; word-wrap: break-word; font-family: inherit; line-height: 1.6; color: white;">${finalText}</div>`;
      }
    }

    return '<div style="color: white; padding: 20px;"><p>No content available for this mode</p></div>';
  };
  



  
  // Empty state when no email is selected
  if ((selectedEmailId ?? 0) === 0) {
    return (
      <div className="flex items-center justify-center h-full bg-[#121212] text-white">
        <div className="text-center max-w-xs mx-auto p-8">
          <div className="mx-auto mb-6 w-16 h-16 rounded-full flex items-center justify-center bg-gray-800/40 backdrop-blur-lg">
            <Clock size={32} className="text-gray-400" />
          </div>
          <h3 className="text-xl font-medium mb-3">No Email Selected</h3>
          <p className="text-sm text-gray-400">Select an email from the list to view its content</p>
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col h-full bg-[#121212] text-white p-6">
        <div className="flex-shrink-0 animate-pulse">
          <div className="h-8 bg-gray-800/60 rounded-lg w-3/4 mb-4" />
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-12 h-12 rounded-full bg-gray-800/60" />
            <div className="space-y-2 flex-grow">
              <div className="h-4 bg-gray-800/60 rounded-full w-48" />
              <div className="h-3 bg-gray-800/60 rounded-full w-32" />
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 mb-6 bg-blue-900/20 text-blue-400 py-2.5 px-4 rounded-lg">
          <RefreshCw size={18} className="animate-spin" />
          <span className="text-sm">Loading message content...</span>
        </div>
        
        <div className="flex-grow animate-pulse space-y-4">
          <div className="h-4 bg-gray-800/60 rounded-full w-full" />
          <div className="h-4 bg-gray-800/60 rounded-full w-5/6" />
          <div className="h-4 bg-gray-800/60 rounded-full w-4/6" />
          <div className="h-20 bg-gray-800/40 rounded-lg w-full mt-6" />
        </div>
      </div>
    );
  }

  // Error state
  if ((error?.length ?? 0) > 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full bg-[#121212] text-white p-6">
        <div className="bg-red-900/20 p-8 rounded-xl border border-red-800/30 max-w-md text-center backdrop-blur-lg">
          <div className="mx-auto mb-6 w-16 h-16 rounded-full flex items-center justify-center bg-red-900/30">
            <AlertCircle size={32} className="text-red-400" />
          </div>
          <h3 className="text-xl font-medium mb-3 text-red-300">Failed to Load Email</h3>
          <p className="text-sm text-red-300/80 mb-6">{error}</p>
          <button 
            onClick={() => selectEmail(selectedEmailId)}
            className="px-5 py-2.5 bg-red-900/30 hover:bg-red-900/50 text-red-200 rounded-full text-sm transition-colors flex items-center justify-center mx-auto gap-2"
          >
            <RefreshCw size={16} />
            Retry Loading
          </button>
        </div>
      </div>
    );
  }



  // Main email content view
  return (
    <div className="flex flex-col h-full bg-[#121212] text-white">
      {/* Email actions bar */}
      <div className="flex-shrink-0 flex items-center justify-between p-2.5 border-b border-gray-800/40 bg-gray-900/20 backdrop-blur-md">
        <div className="flex items-center gap-2 overflow-x-auto hide-scrollbar px-2">
          <button
            onClick={() => selectEmail(null)}
            className="flex items-center justify-center p-2 rounded-full hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="Back to list"
            aria-label="Back to email list"
          >
            <ArrowLeft size={20} aria-hidden="true" />
          </button>

          <Button variant="outline" aria-label="Reply to email" className="rounded-full">
            <Reply size={16} />
            Reply
          </Button>
          <Button variant="outline" aria-label="Forward email" className="rounded-full">
            <Forward size={16} />
            Forward
          </Button>
          <Button variant="outline" aria-label="Archive email" className="rounded-full">
            <Archive size={16} />
            Archive
          </Button>
          <Button variant="destructive" onClick={() => { void handleDelete(); }} aria-label="Delete email" className="rounded-full">
            <Trash2 size={16} />
            Delete
          </Button>
        </div>
        
        <div className="flex items-center gap-2 pr-2">
          <button
            onClick={() => {
              addLog('Mark as unread clicked (not implemented yet).', 'info');
            }}
            className="flex items-center justify-center p-2 rounded-full hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="Mark as unread"
            aria-label="Mark email as unread"
          >
            <CheckCheck size={18} className="text-blue-400" aria-hidden="true" />
          </button>

          <button
            onClick={() => { void handleStar(); }}
            className={`flex items-center justify-center p-2 rounded-full hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-500 ${isStarred ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'}`}
            title={isStarred ? "Unstar" : "Star"}
            aria-label={isStarred ? "Remove star from email" : "Add star to email"}
            aria-pressed={isStarred}
          >
            <Star size={18} className={isStarred ? "fill-yellow-400" : ""} aria-hidden="true" />
          </button>

          <button
            onClick={() => {
              addLog('Download clicked (not implemented yet).', 'info');
            }}
            className="flex items-center justify-center p-2 rounded-full hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="Download"
            aria-label="Download email"
          >
            <Download size={18} aria-hidden="true" />
          </button>

          <button
            onClick={() => {
              addLog('More options clicked (not implemented yet).', 'info');
            }}
            className="flex items-center justify-center p-2 rounded-full hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="More options"
            aria-label="More email options"
            aria-haspopup="menu"
          >
            <MoreVertical size={18} aria-hidden="true" />
          </button>
        </div>
      </div>

      {/* Email header */}
      <div className="flex-shrink-0 px-4 py-3 bg-gray-900/5 border-b border-gray-800/20">
        <header className="space-y-3">
          {/* Subject line with controls */}
          <div className="flex items-center gap-3">
            <h1 className="text-lg font-semibold flex-grow min-w-0">
              <span className="line-clamp-1">{currentEmail?.subject ?? '(No Subject)'}</span>
            </h1>

            {/* Elegant segmented control for view modes */}
            <div className="flex items-center bg-gray-800/40 rounded-lg p-0.5 border border-gray-700/50">
              <button
                onClick={() => setViewMode('html')}
                className={`flex items-center gap-1.5 px-2.5 py-1 rounded-md text-xs font-medium transition-all duration-200 ${
                  viewMode === 'html'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-400 hover:text-gray-300'
                }`}
                title="Rich HTML view with images and formatting"
              >
                <Code size={12} />
                HTML
              </button>
              <button
                onClick={() => setViewMode('text')}
                className={`flex items-center gap-1.5 px-2.5 py-1 rounded-md text-xs font-medium transition-all duration-200 ${
                  viewMode === 'text'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-400 hover:text-gray-300'
                }`}
                title="Clean text view"
              >
                <FileText size={12} />
                Text
              </button>
            </div>

            <button
              onClick={() => { void handleStar(); }}
              className={`p-1.5 rounded-lg transition-colors ${isStarred ? 'text-yellow-400 hover:text-yellow-300' : 'text-gray-400 hover:text-yellow-400'}`}
              aria-label={isStarred ? "Remove star from email" : "Add star to email"}
              aria-pressed={isStarred}
            >
              <Star size={16} className={isStarred ? "fill-current" : ""} />
            </button>
          </div>

          {/* Sender info */}
          {currentEmail && (
            <div className="flex items-center gap-3">
              <div
                className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center font-medium text-sm text-white"
                aria-hidden="true"
              >
                {typeof currentEmail.from === 'object' && currentEmail.from?.text?.charAt(0).toUpperCase()}
                {typeof currentEmail.from === 'string' && (currentEmail.from as string).charAt(0).toUpperCase()}
              </div>
              <div className="flex-grow min-w-0">
                <div className="font-medium text-sm truncate">
                  {
                    typeof currentEmail.from === 'object'
                      ? currentEmail.from.text.split('<')[0].trim().replace(/^"|"$/g, '') || currentEmail.from.text
                      : currentEmail.from
                  }
                </div>
                <div className="text-xs text-gray-400 truncate">
                  {
                    typeof currentEmail.from === 'object' && (currentEmail.from.text?.length ?? 0) > 0 && currentEmail.from.text.includes('<')
                      ? `${currentEmail.from.text.match(/<(.+?)>/)?.[1] ?? ''}`
                      : ''
                  }
                </div>
              </div>
              <div className="flex-shrink-0 text-xs text-gray-400">
                <time dateTime={currentEmail.date}>{formatDate(currentEmail.date)}</time>
              </div>
            </div>
          )}
        </header>
      </div>

      {/* Email body */}
      <main className="flex-grow overflow-y-auto p-4 custom-scrollbar">
        <div
          className="email-content max-w-none text-white"
          role="document"
          aria-label="Email content"
          style={{
            // Base email content styles
            lineHeight: '1.6',
            fontSize: '14px',
            fontFamily: 'inherit',
            color: 'white',
          } as React.CSSProperties}
        >
          <SafeHtmlContent content={getEmailContent()} />
        </div>
      </main>
    </div>
  );
};

export default EmailViewer; 

---

// FILE: src\components\ErrorBoundary.tsx

/**
 * @file Error boundary component for catching and displaying React errors gracefully
 */
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Component, type ErrorInfo, type ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * Error boundary component that catches JavaScript errors anywhere in the child component tree,
 * logs those errors, and displays a fallback UI instead of the component tree that crashed.
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Update state with error info for display
    this.setState({
      error,
      errorInfo
    });
  }

  handleReset = (): void => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render(): React.ReactNode {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback !== undefined) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <div className="flex flex-col items-center justify-center h-full p-6 bg-[#121212] text-white">
          <div className="bg-red-900/20 p-8 rounded-xl border border-red-800/30 max-w-md text-center backdrop-blur-lg">
            <div className="mx-auto mb-6 w-16 h-16 rounded-full flex items-center justify-center bg-red-900/30">
              <AlertTriangle size={32} className="text-red-400" />
            </div>
            <h2 className="text-xl font-medium mb-3 text-red-300">Something went wrong</h2>
            <p className="text-sm text-red-300/80 mb-6">
              An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.
            </p>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mb-6 text-left">
                <summary className="cursor-pointer text-sm text-red-300 mb-2">Error Details</summary>
                <pre className="text-xs text-red-200 bg-red-900/30 p-3 rounded overflow-auto max-h-32">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}
            
            <button 
              onClick={this.handleReset}
              className="px-5 py-2.5 bg-red-900/30 hover:bg-red-900/50 text-red-200 rounded-full text-sm transition-colors flex items-center justify-center mx-auto gap-2 focus:outline-none focus:ring-2 focus:ring-red-500"
              aria-label="Try again"
            >
              <RefreshCw size={16} />
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;


---

// FILE: src\components\FormField.tsx

/**
 * @file Accessible form field components with validation using ShadCN UI
 */
import { AlertCircle, Eye, EyeOff } from 'lucide-react';
import React, { forwardRef, useState } from 'react';

import { Checkbox } from '../shared/ui/checkbox';
import { Input } from '../shared/ui/input';
import { Label } from '../shared/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../shared/ui/select';
import { cn } from '../shared/utils/utils';

interface FormFieldProps {
  label: string;
  id: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel';
  value: string;
  onChange: (_value: string) => void;
  error?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  autoComplete?: string;
  className?: string;
  description?: string;
}

/**
 * Accessible form input field with validation and error handling
 */
export const FormField = forwardRef<HTMLInputElement, FormFieldProps>(({
  label,
  id,
  type = 'text',
  value,
  onChange,
  error,
  placeholder,
  required = false,
  disabled = false,
  autoComplete,
  className = '',
  description
}, ref) => {
  const [showPassword, setShowPassword] = useState(false);
  const isPassword = type === 'password';
  const inputType = isPassword && showPassword ? 'text' : type;

  const inputId = id;
  const errorId = `${id}-error`;
  const descriptionId = `${id}-description`;

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={inputId} className="text-sm font-medium">
        {label}
        {required && (
          <span className="text-destructive ml-1" aria-label="required">*</span>
        )}
      </Label>

      {(description?.length ?? 0) > 0 && (
        <p id={descriptionId} className="text-sm text-muted-foreground">
          {description}
        </p>
      )}

      <div className="relative">
        <Input
          ref={ref}
          id={inputId}
          type={inputType}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          autoComplete={autoComplete}
          aria-invalid={(error?.length ?? 0) > 0 ? 'true' : 'false'}
          aria-describedby={`${(error?.length ?? 0) > 0 ? errorId : ''} ${(description?.length ?? 0) > 0 ? descriptionId : ''}`.trim()}
          className={cn(
            (error?.length ?? 0) > 0 && "border-destructive focus-visible:ring-destructive",
            isPassword && "pr-10"
          )}
        />

        {isPassword && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-ring rounded"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        )}
      </div>

      {(error?.length ?? 0) > 0 && (
        <div id={errorId} className="flex items-center gap-2 text-sm text-destructive" role="alert">
          <AlertCircle size={16} aria-hidden="true" />
          <span>{error}</span>
        </div>
      )}
    </div>
  );
});

FormField.displayName = 'FormField';

interface SelectFieldProps {
  label: string;
  id: string;
  value: string;
  onChange: (_value: string) => void;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  description?: string;
  placeholder?: string;
}

/**
 * Accessible select field component
 */
export const SelectField: React.FC<SelectFieldProps> = ({
  label,
  id,
  value,
  onChange,
  options,
  error,
  required = false,
  disabled = false,
  className = '',
  description,
  placeholder = "Select an option..."
}) => {
  const errorId = `${id}-error`;
  const descriptionId = `${id}-description`;

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={id} className="text-sm font-medium">
        {label}
        {required && (
          <span className="text-destructive ml-1" aria-label="required">*</span>
        )}
      </Label>

      {(description?.length ?? 0) > 0 && (
        <p id={descriptionId} className="text-sm text-muted-foreground">
          {description}
        </p>
      )}

      <Select
        value={value}
        onValueChange={onChange}
        disabled={disabled}
      >
        <SelectTrigger
          id={id}
          className={cn(
            (error?.length ?? 0) > 0 && "border-destructive focus:ring-destructive"
          )}
          aria-invalid={(error?.length ?? 0) > 0 ? 'true' : 'false'}
          aria-describedby={`${(error?.length ?? 0) > 0 ? errorId : ''} ${(description?.length ?? 0) > 0 ? descriptionId : ''}`.trim()}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {(error?.length ?? 0) > 0 && (
        <div id={errorId} className="flex items-center gap-2 text-sm text-destructive" role="alert">
          <AlertCircle size={16} aria-hidden="true" />
          <span>{error}</span>
        </div>
      )}
    </div>
  );
};

interface CheckboxFieldProps {
  label: string;
  id: string;
  checked: boolean;
  onChange: (_checked: boolean) => void;
  disabled?: boolean;
  className?: string;
  description?: string;
}

/**
 * Accessible checkbox field component
 */
export const CheckboxField: React.FC<CheckboxFieldProps> = ({
  label,
  id,
  checked,
  onChange,
  disabled = false,
  className = '',
  description
}) => {
  const descriptionId = `${id}-description`;

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-start gap-3">
        <Checkbox
          id={id}
          checked={checked}
          onCheckedChange={onChange}
          disabled={disabled}
          aria-describedby={(description?.length ?? 0) > 0 ? descriptionId : undefined}
          className="mt-1"
        />
        <div className="flex-1">
          <Label
            htmlFor={id}
            className="text-sm font-medium cursor-pointer"
          >
            {label}
          </Label>
          {(description?.length ?? 0) > 0 && (
            <p id={descriptionId} className="text-sm text-muted-foreground mt-1">
              {description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};




---

// FILE: src\components\ImportDialog.tsx

/**
 * @file Simple import dialog for account files with drag-and-drop
 */
import {
  Upload,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import React, { useState, useCallback } from 'react';

import { ClipboardService } from '../services/clipboardService';
import { Button } from '../shared/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../shared/ui/dialog';

interface ImportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onImportComplete: (_result: { addedCount: number; skippedCount: number }) => void;
}

type ImportState = 'idle' | 'complete' | 'error';

/**
 * Hook for import dialog logic
 */
const useImportDialog = (
  isOpen: boolean,
  onImportComplete: (_result: { addedCount: number; skippedCount: number }) => void
): {
  state: ImportState;
  setState: (_state: ImportState) => void;
  error: string;
  setError: (_error: string) => void;
  importResult: { addedCount: number; skippedCount: number } | null;
  isDragOver: boolean;
  setIsDragOver: (_value: boolean) => void;
  handleImport: () => Promise<void>;
  handleFileImport: (_file: File) => Promise<void>;
} => {
  const [state, setState] = useState<ImportState>('idle');
  const [error, setError] = useState<string>('');
  const [importResult, setImportResult] = useState<{ addedCount: number; skippedCount: number } | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  // Reset state when dialog closes
  React.useEffect(() => {
    if (!isOpen) {
      setState('idle');
      setError('');
      setImportResult(null);
      setIsDragOver(false);
    }
  }, [isOpen]);

  const handleImport = useCallback(async (): Promise<void> => {
    setError('');

    try {
      // Get content from clipboard
      const clipboardContent = await ClipboardService.readText();
      if (clipboardContent === null || clipboardContent.length === 0) {
        setError('Could not read clipboard content');
        setState('error');
        return;
      }
      const result = await window.ipcApi.importFromFileInstant(clipboardContent);

      if (result.error !== null && result.error !== undefined && result.error.length > 0) {
        setError(result.error);
        setState('error');
        return;
      }

      setImportResult({
        addedCount: result.addedCount,
        skippedCount: result.skippedCount,
      });
      setState('complete');
      onImportComplete(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Import failed');
      setState('error');
    }
  }, [onImportComplete]);

  const handleFileImport = useCallback(async (file: File): Promise<void> => {
    setError('');

    try {
      // Read file content and use importFromFileContent
      const content = await file.text();
      const result = await window.ipcApi.importFromFileContent(content);

      if (result.error !== null && result.error !== undefined && result.error.length > 0) {
        setError(result.error);
        setState('error');
        return;
      }

      setImportResult({
        addedCount: result.addedCount,
        skippedCount: result.skippedCount,
      });
      setState('complete');
      onImportComplete(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Import failed');
      setState('error');
    }
  }, [onImportComplete]);

  return {
    state,
    setState,
    error,
    setError,
    importResult,
    isDragOver,
    setIsDragOver,
    handleImport,
    handleFileImport,
  };
};

/**
 * Idle state component for import dialog
 */
const IdleState: React.FC<{
  isDragOver: boolean;
  onImport: () => void;
}> = ({ isDragOver, onImport }) => (
  <div
    className={`text-center py-8 border-2 border-dashed rounded-lg transition-colors ${
      isDragOver
        ? 'border-blue-500 bg-blue-500/10'
        : 'border-border hover:border-blue-500/50'
    }`}
  >
    <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
    <h3 className="text-lg font-medium mb-2">Import Accounts</h3>
    <p className="text-muted-foreground mb-6 max-w-md mx-auto">
      {isDragOver
        ? 'Drop the file here to import accounts'
        : 'Drag & drop a file here or click to select. Supported formats: email:password, email;password, email|password'
      }
    </p>
    {!isDragOver && (
      <Button onClick={onImport} className="gap-2">
        <Upload size={16} />
        Select File & Import
      </Button>
    )}
  </div>
);

/**
 * Complete state component for import dialog
 */
const CompleteState: React.FC<{
  importResult: { addedCount: number; skippedCount: number } | null;
  onClose: () => void;
}> = ({ importResult, onClose }) => (
  <div className="text-center py-8">
    <CheckCircle className="mx-auto h-12 w-12 text-green-500 mb-4" />
    <h3 className="text-lg font-medium mb-2">Import Complete</h3>
    {importResult && (
      <p className="text-muted-foreground mb-6">
        Successfully imported {importResult.addedCount} accounts.
        {importResult.skippedCount > 0 && ` ${importResult.skippedCount} lines were skipped.`}
      </p>
    )}
    <Button onClick={onClose}>Close</Button>
  </div>
);

/**
 * Error state component for import dialog
 */
const ErrorState: React.FC<{
  error: string;
  onClose: () => void;
}> = ({ error, onClose }) => (
  <div className="text-center py-8">
    <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
    <h3 className="text-lg font-medium mb-2">Import Failed</h3>
    <p className="text-muted-foreground mb-6">{error}</p>
    <Button onClick={onClose}>Close</Button>
  </div>
);

export const ImportDialog: React.FC<ImportDialogProps> = ({
  isOpen,
  onClose,
  onImportComplete,
}) => {
  const {
    state,
    setState,
    error,
    setError,
    importResult,
    isDragOver,
    setIsDragOver,
    handleImport,
    handleFileImport,
  } = useImportDialog(isOpen, onImportComplete);



  // Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, [setIsDragOver]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, [setIsDragOver]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const textFile = files.find(file =>
      file.type === 'text/plain' ||
      file.name.endsWith('.txt') ||
      file.name.endsWith('.csv')
    );

    if (textFile) {
      void handleFileImport(textFile);
    } else {
      setError('Please drop a text file (.txt or .csv)');
      setState('error');
    }
  }, [handleFileImport, setIsDragOver, setError, setState]);



  const renderIdleState = (): React.JSX.Element => (
    <div
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <IdleState
        isDragOver={isDragOver}
        onImport={() => void handleImport()}
      />
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Account Import</DialogTitle>
        </DialogHeader>

        <div className="min-h-[300px]">
          {state === 'idle' && renderIdleState()}
          {state === 'complete' && (
            <CompleteState
              importResult={importResult}
              onClose={onClose}
            />
          )}
          {state === 'error' && (
            <ErrorState
              error={error}
              onClose={onClose}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};


---

// FILE: src\components\Layout.tsx

/**
 * @file The main layout component using react-resizable-panels.
 * It sets up the three-panel view for the application.
 */
import { ArrowLeft } from 'lucide-react';
import React, { useEffect, useState, useRef } from 'react';
import { Panel, PanelGroup, PanelResizeHandle, type ImperativePanelHandle } from 'react-resizable-panels';


import { useAccountStore, type AccountState } from '../shared/store/accountStore';
import { useLogStore, type LogState } from '../shared/store/logStore';
import { useMainSettingsStore } from '../shared/store/mainSettingsStore';
import { useUIStore } from '../shared/store/uiStore';
import { Button } from '../shared/ui/button';
import { ToastContainer } from '../shared/ui/Toast';
import { TopBar, TopBarSection, TopBarTitle, TopBarSearch } from '../shared/ui/top-bar';
import { TopBarAccountSection } from '../shared/ui/top-bar-account-section';

import AccountManagerPanel from './AccountManager';
import EmailListPanel from './EmailListPanel';
import EmailViewPanel from './EmailViewPanel';
import LogPanel from './LogPanel';

/**
 * Global styles for YouTube-inspired dark mode theme
 */
const GlobalStyles = (): null => {
  useEffect(() => {
    // Add dark class to body for dark theme
    document.body.classList.add('dark');

    const style = document.createElement('style');
    style.textContent = `
      /* Custom scrollbar styles */
      .custom-scrollbar::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      .custom-scrollbar::-webkit-scrollbar-track {
        background: transparent;
      }
      
      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }
      
      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.2);
      }
      
      /* Utility class to hide number input spinners */
      .hide-spin-buttons::-webkit-outer-spin-button,
      .hide-spin-buttons::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
      .hide-spin-buttons {
        -moz-appearance: textfield !important;
      }

      /* Hide number input spinners (general rule, kept as fallback) */
      input[type='number']::-webkit-outer-spin-button,
      input[type='number']::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
      input[type='number'] {
        -moz-appearance: textfield;
      }
      
      /* Selection color */
      ::selection {
        background: rgba(59, 130, 246, 0.4);
        color: white;
      }
      
      /* YouTube-style focus outlines */
      *:focus-visible {
        outline: 2px solid rgba(59, 130, 246, 0.5);
        outline-offset: 2px;
      }
    `;
    document.head.appendChild(style);
    
    return (): void => {
      style.remove();
    };
  }, []);
  
  return null;
};

/**
 * Custom resize handle with YouTube-inspired design
 */
const CustomResizeHandle = ({ direction = 'horizontal', className = '' }: { direction?: 'horizontal' | 'vertical'; className?: string }): React.JSX.Element => {
  return (
    <PanelResizeHandle
      className={`flex items-center justify-center transition-colors bg-border ${
        direction === 'horizontal'
          ? 'w-px hover:bg-primary/20'
          : 'h-px hover:bg-primary/20'
      } ${className}`}
    />
  );
};

const Layout = (): React.JSX.Element => {
  const accounts = useAccountStore((state: AccountState) => state.accounts);
  const addLog = useLogStore((state: LogState) => state.addLog);
  const { settings } = useMainSettingsStore();
  const {
    currentView,
    openSettings,
    closeSettings,
    isLeftPanelHidden,
    isSettingsOpen,
    isAccountPanelCollapsed,
    setAccountPanelCollapsed,
    isLogPanelCollapsed,
    leftPanelWidth,
    rightPanelWidth,
    logPanelHeight,
    setPanelSizes,
    loadConfig
  } = useUIStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [isConnectingAll, setIsConnectingAll] = useState(false);
  const [mainPanelWidth, setMainPanelWidth] = useState(75); // Main panel width (100% - right panel)
  const accountManagerPanelRef = useRef<ImperativePanelHandle>(null);
  const logPanelRef = useRef<ImperativePanelHandle>(null);

  // Load UI config on mount
  React.useEffect(() => {
    void loadConfig();
  }, [loadConfig]); // Include loadConfig dependency

  const handleCollapse = React.useCallback(() => {
    accountManagerPanelRef.current?.collapse();
  }, []);

  const handleExpand = React.useCallback(() => {
    accountManagerPanelRef.current?.expand();
  }, []);

  const handleLogPanelCollapse = React.useCallback(() => {
    logPanelRef.current?.collapse();
  }, []);

  const handleLogPanelExpand = React.useCallback(() => {
    logPanelRef.current?.expand();
  }, []);

  // Sync log panel state with UI store
  React.useEffect(() => {
    if (isLogPanelCollapsed) {
      handleLogPanelCollapse();
    } else {
      handleLogPanelExpand();
    }
  }, [isLogPanelCollapsed, handleLogPanelCollapse, handleLogPanelExpand]);

  const handleConnectAllAccounts = React.useCallback(() => {
    if (accounts.length === 0) {
      addLog('No accounts to connect to.', 'info');
      return;
    }

    setIsConnectingAll(true);
    try {
      addLog(`Starting to connect to ${accounts.length} accounts...`, 'info');

      // Start watching all accounts in the background
      accounts.forEach(account => {
        void window.ipcApi.watchInbox(account.id);
      });

      addLog(`Successfully started watching ${accounts.length} accounts.`, 'success');
    } catch (error) {
      addLog(`Failed to connect to accounts: ${error instanceof Error ? error.message : String(error)}`, 'error');
    } finally {
      setIsConnectingAll(false);
    }
  }, [accounts, addLog]);

  const handleToggleAccountPanel = React.useCallback(() => {
    if (isAccountPanelCollapsed) {
      handleExpand();
    } else {
      handleCollapse();
    }
  }, [isAccountPanelCollapsed, handleExpand, handleCollapse]);

  // Handle panel resize - optimized for performance during drag
  const handleLeftPanelResize = React.useCallback((size: number) => {
    // Minimal operations during resize for smooth dragging
    setPanelSizes({ leftPanelWidth: size });
  }, [setPanelSizes]);

  const handleRightPanelResize = React.useCallback((size: number) => {
    // Update main panel width for layout calculations
    setMainPanelWidth(100 - size);
    setPanelSizes({ rightPanelWidth: size });
  }, [setPanelSizes, setMainPanelWidth]);

  const handleLogPanelResize = React.useCallback((size: number) => {
    // Minimal operations during resize for smooth dragging
    setPanelSizes({ logPanelHeight: size });
  }, [setPanelSizes]);

  // Get current view title
  const getViewTitle = React.useMemo(() => {
    switch (currentView) {
      case 'settings':
        return 'Settings';
      case 'email':
      default:
        return 'IMAP Viewer';
    }
  }, [currentView]);

  return (
    <div className="h-screen w-screen flex flex-col overflow-hidden bg-background text-foreground">
      <GlobalStyles />

      {/* Skip to main content link for accessibility */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>

      {/* Top Bar */}
      <TopBar>
        <TopBarSection
          side="left"
          width={currentView === 'settings' ? "auto" : (isLeftPanelHidden ? "0%" : `${(leftPanelWidth * mainPanelWidth) / 100 - 2}%`)}
          className="pl-1 pr-1"
        >
          {currentView === 'settings' ? (
            <Button
              variant="ghost"
              size="icon"
              onClick={closeSettings}
              className="rounded-full h-9 w-9"
            >
              <ArrowLeft size={18} />
            </Button>
          ) : (
            <TopBarSearch
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder="Search..."
            />
          )}
        </TopBarSection>

        <TopBarSection
          side="center"
          width={currentView === 'settings' ? "1fr" : (isLeftPanelHidden ? `${mainPanelWidth + 3}%` : `${mainPanelWidth - (leftPanelWidth * mainPanelWidth) / 100 + 3}%`)}
        >
          <TopBarTitle size="md">{getViewTitle}</TopBarTitle>
        </TopBarSection>

        <TopBarSection side="right" width={`${rightPanelWidth - 1}%`} className="pl-4 pr-1">
          <TopBarAccountSection
            accountCount={accounts.length}
            isCollapsed={isAccountPanelCollapsed}
            isConnectingAll={isConnectingAll}
            isSettingsOpen={isSettingsOpen}
            onSettings={isSettingsOpen ? closeSettings : openSettings}
            onToggleCollapse={handleToggleAccountPanel}
            onConnectAll={handleConnectAllAccounts}
          />
        </TopBarSection>
      </TopBar>

      <div className="flex-1 overflow-hidden">
        <PanelGroup
          direction="horizontal"
          aria-label="Main application layout"
          storage={{
            getItem: (name: string) => {
              const value = localStorage.getItem(`panel-layout-${name}`);
              return value !== null && value.length > 0 ? JSON.parse(value) : null;
            },
            setItem: (name: string, value: unknown) => {
              localStorage.setItem(`panel-layout-${name}`, JSON.stringify(value));
            }
          }}
        >
        <Panel id="main-panel" order={1}>
          <PanelGroup
            direction="vertical"
            storage={{
              getItem: (name: string) => {
                const value = localStorage.getItem(`panel-layout-vertical-${name}`);
                return value !== null && value.length > 0 ? JSON.parse(value) : null;
              },
              setItem: (name: string, value: unknown) => {
                localStorage.setItem(`panel-layout-vertical-${name}`, JSON.stringify(value));
              }
            }}
          >
            <Panel id="content-panel" order={1}>
              <PanelGroup
                direction="horizontal"
                storage={{
                  getItem: (name: string) => {
                    const value = localStorage.getItem(`panel-layout-content-${name}`);
                    return value !== null && value.length > 0 ? JSON.parse(value) : null;
                  },
                  setItem: (name: string, value: unknown) => {
                    localStorage.setItem(`panel-layout-content-${name}`, JSON.stringify(value));
                  }
                }}
              >
                {!isLeftPanelHidden && (
                  <>
                    <Panel
                      id="left-panel"
                      order={1}
                      defaultSize={leftPanelWidth}
                      minSize={20}
                      className="border-r border-border"
                      onResize={handleLeftPanelResize}
                    >
                      <EmailListPanel searchQuery={searchQuery} />
                    </Panel>
                    <CustomResizeHandle direction="horizontal" />
                  </>
                )}
                <Panel id="email-view-panel" order={2} minSize={30}>
                  <EmailViewPanel searchQuery={searchQuery} />
                </Panel>
              </PanelGroup>
            </Panel>
            {!settings.hideEventLogger && (
              <>
                <CustomResizeHandle direction="vertical" />
                <Panel
                  id="log-panel"
                  order={2}
                  ref={logPanelRef}
                  defaultSize={logPanelHeight}
                  minSize={10}
                  collapsible
                  collapsedSize={8}
                  className="border border-border"
                  onResize={handleLogPanelResize}
                  onCollapse={() => {
                    // Panel collapsed by user interaction
                  }}
                  onExpand={() => {
                    // Panel expanded by user interaction
                  }}
                >
                  <LogPanel />
                </Panel>
              </>
            )}
          </PanelGroup>
        </Panel>
        <CustomResizeHandle direction="horizontal" />
        <Panel
          id="account-panel"
          ref={accountManagerPanelRef}
          defaultSize={rightPanelWidth}
          minSize={15}
          maxSize={40}
          collapsible
          collapsedSize={7}
          onCollapse={() => setAccountPanelCollapsed(true)}
          onExpand={() => setAccountPanelCollapsed(false)}
          onResize={handleRightPanelResize}
          order={2}
        >
          <div className="h-full w-full">
            <AccountManagerPanel
              collapsed={isAccountPanelCollapsed}
            />
          </div>
        </Panel>
      </PanelGroup>
      </div>

      {/* Toast notifications */}
      <ToastContainer />
    </div>
  );
};

export default Layout;


---

// FILE: src\components\LoadingSpinner.tsx

/**
 * @file Loading spinner component with accessibility features using ShadCN UI
 */
import { Loader2 } from 'lucide-react';
import React from 'react';

import { Skeleton } from '../shared/ui';
import { Card, CardContent } from '../shared/ui/card';
import { cn } from '../shared/utils/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'muted' | 'primary';
  text?: string;
  className?: string;
  fullScreen?: boolean;
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12'
};

const variantClasses = {
  default: 'text-foreground',
  muted: 'text-muted-foreground',
  primary: 'text-primary'
};

/**
 * Accessible loading spinner component
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  text,
  className = '',
  fullScreen = false
}) => {
  const spinner = (
    <div className={cn("flex items-center justify-center gap-3", className)}>
      <Loader2
        className={cn(
          sizeClasses[size],
          variantClasses[variant],
          "animate-spin"
        )}
        aria-hidden="true"
      />
      {(text?.length ?? 0) > 0 && (
        <span className={cn("text-sm", variantClasses[variant])}>
          {text}
        </span>
      )}
      <span className="sr-only">Loading...</span>
    </div>
  );

  if (fullScreen) {
    return (
      <div
        className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50"
        role="status"
        aria-label="Loading"
      >
        <Card className="p-6">
          <CardContent className="p-0">
            {spinner}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div role="status" aria-label="Loading">
      {spinner}
    </div>
  );
};

/**
 * Loading overlay for specific components
 */
export const LoadingOverlay: React.FC<{
  isLoading: boolean;
  children: React.ReactNode;
  text?: string;
  className?: string;
}> = ({ isLoading, children, text = 'Loading...', className = '' }) => {
  return (
    <div className={cn("relative", className)}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-background/60 backdrop-blur-sm flex items-center justify-center z-10">
          <LoadingSpinner text={text} variant="primary" />
        </div>
      )}
    </div>
  );
};

/**
 * Skeleton loader for content placeholders
 */
export const SkeletonLoader: React.FC<{
  lines?: number;
  className?: string;
  showAvatar?: boolean;
  showButton?: boolean;
}> = ({ lines = 3, className = '', showAvatar = false, showButton = false }) => {
  return (
    <div className={cn("space-y-3", className)} aria-label="Loading content">
      {showAvatar && (
        <div className="flex items-center space-x-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-3 w-24" />
          </div>
        </div>
      )}

      {Array.from({ length: lines }, (_, index) => (
        <Skeleton
          key={`skeleton-${Date.now()}-${lines}-${index}`}
          className={cn(
            "h-4",
            index === lines - 1 ? 'w-3/4' : 'w-full'
          )}
        />
      ))}

      {showButton && (
        <Skeleton className="h-9 w-24 mt-4" />
      )}
    </div>
  );
};

export default LoadingSpinner;


---

// FILE: src\components\LogPanel.tsx

/**
 * @file A persistent sidebar panel for displaying application logs.
 */
import {
  Trash2, Terminal,
  RefreshCw, ChevronUp, ChevronDown
} from 'lucide-react';
import React from 'react';

import { useProxyStatus } from '../shared/hooks/useProxyStatus';
import { useLogStore, type LogType } from '../shared/store/logStore';
import { useUIStore } from '../shared/store/uiStore';
import { Button } from '../shared/ui/button';

const logColors: Record<LogType | 'default', { text: string, dot: string }> = {
  success: {
    text: 'text-green-400',
    dot: 'bg-green-500'
  },
  error: {
    text: 'text-destructive',
    dot: 'bg-destructive'
  },
  info: {
    text: 'text-primary',
    dot: 'bg-primary'
  },
  warn: {
    text: 'text-yellow-400',
    dot: 'bg-yellow-500'
  },
  default: {
    text: 'text-gray-400',
    dot: 'bg-gray-500'
  }
};

const ProxyStatusIndicator = (): React.JSX.Element => {
  const {
    status,
    statusInfo,
    label,
    tooltip,
    textColor,
    handleRefresh,
  } = useProxyStatus();

  const { Icon, color } = statusInfo;

  return (
    <div className="flex items-center gap-1.5 group" title={tooltip}>
      <div className="h-2 w-px bg-gray-700" />
      <Icon size={14} className={`${color} ${status === 'connecting' ? 'animate-spin' : ''}`} />
      <span className={`text-xs font-medium ${textColor} truncate max-w-[180px]`}>{label}</span>
      
      {status !== 'disabled' && (
        <button 
          onClick={handleRefresh} 
          disabled={status === 'connecting'}
          className="p-0.5 rounded-full text-gray-400 hover:text-white hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed opacity-0 group-hover:opacity-100 transition-opacity"
          title="Re-check proxy status"
        >
          <RefreshCw size={12} />
        </button>
      )}
    </div>
  );
};

/**
 * Compact time formatter - returns only hours:minutes:seconds
 */
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false });
};

/**
 * Log panel component that displays application events and messages in a compact format
 */
const LogPanel = (): React.JSX.Element => {
  const { logs, clearLogs } = useLogStore();
  const { isLogPanelCollapsed, toggleLogPanel } = useUIStore();
  const scrollRef = React.useRef<HTMLDivElement>(null);

  const filteredLogs = logs.filter(
    (log) => !log.message.startsWith('[IMAP')
  );

  React.useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [logs]);

  return (
    <div className="h-full flex flex-col bg-background text-foreground w-full">
      <div className="flex items-center justify-between py-1 px-2 border-b border-border">
        <div className="flex items-center gap-1.5">
          <Terminal size={14} className="text-muted-foreground" />
          <h3 className="text-xs font-medium">Event Log</h3>
          <ProxyStatusIndicator />
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleLogPanel}
            title={isLogPanelCollapsed ? "Expand log panel" : "Collapse log panel"}
            className="h-6 w-6 rounded-full text-muted-foreground hover:text-foreground hover:bg-muted/50"
          >
            {isLogPanelCollapsed ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={clearLogs}
            title="Clear logs"
            className="h-6 w-6 rounded-full text-destructive hover:text-destructive hover:bg-destructive/10"
          >
            <Trash2 size={14} />
          </Button>
        </div>
      </div>

      <div
        ref={scrollRef}
        className="flex-grow px-1 py-0.5 overflow-y-auto custom-scrollbar font-mono text-xs space-y-0.5"
      >
        {filteredLogs.length === 0 ? (
          <div className="text-gray-500 italic p-1 text-center text-xs">
            No events recorded yet.
          </div>
        ) : (
          filteredLogs.map((log) => {
            const colors = logColors[log.type] || logColors.default;
            const time = formatTime(log.timestamp);
            
            return (
              <div 
                key={log.id} 
                className="flex items-center gap-1.5 py-0.5 px-1 hover:bg-white/5 rounded group"
              >
                <div className={`w-1.5 h-1.5 rounded-full ${colors.dot} flex-shrink-0`} />
                <span className="text-gray-400 text-[10px] flex-shrink-0 opacity-70 group-hover:opacity-100">{time}</span>
                <p className={`text-gray-200 text-[11px] ${log.type === 'error' ? '' : 'truncate'}`}>
                  {log.message}
                </p>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default LogPanel; 

---

// FILE: src\components\SettingsPanel\MainSettings.tsx

/**
 * @file Main application settings component
 */
import { Eye, LogIn, Settings } from 'lucide-react';
import React from 'react';

import { useMainSettingsStore } from '../../shared/store/mainSettingsStore';
import { Button } from '../../shared/ui/button';
import { Card, CardContent } from '../../shared/ui/card';
import { Tooltip } from '../../shared/ui/tooltip';
import { cn } from '../../shared/utils/utils';

interface MainSettingsProps {
  className?: string;
}

/**
 * Interface settings section
 */
const InterfaceSettings: React.FC = () => {
  const { settings, setHideEventLogger, setCompactAccountView } = useMainSettingsStore();

  return (
    <div className="space-y-3 p-2 md:p-3 lg:p-4 rounded-lg border border-border/50">
      <h5 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Interface</h5>

      <div className="space-y-2">
        <Tooltip content="Completely hide the event logger panel from the interface">
          <Button
            type="button"
            onClick={() => setHideEventLogger(!settings.hideEventLogger)}
            variant={settings.hideEventLogger ? 'default' : 'outline'}
            size="sm"
            className="w-full gap-1.5 h-7 text-xs justify-start"
          >
            <Eye size={12} />
            Hide Event Logger
          </Button>
        </Tooltip>

        <Tooltip content="Show accounts in compact view with reduced height">
          <Button
            type="button"
            onClick={() => setCompactAccountView(!settings.compactAccountView)}
            variant={settings.compactAccountView ? 'default' : 'outline'}
            size="sm"
            className="w-full gap-1.5 h-7 text-xs justify-start"
          >
            <Eye size={12} />
            Compact Account View
          </Button>
        </Tooltip>
      </div>
    </div>
  );
};

/**
 * Account settings section
 */
const AccountSettings: React.FC = () => {
  const { settings, setAutoLoginOnStartup } = useMainSettingsStore();

  return (
    <div className="space-y-3 p-2 md:p-3 lg:p-4 rounded-lg border border-border/50">
      <h5 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Accounts</h5>

      <div className="space-y-2">
        <Tooltip content="Automatically connect to all accounts when the application starts">
          <Button
            type="button"
            onClick={() => setAutoLoginOnStartup(!settings.autoLoginOnStartup)}
            variant={settings.autoLoginOnStartup ? 'default' : 'outline'}
            size="sm"
            className="w-full gap-1.5 h-7 text-xs justify-start"
          >
            <LogIn size={12} />
            Auto-Login on Startup
          </Button>
        </Tooltip>
      </div>
    </div>
  );
};

/**
 * Main settings component for core application configuration
 */
const MainSettings: React.FC<MainSettingsProps> = ({ className }) => {
  return (
    <div className={cn("flex flex-col h-full p-4 max-w-2xl mx-auto", className)}>
      <Card className="mb-4">
        <CardContent className="px-3 md:px-4 lg:px-6 pt-1 pb-6">
          <h4 className="text-sm font-medium mb-3 flex items-center justify-center gap-2">
            <Settings size={16} className="text-muted-foreground" />
            Main Settings
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            <InterfaceSettings />
            <AccountSettings />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MainSettings;


---

// FILE: src\components\SettingsPanel\ProxyAddForm.tsx

/**
 * @file Proxy add form component
 */
import React from 'react';
import type { UseFormReturn } from 'react-hook-form';

import type { ProxyFormData } from '../../shared/hooks/useProxyManager';
import { Card, CardContent } from '../../shared/ui/card';

import { AddProxyButton, AuthInputs, HostPortInputs, ProxyTypeSelector } from './ProxyAddFormComponents';

interface ProxyAddFormProps {
  form: UseFormReturn<ProxyFormData>;
  quickProxyType: 'http' | 'https' | 'socks4' | 'socks5';
  setQuickProxyType: (_type: 'http' | 'https' | 'socks4' | 'socks5') => void;
  handleAddProxy: (_data: ProxyFormData & { type?: 'http' | 'https' | 'socks4' | 'socks5' }) => void;
}

/**
 * Form component for adding new proxies
 */
export const ProxyAddForm: React.FC<ProxyAddFormProps> = ({
  form,
  quickProxyType,
  setQuickProxyType,
  handleAddProxy
}) => {
  return (
    <Card className="mb-4">
      <CardContent className="p-4 space-y-4">
        {/* First Row: Type Selection + Add Button */}
        <div className="flex items-center justify-between gap-4">
          <ProxyTypeSelector
            quickProxyType={quickProxyType}
            setQuickProxyType={setQuickProxyType}
          />
          <AddProxyButton
            form={form}
            quickProxyType={quickProxyType}
            handleAddProxy={handleAddProxy}
          />
        </div>

        {/* Second Row: Host and Port */}
        <HostPortInputs form={form} />

        {/* Third Row: Username and Password */}
        <AuthInputs form={form} />
      </CardContent>
    </Card>
  );
};

export default ProxyAddForm;


---

// FILE: src\components\SettingsPanel\ProxyAddFormComponents.tsx

/**
 * @file Proxy add form sub-components
 */
import { Plus } from 'lucide-react';
import React from 'react';
import type { UseFormReturn } from 'react-hook-form';

import type { ProxyFormData } from '../../shared/hooks/useProxyManager';
import { Button } from '../../shared/ui/button';
import { Input } from '../../shared/ui/input';
import { Label } from '../../shared/ui/label';
import { ToggleGroup, ToggleGroupItem } from '../../shared/ui/toggle-group';
import { Tooltip } from '../../shared/ui/tooltip';

/**
 * Proxy type selection component
 */
export const ProxyTypeSelector: React.FC<{
  quickProxyType: 'http' | 'https' | 'socks4' | 'socks5';
  setQuickProxyType: (type: 'http' | 'https' | 'socks4' | 'socks5') => void;
}> = ({ quickProxyType, setQuickProxyType }) => (
  <div className="flex-1">
    <Label className="text-xs text-muted-foreground mb-2 block">Proxy Type</Label>
    <ToggleGroup
      type="single"
      value={quickProxyType}
      onValueChange={(value) => {
        if (value && ['http', 'https', 'socks4', 'socks5'].includes(value)) {
          setQuickProxyType(value as 'http' | 'https' | 'socks4' | 'socks5');
        }
      }}
      className="justify-start"
    >
      <ToggleGroupItem value="socks5" variant="outline" size="default" className="px-4 h-9">
        SOCKS5
      </ToggleGroupItem>
      <ToggleGroupItem value="socks4" variant="outline" size="default" className="px-4 h-9">
        SOCKS4
      </ToggleGroupItem>
      <ToggleGroupItem value="http" variant="outline" size="default" className="px-4 h-9">
        HTTP
      </ToggleGroupItem>
      <ToggleGroupItem value="https" variant="outline" size="default" className="px-4 h-9">
        HTTPS
      </ToggleGroupItem>
    </ToggleGroup>
  </div>
);

/**
 * Add proxy button component
 */
export const AddProxyButton: React.FC<{
  form: UseFormReturn<ProxyFormData>;
  quickProxyType: 'http' | 'https' | 'socks4' | 'socks5';
  handleAddProxy: (data: ProxyFormData & { type?: 'http' | 'https' | 'socks4' | 'socks5' }) => void;
}> = ({ form, quickProxyType, handleAddProxy }) => {
  const handleClick = async (e: React.MouseEvent): Promise<void> => {
    e.preventDefault();
    e.stopPropagation();

    const isValid = await form.trigger();
    if (isValid) {
      const data = form.getValues();
      handleAddProxy({...data, type: quickProxyType});
    }
  };

  return (
    <Tooltip content="Add new proxy with current settings">
      <Button
        type="button"
        onClick={(e) => void handleClick(e)}
        size="sm"
        className="h-9 px-4 gap-2 mt-6"
      >
        <Plus size={16} />
        Add Proxy
      </Button>
    </Tooltip>
  );
};

/**
 * Host and port input fields
 */
export const HostPortInputs: React.FC<{
  form: UseFormReturn<ProxyFormData>;
}> = ({ form }) => (
  <div className="grid grid-cols-2 gap-3">
    <div>
      <Label htmlFor="quick-host" className="text-xs text-muted-foreground">Host</Label>
      <Input
        id="quick-host"
        placeholder="proxy.example.com"
        {...form.register('host')}
        className="h-9 text-sm"
      />
    </div>
    <div>
      <Label htmlFor="quick-port" className="text-xs text-muted-foreground">Port</Label>
      <Input
        id="quick-port"
        type="number"
        placeholder="1080"
        {...form.register('port', { valueAsNumber: true })}
        className="h-9 text-sm"
      />
    </div>
  </div>
);

/**
 * Username and password input fields
 */
export const AuthInputs: React.FC<{
  form: UseFormReturn<ProxyFormData>;
}> = ({ form }) => (
  <div className="grid grid-cols-2 gap-3">
    <div>
      <Label htmlFor="quick-username" className="text-xs text-muted-foreground">Username (Optional)</Label>
      <Input
        id="quick-username"
        placeholder="username"
        {...form.register('username')}
        className="h-9 text-sm"
      />
    </div>
    <div>
      <Label htmlFor="quick-password" className="text-xs text-muted-foreground">Password (Optional)</Label>
      <Input
        id="quick-password"
        type="password"
        placeholder="password"
        {...form.register('password')}
        className="h-9 text-sm"
      />
    </div>
  </div>
);


---

// FILE: src\components\SettingsPanel\ProxyAdvancedSettings.tsx

/**
 * @file Proxy advanced settings component
 */
import { Shield } from 'lucide-react';
import React from 'react';

import { Card, CardContent } from '../../shared/ui/card';

import { ConnectionSettings, ListManagement, SourceManagement } from './ProxyAdvancedSettingsComponents';

interface ProxyAdvancedSettingsProps {
  // Connection settings
  useRandomProxy: boolean;
  setUseRandomProxy: (_value: boolean) => void;
  maxRetries: number;
  setMaxRetries: (_value: number) => void;

  // Source settings
  randomizeSource: boolean;
  setRandomizeSource: (_value: boolean) => void;
  sourceUrl: string;
  setSourceUrl: (_value: string) => void;
  autoUpdateEnabled: boolean;
  setAutoUpdateEnabled: (_value: boolean) => void;
  updateInterval: number;
  setUpdateInterval: (_value: number) => void;

  // List management
  showImport: boolean;
  setShowImport: (_value: boolean) => void;
  handleTestAllProxies: () => Promise<void>;
  handleExport: () => void;
  isLoading: boolean;
  proxiesCount: number;
}

/**
 * Advanced settings component for proxy configuration
 */
export const ProxyAdvancedSettings: React.FC<ProxyAdvancedSettingsProps> = ({
  useRandomProxy,
  setUseRandomProxy,
  maxRetries,
  setMaxRetries,
  randomizeSource,
  setRandomizeSource,
  sourceUrl,
  setSourceUrl,
  autoUpdateEnabled,
  setAutoUpdateEnabled,
  updateInterval,
  setUpdateInterval,
  showImport,
  setShowImport,
  handleTestAllProxies,
  handleExport,
  isLoading,
  proxiesCount
}) => {
  return (
    <Card className="mb-4">
      <CardContent className="px-3 md:px-4 lg:px-6 pt-1 pb-6">
        <h4 className="text-sm font-medium mb-3 flex items-center justify-center gap-2">
          <Shield size={16} className="text-muted-foreground" />
          Advanced Settings
        </h4>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
          <ConnectionSettings
            useRandomProxy={useRandomProxy}
            setUseRandomProxy={setUseRandomProxy}
            maxRetries={maxRetries}
            setMaxRetries={setMaxRetries}
          />

          <SourceManagement
            randomizeSource={randomizeSource}
            setRandomizeSource={setRandomizeSource}
            sourceUrl={sourceUrl}
            setSourceUrl={setSourceUrl}
            autoUpdateEnabled={autoUpdateEnabled}
            setAutoUpdateEnabled={setAutoUpdateEnabled}
            updateInterval={updateInterval}
            setUpdateInterval={setUpdateInterval}
          />

          <ListManagement
            showImport={showImport}
            setShowImport={setShowImport}
            handleTestAllProxies={handleTestAllProxies}
            handleExport={handleExport}
            isLoading={isLoading}
            proxiesCount={proxiesCount}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default ProxyAdvancedSettings;


---

// FILE: src\components\SettingsPanel\ProxyAdvancedSettingsComponents.tsx

/**
 * @file Proxy advanced settings sub-components
 */
import { Download, RefreshCw, RotateCcw, Shield, Upload } from 'lucide-react';
import React from 'react';

import { Button } from '../../shared/ui/button';
import { Input } from '../../shared/ui/input';
import { Tooltip } from '../../shared/ui/tooltip';

/**
 * Connection settings section
 */
export const ConnectionSettings: React.FC<{
  useRandomProxy: boolean;
  setUseRandomProxy: (value: boolean) => void;
  maxRetries: number;
  setMaxRetries: (value: number) => void;
}> = ({ useRandomProxy, setUseRandomProxy, maxRetries, setMaxRetries }) => (
  <div className="space-y-3 p-2 md:p-3 lg:p-4 rounded-lg border border-border/50">
    <h5 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Connection</h5>

    <div className="space-y-2">
      <Tooltip content="Use random proxy for each connection attempt">
        <Button
          type="button"
          onClick={() => setUseRandomProxy(!useRandomProxy)}
          variant={useRandomProxy ? 'default' : 'outline'}
          size="sm"
          className="w-full gap-1.5 h-7 text-xs justify-start"
        >
          <Shield size={12} />
          Random
        </Button>
      </Tooltip>

      <Tooltip content="Number of retry attempts before giving up">
        <div className="flex items-center gap-2">
          <label className="text-xs text-muted-foreground flex-shrink-0">Max Retries:</label>
          <Input
            type="number"
            min="1"
            max="10"
            value={maxRetries}
            onChange={(e) => setMaxRetries(parseInt(e.target.value) || 3)}
            className="h-7 w-16 text-xs text-center"
            placeholder="3"
          />
        </div>
      </Tooltip>
    </div>
  </div>
);

/**
 * Source management section
 */
export const SourceManagement: React.FC<{
  randomizeSource: boolean;
  setRandomizeSource: (value: boolean) => void;
  sourceUrl: string;
  setSourceUrl: (value: string) => void;
  autoUpdateEnabled: boolean;
  setAutoUpdateEnabled: (value: boolean) => void;
  updateInterval: number;
  setUpdateInterval: (value: number) => void;
}> = ({ 
  randomizeSource, 
  setRandomizeSource, 
  sourceUrl, 
  setSourceUrl, 
  autoUpdateEnabled, 
  setAutoUpdateEnabled, 
  updateInterval, 
  setUpdateInterval 
}) => (
  <div className="space-y-3 p-2 md:p-3 lg:p-4 rounded-lg border border-border/50">
    <h5 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Source</h5>

    <div className="space-y-2">
      <Tooltip content="Load proxies from external source URL">
        <Button
          type="button"
          onClick={() => setRandomizeSource(!randomizeSource)}
          variant={randomizeSource ? 'default' : 'outline'}
          size="sm"
          className="w-full gap-1.5 h-7 text-xs justify-start"
        >
          <RotateCcw size={12} />
          Randomize
        </Button>
      </Tooltip>

      {randomizeSource && (
        <div className="space-y-1">
          <Tooltip content="URL to fetch proxy list from">
            <Input
              placeholder="https://example.com/proxy-list.txt"
              value={sourceUrl}
              onChange={(e) => setSourceUrl(e.target.value)}
              className="h-7 text-xs"
            />
          </Tooltip>
        </div>
      )}

      <Tooltip content="Automatically update proxy list at regular intervals">
        <Button
          type="button"
          onClick={() => setAutoUpdateEnabled(!autoUpdateEnabled)}
          variant={autoUpdateEnabled ? 'default' : 'outline'}
          size="sm"
          className="w-full gap-1.5 h-7 text-xs justify-start"
        >
          <RefreshCw size={12} />
          Auto Update
        </Button>
      </Tooltip>

      {autoUpdateEnabled && (
        <Tooltip content="Update interval in minutes (1-1440)">
          <div className="flex items-center gap-2">
            <label className="text-xs text-muted-foreground flex-shrink-0">Interval:</label>
            <Input
              type="number"
              min="1"
              max="1440"
              value={updateInterval}
              onChange={(e) => setUpdateInterval(parseInt(e.target.value) || 30)}
              className="h-7 w-16 text-xs text-center"
              placeholder="30"
            />
            <span className="text-xs text-muted-foreground">min</span>
          </div>
        </Tooltip>
      )}
    </div>
  </div>
);

/**
 * List management section
 */
export const ListManagement: React.FC<{
  showImport: boolean;
  setShowImport: (value: boolean) => void;
  handleTestAllProxies: () => Promise<void>;
  handleExport: () => void;
  isLoading: boolean;
  proxiesCount: number;
}> = ({ showImport, setShowImport, handleTestAllProxies, handleExport, isLoading, proxiesCount }) => (
  <div className="space-y-3 p-2 md:p-3 lg:p-4 rounded-lg border border-border/50">
    <h5 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">List Management</h5>

    <div className="space-y-2">
      <Tooltip content="Import proxies from text or file">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setShowImport(!showImport)}
          className="w-full gap-1.5 h-7 text-xs justify-start"
        >
          <Upload size={12} />
          Import
        </Button>
      </Tooltip>

      <Tooltip content="Test connectivity of all proxies">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => {
            void handleTestAllProxies();
          }}
          disabled={isLoading || proxiesCount === 0}
          className="w-full gap-1.5 h-7 text-xs justify-start"
        >
          <RefreshCw size={12} className={isLoading ? 'animate-spin' : ''} />
          Test All
        </Button>
      </Tooltip>

      <Tooltip content="Export proxy list to file">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleExport}
          disabled={proxiesCount === 0}
          className="w-full gap-1.5 h-7 text-xs justify-start"
        >
          <Download size={12} />
          Export
        </Button>
      </Tooltip>
    </div>
  </div>
);


---

// FILE: src\components\SettingsPanel\ProxyImportPanel.tsx

/**
 * @file Proxy import panel component
 */
import React from 'react';

import { Button } from '../../shared/ui/button';
import { Card, CardContent } from '../../shared/ui/card';

interface ProxyImportPanelProps {
  showImport: boolean;
  importText: string;
  setImportText: (text: string) => void;
  setShowImport: (show: boolean) => void;
  handleImport: () => Promise<void>;
}

/**
 * Panel component for importing proxy lists
 */
export const ProxyImportPanel: React.FC<ProxyImportPanelProps> = ({
  showImport,
  importText,
  setImportText,
  setShowImport,
  handleImport
}) => {
  if (!showImport) {
    return null;
  }

  return (
    <Card className="mb-4">
      <CardContent className="p-3">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-sm font-medium">Import Proxies</h4>
          <span className="text-xs text-muted-foreground">
            {importText.split('\n').filter(line => line.trim() && !line.startsWith('#')).length} proxies detected
          </span>
        </div>
        <textarea
          className="w-full min-h-[80px] px-3 py-2 text-sm rounded border border-input bg-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring resize-none"
          placeholder="type:host:port:username:password (one per line)&#10;socks5:***********:1080:user:pass&#10;http:proxy.example.com:8080::"
          value={importText}
          onChange={(e) => setImportText(e.target.value)}
        />
        <div className="flex justify-end gap-2 mt-2">
          <Button type="button" variant="outline" size="sm" onClick={() => setShowImport(false)}>
            Cancel
          </Button>
          <Button 
            type="button" 
            size="sm" 
            onClick={() => {
              void handleImport();
            }} 
            disabled={!importText.trim()}
          >
            Import
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProxyImportPanel;


---

// FILE: src\components\SettingsPanel\ProxyList.tsx

/**
 * @file Proxy list component
 */
import { Globe, RefreshCw, Trash2 } from 'lucide-react';
import React from 'react';

import { Badge } from '../../shared/ui/badge';
import { Button } from '../../shared/ui/button';
import { Card, CardContent } from '../../shared/ui/card';

interface ProxyItem {
  host: string;
  port: number;
  username?: string;
  password?: string;
  type?: 'http' | 'https' | 'socks4' | 'socks5';
}

interface TestResult {
  success: boolean;
  error?: string;
  timestamp: number;
}

interface ProxyListProps {
  proxies: ProxyItem[];
  testResults: Record<number, TestResult>;
  isTesting: Record<number, boolean>;
  handleTestProxy: (index: number) => Promise<void>;
  handleDeleteProxy: (index: number) => void;
}

/**
 * Component for displaying and managing the proxy list
 */
export const ProxyList: React.FC<ProxyListProps> = ({
  proxies,
  testResults,
  isTesting,
  handleTestProxy,
  handleDeleteProxy
}) => {
  if (proxies.length === 0) {
    return (
      <Card className="flex-1 min-h-0">
        <CardContent className="p-0 h-full">
          <div className="text-center text-muted-foreground py-12">
            <Globe className="w-16 h-16 mx-auto mb-4 opacity-30" />
            <p className="font-medium mb-2">No proxies configured</p>
            <p className="text-sm">Add a proxy above or import a list to get started</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="flex-1 min-h-0">
      <CardContent className="p-0 h-full">
        <div className="h-full overflow-hidden">
          <div className="p-3 border-b border-border bg-muted/20">
            <div className="text-xs font-medium text-muted-foreground">
              Proxy List ({proxies.length})
            </div>
          </div>
          <div className="overflow-y-auto h-full p-3 space-y-2">
            {proxies.map((proxy, index) => (
              <div
                key={`proxy-${proxy.host}-${proxy.port}`}
                className="flex items-center gap-3 p-2 border border-border rounded-md hover:bg-muted/30 transition-colors group"
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="font-mono text-sm font-medium">{proxy.host}:{proxy.port}</span>
                    <Badge variant="secondary" className="text-xs px-1.5 py-0.5 h-5">
                      {proxy.type?.toUpperCase() ?? 'SOCKS5'}
                    </Badge>
                    {(proxy.username?.length ?? 0) > 0 && (
                      <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-5">
                        Auth
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-1 mt-1">
                    {testResults[index]?.success && (
                      <Badge variant="default" className="text-xs px-1.5 py-0.5 h-5 bg-green-500 hover:bg-green-600">
                        ✓ Working
                      </Badge>
                    )}
                    {testResults[index]?.success === false && (
                      <Badge variant="destructive" className="text-xs px-1.5 py-0.5 h-5">
                        ✗ Failed
                      </Badge>
                    )}
                    {testResults[index]?.timestamp && (
                      <span className="text-xs text-muted-foreground">
                        {new Date(testResults[index].timestamp).toLocaleTimeString()}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      void handleTestProxy(index);
                    }}
                    disabled={isTesting[index]}
                    className="h-7 w-7 p-0"
                    title="Test proxy"
                  >
                    <RefreshCw size={12} className={isTesting[index] ? 'animate-spin' : ''} />
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteProxy(index)}
                    className="h-7 w-7 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                    title="Delete proxy"
                  >
                    <Trash2 size={12} />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProxyList;


---

// FILE: src\components\SettingsPanel\ProxySettings.tsx

/**
 * @file Modern proxy settings component with intuitive interface for all use cases.
 */
import React, { useState } from 'react';

import { useProxyManager } from '../../shared/hooks/useProxyManager';
import { useProxyStatus } from '../../shared/hooks/useProxyStatus';
import { cn } from '../../shared/utils/utils';

import ProxyAddForm from './ProxyAddForm';
import ProxyAdvancedSettings from './ProxyAdvancedSettings';
import ProxyImportPanel from './ProxyImportPanel';
import ProxyList from './ProxyList';
import ProxyStatusHeader from './ProxyStatusHeader';


interface ProxySettingsProps {
  className?: string;
}

/**
 * Main proxy settings component with modular architecture
 */
const ProxySettings: React.FC<ProxySettingsProps> = ({ className }) => {
  const [importText, setImportText] = useState('');
  const [showImport, setShowImport] = useState(false);
  const [quickProxyType, setQuickProxyType] = useState<'http' | 'https' | 'socks4' | 'socks5'>('socks5');

  const {
    form,
    handleAddProxy,
    handleDeleteProxy,
    handleTestProxy,
    handleTestAllProxies,
    handleImportProxies,
    handleExportProxies,
    enableProxies,
    setEnableProxies,
    randomizeSource,
    setRandomizeSource,
    sourceUrl,
    setSourceUrl,
    autoUpdateEnabled,
    setAutoUpdateEnabled,
    updateInterval,
    setUpdateInterval,
    maxRetries,
    setMaxRetries,
    useRandomProxy,
    setUseRandomProxy,
    isLoading,
    isTesting,
    proxies,
    testResults,
  } = useProxyManager();

  const { error } = useProxyStatus();

  const handleImport = async (): Promise<void> => {
    if (!importText.trim()) return;
    await handleImportProxies(importText);
    setImportText('');
    setShowImport(false);
  };

  const handleExport = (): void => {
    const content = handleExportProxies();
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'proxies.txt';
    a.click();
    URL.revokeObjectURL(url);
  };



  return (
    <div className={cn("flex flex-col h-full p-4 max-w-2xl mx-auto", className)}>
      <ProxyStatusHeader
        enableProxies={enableProxies}
        setEnableProxies={setEnableProxies}
        isLoading={isLoading}
        error={error}
        proxiesCount={proxies.length}
      />

      <ProxyAddForm
        form={form}
        quickProxyType={quickProxyType}
        setQuickProxyType={setQuickProxyType}
        handleAddProxy={handleAddProxy}
      />

      <ProxyAdvancedSettings
        useRandomProxy={useRandomProxy}
        setUseRandomProxy={setUseRandomProxy}
        maxRetries={maxRetries}
        setMaxRetries={setMaxRetries}
        randomizeSource={randomizeSource}
        setRandomizeSource={setRandomizeSource}
        sourceUrl={sourceUrl}
        setSourceUrl={setSourceUrl}
        autoUpdateEnabled={autoUpdateEnabled}
        setAutoUpdateEnabled={setAutoUpdateEnabled}
        updateInterval={updateInterval}
        setUpdateInterval={setUpdateInterval}
        showImport={showImport}
        setShowImport={setShowImport}
        handleTestAllProxies={handleTestAllProxies}
        handleExport={handleExport}
        isLoading={isLoading}
        proxiesCount={proxies.length}
      />

      <ProxyImportPanel
        showImport={showImport}
        importText={importText}
        setImportText={setImportText}
        setShowImport={setShowImport}
        handleImport={handleImport}
      />

      <ProxyList
        proxies={proxies}
        testResults={testResults}
        isTesting={isTesting}
        handleTestProxy={handleTestProxy}
        handleDeleteProxy={handleDeleteProxy}
      />

    </div>
  );
};

export default ProxySettings;


---

// FILE: src\components\SettingsPanel\ProxyStatusHeader.tsx

/**
 * @file Proxy status header component
 */
import { Globe } from 'lucide-react';
import React from 'react';

import { Button } from '../../shared/ui/button';
import { cn } from '../../shared/utils/utils';

interface ProxyStatusHeaderProps {
  enableProxies: boolean;
  setEnableProxies: (_enabled: boolean) => Promise<void>;
  isLoading: boolean;
  error: string | null;
  proxiesCount: number;
}

/**
 * Header component showing proxy status and controls
 */
export const ProxyStatusHeader: React.FC<ProxyStatusHeaderProps> = ({
  enableProxies,
  setEnableProxies,
  isLoading,
  error,
  proxiesCount
}) => {
  return (
    <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg mb-4">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <div className={cn("w-2 h-2 rounded-full", enableProxies ? 'bg-green-500' : 'bg-gray-500')} />
          <Button
            type="button"
            onClick={() => {
              void (async (): Promise<void> => {
                try {
                  await setEnableProxies(!enableProxies);
                } catch (toggleError) {
                  // Log error for debugging - consider using proper logging service in production
                  // TODO: Replace with proper logging service in production
                  // eslint-disable-next-line no-console
                  console.error('Failed to toggle proxy:', toggleError);
                }
              })();
            }}
            variant={enableProxies ? 'default' : 'outline'}
            size="sm"
            className="gap-2 h-8 px-4"
            disabled={isLoading}
          >
            {isLoading ? 'Loading...' : (enableProxies ? 'Enabled' : 'Disabled')}
          </Button>
          {(error?.length ?? 0) > 0 && <span className="text-xs text-red-500 ml-1">({error})</span>}
        </div>

        <div className="h-4 w-px bg-border" />

        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Globe size={14} />
          <span>{proxiesCount} {proxiesCount === 1 ? 'proxy' : 'proxies'}</span>
        </div>
      </div>
    </div>
  );
};

export default ProxyStatusHeader;


---

// FILE: src\components\SettingsPanel\SettingsView.tsx

/**
 * @file Component for displaying settings directly in the main content area
 */
import { Globe, Shield, Users, RotateCcw, Settings } from 'lucide-react';
import React, { useState } from 'react';

import { useUIStore } from '../../shared/store/uiStore';
import { Button } from '../../shared/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../shared/ui/card';
import { Tooltip } from '../../shared/ui/tooltip';

import MainSettings from './MainSettings';
import ProxySettings from './ProxySettings';


type SettingsTab = 'main' | 'proxy' | 'security' | 'accounts';



const SettingsView: React.FC = () => {
  const [activeTab, setActiveTab] = useState<SettingsTab>('main');
  const [showResetDialog, setShowResetDialog] = useState(false);
  const { resetConfig } = useUIStore();

  const handleResetConfig = (): void => {
    resetConfig();
    setShowResetDialog(false);
  };

  return (
    <div className="h-full flex flex-col bg-background text-foreground">
      {/* Tab Navigation */}
      <div className="flex items-center justify-between border-b border-border bg-background/95 backdrop-blur h-12 px-4">
        <div />
        <div className="flex items-center gap-1">
          <button
            onClick={() => setActiveTab('main')}
            className={`flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'main'
                ? 'bg-muted text-foreground'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
          >
            <Settings size={16} />
            Main
          </button>

          <button
            onClick={() => setActiveTab('proxy')}
            className={`flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'proxy'
                ? 'bg-muted text-foreground'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
          >
            <Globe size={16} />
            Proxy
          </button>

          <button
            onClick={() => setActiveTab('security')}
            className={`flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'security'
                ? 'bg-muted text-foreground'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
          >
            <Shield size={16} />
            Security
          </button>

          <button
            onClick={() => setActiveTab('accounts')}
            className={`flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'accounts'
                ? 'bg-muted text-foreground'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
          >
            <Users size={16} />
            Actions
          </button>
        </div>

        {/* Reset Config Button */}
        <Tooltip content="Reset all settings to default">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setShowResetDialog(true)}
            className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
          >
            <RotateCcw size={16} />
          </Button>
        </Tooltip>
      </div>

      {/* Content */}
      <main className="flex-1 overflow-auto custom-scrollbar bg-background">
          {activeTab === 'main' && (
            <MainSettings />
          )}

          {activeTab === 'proxy' && (
            <ProxySettings />
          )}
          
          {activeTab === 'security' && (
            <div className="p-6 max-w-2xl mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle>Security Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">Security settings will be added in a future update.</p>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'accounts' && (
            <div className="p-6 max-w-2xl mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle>Account Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">Batch account operations will be added in a future update.</p>
                </CardContent>
              </Card>
            </div>
          )}
        </main>

        {/* Reset Configuration Confirmation Dialog */}
        {showResetDialog && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-full max-w-md mx-4">
              <CardHeader>
                <CardTitle className="text-lg">Reset Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">
                  Are you sure you want to reset all settings to default? This will reset panel sizes, positions, and all UI preferences. This action cannot be undone.
                </p>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowResetDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleResetConfig}
                  >
                    Reset
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
    </div>
  );
};

export default SettingsView; 

---

// FILE: src\components\VirtualizedList.tsx

/**
 * @file Virtualized list component for performance optimization with large datasets
 */
import React, { useState, useRef, useCallback } from 'react';

interface VirtualizedListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (_item: T, _index: number) => React.ReactNode;
  overscan?: number;
  className?: string;
  onScroll?: (_scrollTop: number) => void;
  onEndReached?: () => void;
  endReachedThreshold?: number;
}

/**
 * Virtualized list component that only renders visible items for better performance
 * Useful for large email lists or any large dataset
 */
export function VirtualizedList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = '',
  onScroll,
  onEndReached,
  endReachedThreshold = 0.8
}: VirtualizedListProps<T>): React.ReactElement {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Calculate visible range
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = items.slice(startIndex, endIndex + 1);
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    onScroll?.(newScrollTop);

    // Check if we've reached the end
    if (onEndReached && endReachedThreshold) {
      const { scrollHeight, clientHeight } = e.currentTarget;
      const scrollPercentage = (newScrollTop + clientHeight) / scrollHeight;
      
      if (scrollPercentage >= endReachedThreshold) {
        onEndReached();
      }
    }
  }, [onScroll, onEndReached, endReachedThreshold]);

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
      role="list"
      aria-label="Virtualized list"
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {visibleItems.map((item, index) => {
            const actualIndex = startIndex + index;

            return (
              <div
                key={actualIndex}
                style={{ height: itemHeight }}
                role="listitem"
              >
                {renderItem(item, actualIndex)}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

// Hook for managing virtualized list state
export const useVirtualizedList = <T,>(
  items: T[],
  itemHeight: number,
  containerHeight: number
): {
  scrollTop: number;
  setScrollTop: React.Dispatch<React.SetStateAction<number>>;
  getVisibleRange: () => { startIndex: number; endIndex: number };
} => {
  const [scrollTop, setScrollTop] = useState(0);

  const getVisibleRange = useCallback(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight)
    );
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length]);

  return {
    scrollTop,
    setScrollTop,
    getVisibleRange
  };
};

interface InfiniteScrollListProps<T> extends Omit<VirtualizedListProps<T>, 'onEndReached'> {
  hasMore: boolean;
  isLoading: boolean;
  loadMore: () => Promise<void> | void;
  loadingComponent?: React.ReactNode;
}

/**
 * Virtualized list with infinite scroll capabilities
 */
export function InfiniteScrollList<T>({
  hasMore,
  isLoading,
  loadMore,
  loadingComponent,
  ...listProps
}: InfiniteScrollListProps<T>): React.ReactElement {
  const handleEndReached = useCallback(async () => {
    if (hasMore && !isLoading) {
      await loadMore();
    }
  }, [hasMore, isLoading, loadMore]);

  return (
    <div className="relative">
      <VirtualizedList
        {...listProps}
        onEndReached={() => {
          void handleEndReached();
        }}
        endReachedThreshold={0.8}
      />
      {isLoading && (
        <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-gray-900 to-transparent">
          {loadingComponent ?? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400" />
              <span className="ml-2 text-sm text-gray-400">Loading more...</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default VirtualizedList;


---

// FILE: src\ipc\account.ts

import fs from 'fs';
import readline from 'readline';

import { dialog, type IpcMain, type IpcMainInvokeEvent, type BrowserWindow } from 'electron';

import { AccountImportService, type EmailServerConfig } from '../services/accountImportService';
import { discoverEmailConfig, type DiscoveredConfig } from '../services/autoDiscoveryService';
import { imapFlowConnectionManager } from '../services/imapFlowConnectionManager';
import { InstantImportService } from '../services/instantImportService';
import {
    getAccounts,
    addAccount,
    updateAccount,
    removeAccount,
    addAccounts,
    getDomains,
    saveDomain,
    removeDomain
} from '../services/storeService';
import type { Account } from '../shared/types/account';

type SendLogFn = (level: 'info' | 'success' | 'error', message: string) => void;

/**
 * Convert DiscoveredConfig to EmailServerConfig format
 */
function convertToEmailServerConfig(config: DiscoveredConfig | null): EmailServerConfig | null {
    if (!config) return null;

    return {
        imap: config.imap ? {
            host: config.imap.host,
            port: config.imap.port,
            secure: config.imap.secure
        } : undefined,
        smtp: config.smtp ? {
            host: config.smtp.host,
            port: config.smtp.port,
            secure: config.smtp.secure
        } : undefined
    };
}

/**
 * A wrapper for discoverEmailConfig that caches results in domains.txt.
 */
async function getEmailConfig(domain: string, sendLog: SendLogFn, force: boolean = false): Promise<DiscoveredConfig | null> {
    sendLog('info', `getEmailConfig called for ${domain} with force=${force}`);
    const savedDomains = getDomains();

    // 1. Check our saved domains first (skip if force is true)
    if (!force && domain in savedDomains && savedDomains[domain] !== null && savedDomains[domain] !== undefined) {
        sendLog('info', `Found saved config for ${domain}`);
        // No need to parse JSON anymore, getDomains returns a parsed object
        return savedDomains[domain];
    }

    // 2. If not found or force is true, run auto-discovery
    if (force) {
        sendLog('info', `Force discovery requested for ${domain}. Running auto-discovery...`);
    } else {
        sendLog('info', `No saved config for ${domain}. Running auto-discovery...`);
    }
    const config = await discoverEmailConfig(domain, sendLog, { force });

    // 3. Save the result for next time
    if (config) {
        sendLog('success', `Successfully discovered config for ${domain}. Saving...`);
        // Pass the raw config object to saveDomain, casting it to ensure compatibility
        saveDomain(domain, config);
    }

    return config;
}

/**
 * Register discovery and domain handlers
 */
function registerDiscoveryHandlers(ipcMain: IpcMain, sendLog: SendLogFn): void {
    ipcMain.handle('discover:email-config', async (_event: IpcMainInvokeEvent, domain: string, force: boolean = false) => {
        // This handler now uses our caching wrapper
        sendLog('info', `IPC discover:email-config called for ${domain} with force=${force}`);
        return getEmailConfig(domain, sendLog, force);
    });

    ipcMain.handle('domains:get', () => {
        return getDomains();
    });

    ipcMain.handle('domains:save', (_event: IpcMainInvokeEvent, domain: string, config: DiscoveredConfig) => {
        saveDomain(domain, config);
        sendLog('info', `Saved domain configuration for: ${domain}`);
        return { success: true };
    });

    ipcMain.handle('domains:remove', (_event: IpcMainInvokeEvent, domain: string) => {
        removeDomain(domain);
        sendLog('info', `Removed domain configuration for: ${domain}`);
        return { success: true };
    });
}

/**
 * Register basic account CRUD handlers
 */
function registerAccountCrudHandlers(ipcMain: IpcMain, sendLog: SendLogFn): void {
    ipcMain.handle('accounts:get', () => {
        return getAccounts();
    });

    ipcMain.handle('accounts:get-by-id', (_event: IpcMainInvokeEvent, accountId: string) => {
        const accounts = getAccounts();
        return accounts.find(acc => acc.id === accountId) || null;
    });

    ipcMain.handle('accounts:add', (_event: IpcMainInvokeEvent, accountData: Omit<Account, 'id'>) => {
        console.log('IPC accounts:add called with:', accountData);

        // Save custom IMAP settings to domains.txt for future use
        const domain = accountData.email.split('@')[1];
        if ((domain?.length ?? 0) > 0 && accountData.incoming !== null && accountData.incoming !== undefined) {
            // Don't save example domains or configurations with example hosts
            if (domain.includes('example.com') || domain.includes('example.org') ||
                accountData.incoming.host.includes('example.com') || accountData.incoming.host.includes('example.org')) {
                console.log('Skipping domain save for example domain/host:', domain, accountData.incoming.host);
            } else {
                const savedDomains = getDomains();

                // Check if this domain doesn't exist in our saved domains or has different settings
                const existingConfig = savedDomains[domain];
                const currentConfig: DiscoveredConfig = {};

            // Build current config from account data
            if (accountData.incoming !== null && accountData.incoming !== undefined) {
                currentConfig.imap = {
                    host: accountData.incoming.host,
                    port: accountData.incoming.port,
                    secure: accountData.incoming.useTls
                };
            }

            if (accountData.outgoing) {
                currentConfig.smtp = {
                    host: accountData.outgoing.host,
                    port: accountData.outgoing.port,
                    secure: accountData.outgoing.useTls
                };
            }

            // Save if domain doesn't exist or settings are different
            const shouldSave = existingConfig === null || existingConfig === undefined ||
                (currentConfig.imap !== null && currentConfig.imap !== undefined &&
                 (existingConfig.imap === null || existingConfig.imap === undefined ||
                    existingConfig.imap.host !== currentConfig.imap.host ||
                    existingConfig.imap.port !== currentConfig.imap.port ||
                    existingConfig.imap.secure !== currentConfig.imap.secure));

                if (shouldSave === true) {
                    sendLog('info', `Saving custom IMAP settings for domain: ${domain}`);
                    saveDomain(domain, currentConfig);
                }
            }
        }

        sendLog('info', `Adding account: ${accountData.email}`);
        console.log('Calling addAccount with:', accountData);
        const result = addAccount(accountData);
        console.log('addAccount returned:', result);
        return result;
    });

    ipcMain.handle('accounts:update', (_event: IpcMainInvokeEvent, accountId: string, accountData: Partial<Omit<Account, 'id'>>) => {
        console.log('IPC accounts:update called with accountId:', accountId);
        console.log('IPC accounts:update accountData:', JSON.stringify(accountData, null, 2));

        const updatedAccount = updateAccount(accountId, accountData);
        if (!updatedAccount) {
            throw new Error('Failed to find account to update.');
        }

        console.log('IPC accounts:update returning:', JSON.stringify(updatedAccount, null, 2));
        return updatedAccount;
    });

    ipcMain.handle('accounts:delete', (_event: IpcMainInvokeEvent, accountId: string) => {
        try {
            removeAccount(accountId);
            void imapFlowConnectionManager.end(accountId);
            return { success: true };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(`Failed to delete account ${accountId}:`, error);
            throw new Error('Failed to delete account from store.');
        }
    });
}

/**
 * Register file import handlers
 */
function registerFileImportHandlers(ipcMain: IpcMain, mainWindow: BrowserWindow, sendLog: SendLogFn): void {
    ipcMain.handle('accounts:import-from-file', async () => {
        const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, {
            title: 'Import Accounts',
            buttonLabel: 'Import',
            properties: ['openFile'],
            filters: [{ name: 'Text Files', extensions: ['txt', 'csv'] }],
        });

        if (canceled || filePaths.length === 0) {
            return { addedCount: 0, skippedCount: 0, error: 'File selection canceled.' };
        }

        const filePath = filePaths[0];
        const fileStream = fs.createReadStream(filePath);
        const rl = readline.createInterface({ input: fileStream, crlfDelay: Infinity });

        let skippedCount = 0;
        const accountsToAdd: Omit<Account, 'id' | 'connectionStatus'>[] = [];
        const separatorRegex = /[:;|]/;

        for await (const line of rl) {
            const parts = line.split(separatorRegex);

            if (parts.length < 2) {
                skippedCount++;
                continue;
            }

            const email = parts[0].trim();
            const password = parts.slice(1).join(parts[0].match(separatorRegex)?.[0] ?? '').trim();

            if (/^\S+@\S+\.\S+$/.test(email) && (password?.length ?? 0) > 0) {
                accountsToAdd.push({
                    displayName: email.split('@')[0],
                    email,
                    password,
                    incoming: { protocol: 'imap', host: 'imap.example.com', port: 993, useTls: true },
                    useProxy: false,
                });
            } else {
                skippedCount++;
            }
        }

        sendLog('info', `Parsed ${accountsToAdd.length} accounts. Starting server configuration discovery...`);

        const configuredAccountsPromises = accountsToAdd.map(async (accountData) => {
            try {
                // Use the caching wrapper here as well
                const config = await getEmailConfig(accountData.email, sendLog);
                if (config?.imap) {
                    accountData.incoming = {
                        protocol: 'imap',
                        host: config.imap.host,
                        port: config.imap.port,
                        useTls: config.imap.secure,
                    };
                    if (config.smtp) {
                        accountData.outgoing = {
                            protocol: 'smtp',
                            host: config.smtp.host,
                            port: config.smtp.port,
                            useTls: config.smtp.secure,
                        };
                    } else {
                        delete accountData.outgoing;
                    }
                } else {
                    sendLog('error', `Auto-discovery failed for ${accountData.email}. Requires manual configuration.`);
                }
            } catch (e) {
                sendLog('error', `Error during discovery for ${accountData.email}: ${(e as Error).message}`);
            }
            return accountData;
        });

        const configuredAccounts = await Promise.all(configuredAccountsPromises);
        const newAccounts = addAccounts(configuredAccounts);
        
        return {
            addedCount: newAccounts.length,
            skippedCount,
            totalCount: configuredAccounts.length + skippedCount
        };
    });
}

/**
 * Register preview and instant import handlers
 */
function registerPreviewImportHandlers(ipcMain: IpcMain, mainWindow: BrowserWindow, sendLog: SendLogFn): void {
    // Enhanced import with preview and progress
    ipcMain.handle('accounts:import-preview', async () => {
        const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, {
            title: 'Preview Import File',
            buttonLabel: 'Preview',
            properties: ['openFile'],
            filters: [
                { name: 'Text Files', extensions: ['txt', 'csv'] },
                { name: 'All Files', extensions: ['*'] }
            ],
        });

        if (canceled || filePaths.length === 0) {
            return { success: false, error: 'File selection canceled.' };
        }

        try {
            const preview = await AccountImportService.generatePreview(filePaths[0]);
            return { success: true, preview, filePath: filePaths[0] };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to preview file';
            sendLog('error', `Preview failed: ${errorMessage}`);
            return { success: false, error: errorMessage };
        }
    });

    ipcMain.handle('accounts:import-enhanced', async (event: IpcMainInvokeEvent, filePath: string) => {
        try {
            sendLog('info', 'Starting enhanced import...');

            // Parse the file with progress reporting
            const parseResult = await AccountImportService.parseFile(filePath, (progress) => {
                // Send progress updates to renderer
                event.sender.send('import:progress', progress);
            });

            if (!parseResult.success) {
                sendLog('error', `Parse failed: ${parseResult.errors.join(', ')}`);
                return { success: false, errors: parseResult.errors };
            }

            sendLog('info', `Parsed ${parseResult.accounts.length} accounts. Configuring servers...`);

            // Configure accounts with server discovery
            const configuredAccounts = await AccountImportService.configureAccounts(
                parseResult.accounts,
                async (email) => {
                    const config = await getEmailConfig(email, sendLog);
                    return convertToEmailServerConfig(config);
                },
                (progress) => {
                    event.sender.send('import:progress', progress);
                }
            );

            // Add to store
            const newAccounts = addAccounts(configuredAccounts);

            sendLog('success', `Successfully imported ${newAccounts.length} accounts`);

            return {
                success: true,
                addedCount: newAccounts.length,
                skippedCount: parseResult.skippedLines,
                totalCount: parseResult.totalLines,
            };

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Import failed';
            sendLog('error', `Enhanced import failed: ${errorMessage}`);
            return { success: false, error: errorMessage };
        }
    });
}

/**
 * Register instant import handlers
 */
function registerInstantImportHandlers(ipcMain: IpcMain, mainWindow: BrowserWindow, sendLog: SendLogFn): void {
    // Instant import - adds accounts immediately and discovers DNS in background
    ipcMain.handle('accounts:import-from-file-instant', async () => {
        const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, {
            title: 'Import Accounts (Instant)',
            buttonLabel: 'Import',
            properties: ['openFile'],
            filters: [
                { name: 'Text Files', extensions: ['txt', 'csv'] },
                { name: 'All Files', extensions: ['*'] }
            ],
        });

        if (canceled || filePaths.length === 0) {
            return { success: false, error: 'File selection canceled.' };
        }

        try {
            sendLog('info', 'Starting instant import...');
            const result = await InstantImportService.importFromFile(
                filePaths[0],
                (email) => getEmailConfig(email, sendLog)
            );

            if (result.success) {
                sendLog('success', `Instantly imported ${result.addedCount} accounts. DNS discovery running in background.`);
            } else {
                sendLog('error', `Instant import failed: ${result.error}`);
            }

            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Instant import failed';
            sendLog('error', `Instant import failed: ${errorMessage}`);
            return { success: false, error: errorMessage };
        }
    });
}

/**
 * Register drag-and-drop import handlers
 */
function registerDragDropImportHandlers(ipcMain: IpcMain, sendLog: SendLogFn): void {
    // Import from specific file path (for drag-and-drop)
    ipcMain.handle('accounts:import-from-file-data', async (_event: IpcMainInvokeEvent, filePath: string) => {
        try {
            sendLog('info', 'Starting file import...');
            const result = await InstantImportService.importFromFile(
                filePath,
                (email) => getEmailConfig(email, sendLog)
            );

            if (result.success) {
                sendLog('success', `Imported ${result.addedCount} accounts from dropped file. DNS discovery running in background.`);
            } else {
                sendLog('error', `File import failed: ${result.error}`);
            }

            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'File import failed';
            sendLog('error', `File import failed: ${errorMessage}`);
            return { success: false, error: errorMessage };
        }
    });

    // Import from file content (for drag-and-drop)
    ipcMain.handle('accounts:import-from-file-content', (_event: IpcMainInvokeEvent, content: string) => {
        try {
            sendLog('info', 'Starting content import...');
            const result = InstantImportService.importFromContent(
                content,
                (email) => getEmailConfig(email, sendLog)
            );

            if (result.success) {
                sendLog('success', `Imported ${result.addedCount} accounts from dropped content. DNS discovery running in background.`);
            } else {
                sendLog('error', `Content import failed: ${result.error}`);
            }

            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Content import failed';
            sendLog('error', `Content import failed: ${errorMessage}`);
            return { success: false, error: errorMessage };
        }
    });
}

/**
 * Main function to register all account handlers
 */
export const registerAccountHandlers = (ipcMain: IpcMain, mainWindow: BrowserWindow, sendLog: SendLogFn): void => {
    registerDiscoveryHandlers(ipcMain, sendLog);
    registerAccountCrudHandlers(ipcMain, sendLog);
    registerFileImportHandlers(ipcMain, mainWindow, sendLog);
    registerPreviewImportHandlers(ipcMain, mainWindow, sendLog);
    registerInstantImportHandlers(ipcMain, mainWindow, sendLog);
    registerDragDropImportHandlers(ipcMain, sendLog);
};

---

// FILE: src\ipc\config.ts

/**
 * @file IPC handlers for user configuration management
 */
import type { IpcMain, IpcMainInvokeEvent } from 'electron';

import { getConfig, saveConfig } from '../services/storeService';

type SendLogFn = (level: 'info' | 'success' | 'error', message: string) => void;

export const registerConfigHandlers = (ipcMain: IpcMain, sendLog: SendLogFn): void => {
  // Get user configuration
  ipcMain.handle('config:get-user', (): Record<string, unknown> => {
    try {
      const config = getConfig();
      return (config.ui as Record<string, unknown>) ?? {};
    } catch (error) {
      sendLog('error', `Failed to load user config: ${error instanceof Error ? error.message : String(error)}`);
      return {};
    }
  });

  // Save user configuration
  ipcMain.handle('config:save-user', (_event: IpcMainInvokeEvent, userConfig: Record<string, unknown>): void => {
    try {
      const config = getConfig();
      config.ui = userConfig;
      saveConfig(config);
      sendLog('info', 'User configuration saved successfully');
    } catch (error) {
      sendLog('error', `Failed to save user config: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  });

  // Reset entire configuration to defaults
  ipcMain.handle('config:reset-all', (): void => {
    try {
      const defaultConfig = {
        proxy: {
          enabled: false,
          type: "socks5",
          hostPort: "127.0.0.1:1080",
          auth: false
        },
        ui: {
          isSettingsOpen: false,
          currentView: "email",
          isLeftPanelHidden: false,
          isLogPanelCollapsed: false,
          isAccountPanelCollapsed: false,
          leftPanelWidth: 25,
          rightPanelWidth: 25,
          logPanelHeight: 25
        }
      };
      saveConfig(defaultConfig);
      sendLog('info', 'All configuration reset to defaults');
    } catch (error) {
      sendLog('error', `Failed to reset config: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  });
};


---

// FILE: src\ipc\imapFlow.ts

import type { IpcMain, IpcMainInvokeEvent, WebContents } from 'electron';

import { imapFlowConnectionManager } from '../services/imapFlowConnectionManager';
import {
  connectToAccount,
  getMailboxes as fetchMailboxes,
  fetchEmails,
  fetchEmailBody,
  deleteEmail as deleteEmailIMAP,
  markAsSeen as markAsSeenIMAP,
  markAsUnseen as markAsUnseenIMAP,
  deleteEmails as deleteEmailsIMAP,
} from '../services/imapFlowService';
import { getAccounts } from '../services/storeService';
import type { Account } from '../shared/types/account';
import type { MailBoxes } from '../shared/types/electron';
import type { EmailHeader } from '../shared/types/email';

// Interface for IMAP mailbox object
interface ImapMailbox {
  path: string;
  delimiter: string;
  flags?: string[];
  specialUse?: string;
}

type SendLogFn = (_level: 'info' | 'success' | 'error', _message: string) => void;
type SendConnectionStatusFn = (_accountId: string, _status: 'connected' | 'connecting' | 'disconnected') => void;

interface MailboxWatcher {
  release: () => Promise<void>;
  listener: (_newCount: number) => void;
}

// Note: Using 'any' for IMAP types due to complex external library interfaces

// Map to keep track of the active mailbox watcher for each account
const mailboxWatchers = new Map<string, MailboxWatcher>();

/**
 * Helper to find the real path of the INBOX folder.
 * @param imap - The connected IMAP instance.
 * @returns The path of the INBOX folder.
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function findInboxPath(imap: any): Promise<string> {
  const mailboxes = await imap.list();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const inbox = mailboxes.find((m: any) => m.flags?.has('\\Inbox') === true);
  return inbox?.path ?? 'INBOX';
}

const getOrCreateConnection = async (
  accountId: string,
  sendLog: SendLogFn,
  sendConnectionStatus: SendConnectionStatusFn
// eslint-disable-next-line @typescript-eslint/no-explicit-any
): Promise<any> => {
  const imap = imapFlowConnectionManager.get(accountId);
  if (imap) {
    sendLog('info', `Using existing connection for account.`);
    sendConnectionStatus(accountId, 'connected');
    return imap;
  }

  sendLog('info', `No active connection found. Creating a new one...`);
  sendConnectionStatus(accountId, 'connecting');
  // Get the latest account data from file (includes any recent updates)
  const accounts = getAccounts();
  const account = accounts.find((acc: Account) => acc.id === accountId);

  if (!account) {
    const errorMsg = `Account not found for id: ${accountId}`;
    sendLog('error', errorMsg);
    sendConnectionStatus(accountId, 'disconnected');
    throw new Error(errorMsg);
  }

  try {
    const logFn = (message: string, level: 'info' | 'success' | 'error' = 'info'): void => sendLog(level, message);
    logFn(`Attempting to connect to ${account.incoming.host} for ${account.email}...`);

    const { imap: newImap, proxyUsed } = await connectToAccount(account, logFn);

    let successMessage = `Successfully connected to ${account.email}.`;
    if (proxyUsed) {
      successMessage = `Successfully connected to ${account.email} via proxy.`;
    }
    sendLog('success', successMessage);
    sendConnectionStatus(accountId, 'connected');

    imapFlowConnectionManager.set(accountId, newImap);
    return newImap;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    const errorMessage = error.responseText ?? error.message ?? 'An unknown connection error occurred.';
    sendLog('error', errorMessage);
    sendConnectionStatus(accountId, 'disconnected');
    throw error;
  }
};

/**
 * Helper function to process email message
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function processEmailMessage(message: any): EmailHeader | null {
  if (message.envelope === null || message.envelope === undefined ||
      message.flags === null || message.flags === undefined) {
    return null;
  }

  const fromAddress = message.envelope.from?.[0];
  let fromText = 'Unknown Sender';
  if (fromAddress !== null && fromAddress !== undefined) {
    fromText = (fromAddress.name?.length ?? 0) > 0
      ? `${fromAddress.name} <${fromAddress.address}>`
      : fromAddress.address ?? 'Unknown Sender';
  }

  return {
    uid: message.uid,
    subject: message.envelope.subject ?? 'No Subject',
    from: { text: fromText },
    date: message.envelope.date?.toISOString() ?? new Date().toISOString(),
    flags: Array.from(message.flags),
    seen: message.flags.has('\\Seen')
  };
}

/**
 * Helper function to fetch initial emails
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function fetchInitialEmails(imap: any, mailboxName: string, limit: number, sendLog: SendLogFn): Promise<EmailHeader[]> {
  const initialEmails: EmailHeader[] = [];

  if (typeof imap.mailbox !== 'object' || imap.mailbox === null ||
      typeof imap.mailbox.exists !== 'number' || imap.mailbox.exists <= 0) {
    return initialEmails;
  }

  const totalMessages = imap.mailbox.exists;
  sendLog('info', `Found ${totalMessages} messages in ${mailboxName}`);

  if (totalMessages > 0) {
    const start = Math.max(1, totalMessages - limit + 1);
    const end = totalMessages;

    for await (const message of imap.fetch(`${start}:${end}`, { envelope: true, flags: true })) {
      const emailHeader = processEmailMessage(message);
      if (emailHeader !== null) {
        initialEmails.push(emailHeader);
      }
    }
  }

  return initialEmails;
}

/**
 * Register IMAP connection and mailbox handlers
 */
function registerImapConnectionHandlers(
  ipcMain: IpcMain,
  webContents: WebContents,
  sendLog: SendLogFn,
  sendConnectionStatus: SendConnectionStatusFn
): void {
  // This handler starts a background watch on an account's INBOX.
  ipcMain.handle('imap:watch-inbox', async (_event, accountId: string) => {
    try {
      const imap = await getOrCreateConnection(accountId, sendLog, sendConnectionStatus);

      // Don't create a new watcher if one already exists for this account.
      if (mailboxWatchers.has(accountId)) {
        return;
      }

      const inboxPath = await findInboxPath(imap);
      const lock = await imap.getMailboxLock(inboxPath);
      
      let messageCount = (typeof imap.mailbox === 'object' && imap.mailbox !== null && typeof imap.mailbox.exists === 'number') ? imap.mailbox.exists : 0;

      const listener = (newCount: number): void => {
        if (newCount > messageCount) {
          const newMailCount = newCount - messageCount;
          sendLog('success', `📬 Account ${accountId} has ${newMailCount} new email(s) in INBOX!`);
          webContents.send('mail:new', { accountId, mailboxName: inboxPath, newMailCount });
        }
        messageCount = newCount;
      };

      imap.on('exists', listener);
      
      mailboxWatchers.set(accountId, { release: () => lock.release(), listener });
      sendLog('info', `Started watching INBOX for account ${accountId}.`);

    } catch (error) {
      sendLog('error', `Failed to start watching INBOX for ${accountId}: ${(error as Error).message}`);
    }
  });

  // This single handler now manages selecting a mailbox, fetching initial emails, and watching for new ones.
  ipcMain.handle('imap:select-mailbox', async (_event, accountId: string, mailboxName: string, limit: number) => {
    try {
      sendLog('info', `Selecting mailbox ${mailboxName} for account ${accountId}`);
      const imap = await getOrCreateConnection(accountId, sendLog, sendConnectionStatus);

      // We only need a short-term lock for fetching, no more IDLE here.
      const lock = await imap.getMailboxLock(mailboxName);

      try {
        const initialEmails = await fetchInitialEmails(imap, mailboxName, limit, sendLog);
        initialEmails.reverse(); // Show newest first
        const totalCount = (typeof imap.mailbox === 'object' && imap.mailbox !== null && typeof imap.mailbox.exists === 'number') ? imap.mailbox.exists : 0;
        sendLog('success', `Successfully fetched ${initialEmails.length} emails from ${mailboxName} (${totalCount} total)`);
        return { emails: initialEmails, totalCount };
      } finally {
        // Release the lock immediately after fetching.
        lock.release();
      }

    } catch (error) {
      const errorMessage = `Failed to select mailbox ${mailboxName}: ${(error as Error).message}`;
      sendLog('error', errorMessage);
      throw new Error(errorMessage);
    }
  });
}

/**
 * Register IMAP mailbox and email fetching handlers
 */
function registerImapMailboxHandlers(
  ipcMain: IpcMain,
  sendLog: SendLogFn,
  sendConnectionStatus: SendConnectionStatusFn
): void {
  ipcMain.handle('imap:getMailboxes', async (_event: IpcMainInvokeEvent, accountId: string) => {
    const imap = await getOrCreateConnection(accountId, sendLog, sendConnectionStatus);
    sendLog('info', 'Fetching mailboxes...');
    const mailboxes = await fetchMailboxes(imap) as ImapMailbox[];

    // Преобразуем формат mailboxes для совместимости с существующим кодом
    const mailboxesTree: MailBoxes = {};

    for (const mailbox of mailboxes) {
      const pathParts = mailbox.path.split(mailbox.delimiter);
      let currentLevel = mailboxesTree;
    
      for (let i = 0; i < pathParts.length; i++) {
        const part = pathParts[i];
        
        if (!(part in currentLevel)) {
          currentLevel[part] = {
            attribs: [],
            children: {},
            delimiter: mailbox.delimiter,
          } as MailBoxes[string];
        }
    
        if (i === pathParts.length - 1) {
          currentLevel[part].attribs = Array.from(mailbox.flags ?? []);
          if (currentLevel[part].children === undefined) {
            currentLevel[part].children = {};
          }
        }
        
        if (currentLevel[part].children !== null && currentLevel[part].children !== undefined) {
          currentLevel = currentLevel[part].children;
        }
      }
    }
    
    sendLog('success', 'Mailboxes fetched.');
    return mailboxesTree;
  });

  ipcMain.handle('imap:getEmails', async (_event: IpcMainInvokeEvent, accountId: string, mailboxName: string, offset: number, limit: number) => {
    const imap = imapFlowConnectionManager.get(accountId);
    if (!imap) {
      // eslint-disable-next-line no-console
      console.error(`No active connection found for account ${accountId}`);
      return [];
    }

    try {
      const emails = await fetchEmails(imap, mailboxName, offset, limit);
      return emails;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Failed to fetch emails for ${accountId} from ${mailboxName}:`, error);
      return []; // Return empty array on error
    }
  });
}

/**
 * Register IMAP email operation handlers
 */
function registerImapEmailHandlers(
  ipcMain: IpcMain,
  sendLog: SendLogFn
): void {
  ipcMain.handle('imap:getEmailBody', async (_event: IpcMainInvokeEvent, accountId: string, mailboxName: string, emailUid: number) => {
    const imap = imapFlowConnectionManager.get(accountId);
    if (!imap) {
      // eslint-disable-next-line no-console
      console.error(`No active connection found for account ${accountId}`);
      return null;
    }

    try {
      const logFn = (message: string, level: 'info' | 'success' | 'error' = 'info'): void => sendLog(level, message);
      const email = await fetchEmailBody(imap, mailboxName, emailUid, logFn);
      return email;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Failed to fetch email body for UID ${emailUid}:`, error);
      throw error;
    }
  });

  ipcMain.handle('imap:deleteEmail', async (_event: IpcMainInvokeEvent, accountId: string, mailboxName: string, emailUid: number) => {
    const imap = imapFlowConnectionManager.get(accountId);
    if (!imap) {
      // eslint-disable-next-line no-console
      console.error(`No active connection found for account ${accountId}`);
      return;
    }

    try {
      await deleteEmailIMAP(imap, mailboxName, emailUid);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Failed to delete email UID ${emailUid}:`, error);
      throw error;
    }
  });

  ipcMain.handle('imap:markAsSeen', async (_event: IpcMainInvokeEvent, accountId: string, mailboxName: string, emailUid: number) => {
    const imap = imapFlowConnectionManager.get(accountId);
    if (!imap) return;
    try {
      await markAsSeenIMAP(imap, mailboxName, emailUid);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Failed to mark email UID ${emailUid} as seen:`, error);
      throw error;
    }
  });

  ipcMain.handle('imap:markAsUnseen', async (_event: IpcMainInvokeEvent, accountId: string, mailboxName: string, emailUid: number) => {
    const imap = imapFlowConnectionManager.get(accountId);
    if (!imap) return;
    try {
      await markAsUnseenIMAP(imap, mailboxName, emailUid);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Failed to mark email UID ${emailUid} as unseen:`, error);
      throw error;
    }
  });

  ipcMain.handle('imap:deleteEmails', async (_event: IpcMainInvokeEvent, accountId: string, mailboxName: string, emailUids: number[]) => {
    const imap = imapFlowConnectionManager.get(accountId);
    if (!imap) {
      // eslint-disable-next-line no-console
      console.error(`No active connection found for account ${accountId}`);
      return { success: false, error: 'No active connection' };
    }
    try {
      await deleteEmailsIMAP(imap, mailboxName, emailUids);
      return { success: true };
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Failed to delete emails with UIDs ${emailUids}:`, error);
      return { success: false, error: (error as Error).message };
    }
  });
}

/**
 * Main function to register all IMAP handlers
 */
export const registerImapFlowHandlers = (
  ipcMain: IpcMain,
  webContents: WebContents,
  sendLog: SendLogFn,
  sendConnectionStatus: SendConnectionStatusFn
): void => {
  registerImapConnectionHandlers(ipcMain, webContents, sendLog, sendConnectionStatus);
  registerImapMailboxHandlers(ipcMain, sendLog, sendConnectionStatus);
  registerImapEmailHandlers(ipcMain, sendLog);
};

---

// FILE: src\ipc\index.ts

import type { IpcMain, WebContents, BrowserWindow } from 'electron';

import { getGlobalProxy } from '../services/storeService';
import type { ProxyStatus } from '../shared/types/electron';

import { registerAccountHandlers } from './account';
import { registerConfigHandlers } from './config';
import { registerImapFlowHandlers } from './imapFlow';
import { registerProxyHandlers } from './proxy';

type SendLogFn = (_level: 'info' | 'success' | 'error', _message: string) => void;
type SendConnectionStatusFn = (_accountId: string, _status: 'connected' | 'connecting' | 'disconnected') => void;
type SendProxyStatusFn = (_status: ProxyStatus, _details?: { ip?: string; error?: string }) => void;

export interface RegisterHandlersArgs {
  ipcMain: IpcMain;
  webContents: WebContents;
  mainWindow: BrowserWindow;
  sendLog: SendLogFn;
  sendProxyStatus: SendProxyStatusFn;
  sendConnectionStatus: SendConnectionStatusFn;
}

/**
 * @file Entry point for registering all IPC handlers.
 * It imports handlers from different files and registers them with the main process.
 */
export const registerIpcHandlers = ({ ipcMain, webContents, mainWindow, ...helpers }: RegisterHandlersArgs): void => {
    // Register all handlers from the different modules
    registerAccountHandlers(ipcMain, mainWindow, helpers.sendLog);
    registerImapFlowHandlers(ipcMain, webContents, helpers.sendLog, helpers.sendConnectionStatus);
    registerProxyHandlers(ipcMain, helpers.sendProxyStatus);
    registerConfigHandlers(ipcMain, helpers.sendLog);

    // Handle renderer ready signal
    ipcMain.handle('renderer:ready', () => {
        // Initial proxy check on startup, once the renderer is ready to receive updates.
        const initialProxyConfig = getGlobalProxy();
        if (initialProxyConfig?.enabled === true) {
            // Proxy connection test would be handled by proxy handlers
            helpers.sendProxyStatus('connecting');
        }
        helpers.sendLog('info', 'Renderer process is ready and listening for events.');
    });
};

---

// FILE: src\ipc\proxy.ts

import type { IncomingMessage } from 'http';
import https from 'https';

import type { IpcMain, IpcMainInvokeEvent } from 'electron';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { SocksProxyAgent } from 'socks-proxy-agent';

import { getGlobalProxy, setGlobalProxy, getProxyList, saveProxyList, testProxy as testProxyService } from '../services/storeService';
import type { ProxyConfig, GlobalProxyConfig } from '../shared/types/account';
import type { ProxyStatus } from '../shared/types/electron';


const testProxyConnection = (proxy: GlobalProxyConfig, sendProxyStatus: (_status: ProxyStatus, _details?: { ip?: string; error?: string }) => void): void => {
    sendProxyStatus('connecting');

    const [proxyHost, proxyPortStr] = proxy.hostPort.split(':');
    const proxyPort = parseInt(proxyPortStr, 10);

    const authPart = (proxy.auth === true && (proxy.username?.length ?? 0) > 0) ?
      `${encodeURIComponent(proxy.username ?? '')}:${encodeURIComponent(proxy.password ?? '')}@` : '';
    const url = `${proxy.type}://${authPart}${proxyHost}:${proxyPort}`;

    const agent = proxy.type === 'https' ? new HttpsProxyAgent(url) : new SocksProxyAgent(url);

    const requestOptions = {
        hostname: 'cloudflare.com',
        port: 443,
        path: '/cdn-cgi/trace',
        method: 'GET',
        agent,
        timeout: 5000, // 5 second timeout
    };

    const req = https.get(requestOptions, (res: IncomingMessage) => {
        let body = '';
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        res.on('data', (chunk: any) => {
            body += chunk;
        });

        res.on('end', () => {
            if (typeof res.statusCode === 'number' && res.statusCode >= 200 && res.statusCode < 300) {
                const ipMatch = body.match(/^ip=(.*)$/m);
                const externalIp = ipMatch ? ipMatch[1] : undefined;
                sendProxyStatus('connected', { ip: externalIp });
            } else {
                sendProxyStatus('error', { error: `Proxy test failed with status code: ${res.statusCode}` });
            }
        });
    });

    req.on('error', (err: Error) => {
        sendProxyStatus('error', { error: err.message });
    });

    req.on('timeout', () => {
        req.destroy();
        sendProxyStatus('error', { error: 'Proxy connection timed out' });
    });
};

export const registerProxyHandlers = (ipcMain: IpcMain, sendProxyStatus: (_status: ProxyStatus, _details?: { ip?: string; error?: string }) => void): void => {
    ipcMain.handle('proxy:get-global', () => {
        return getGlobalProxy();
    });

    ipcMain.handle('proxy:set-global', (_event: IpcMainInvokeEvent, config: GlobalProxyConfig | null) => {
        setGlobalProxy(config);
        if (config?.enabled === true) {
            testProxyConnection(config, sendProxyStatus);
        } else {
            sendProxyStatus('disabled');
        }
    });
    
    // New handlers for proxy list management
    ipcMain.handle('proxy:get-list', () => {
        return getProxyList();
    });
    
    ipcMain.handle('proxy:save-list', (_event: IpcMainInvokeEvent, proxies: ProxyConfig[]) => {
        return saveProxyList(proxies);
    });
    
    ipcMain.handle('proxy:test', async (_event: IpcMainInvokeEvent, proxy: ProxyConfig) => {
        return testProxyService(proxy);
    });
};

export { testProxyConnection }; 

---

// FILE: src\main.ts

/// <reference path="./types/electron-squirrel-startup.d.ts" />

import path from 'node:path';

import { app, BrowserWindow, ipcMain } from 'electron';
import started from 'electron-squirrel-startup';

import { registerIpcHandlers } from './ipc';
import { imapFlowConnectionManager } from './services/imapFlowConnectionManager';
import type { ProxyStatus } from './shared/types/electron';

// Vite environment variables
declare const MAIN_WINDOW_VITE_DEV_SERVER_URL: string | undefined;
declare const MAIN_WINDOW_VITE_NAME: string;

// Node.js globals for Electron main process
declare const __dirname: string;

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (started === true) {
  app.quit();
}

let mainWindow: BrowserWindow | null = null;

const createWindow = (): void => {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
    },
  });

  // and load the index.html of the app.
  if (typeof MAIN_WINDOW_VITE_DEV_SERVER_URL === 'string' && MAIN_WINDOW_VITE_DEV_SERVER_URL.length > 0) {
    void mainWindow.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
  } else {
    void mainWindow.loadFile(path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`));
  }

  // Open the DevTools.
  mainWindow.webContents.openDevTools();
  
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
};

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', createWindow);

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    void imapFlowConnectionManager.endAll();
    app.quit();
  }
});

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (mainWindow === null) {
    createWindow();
  }
});

// Helper function to send logs to the renderer process
const sendLog = (level: 'info' | 'success' | 'error', message: string): void => {
  if (mainWindow) {
    mainWindow.webContents.send('log:add', { level, message });
  }
};

// Helper function to send connection status updates to renderer
const sendConnectionStatus = (accountId: string, status: 'connected' | 'connecting' | 'disconnected'): void => {
  if (mainWindow) {
    mainWindow.webContents.send('account:connection-status', { accountId, status });
  }
};

// Helper to send proxy status updates
const sendProxyStatus = (status: ProxyStatus, details: { ip?: string; error?: string } = {}): void => {
  if (mainWindow) {
    mainWindow.webContents.send('proxy:status-update', { status, ...details });
  }
};

// --- IPC and App Logic Setup ---
void app.whenReady().then(() => {
  if (!mainWindow) {
    createWindow();
  }

  if (mainWindow) {
    registerIpcHandlers({
      ipcMain,
      webContents: mainWindow.webContents,
      mainWindow,
      sendLog,
      sendProxyStatus,
      sendConnectionStatus,
    });
  }
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and import them here.


---

// FILE: src\preload.ts

// See the Electron documentation for details on how to use preload scripts:
// https://www.electronjs.org/docs/latest/tutorial/process-model#preload-scripts

import { contextBridge, ipcRenderer } from 'electron';

import type { Account } from './shared/types/account';
import type { DiscoveredConfig } from './shared/types/protocol';

/**
 * @file Preload script for the renderer process.
 * Exposes a safe, type-strong API to the renderer for interacting with the main process.
 */

export const ipcApi = {
  discoverEmailConfig: (email: string, force?: boolean): Promise<DiscoveredConfig | null> => ipcRenderer.invoke('discover:email-config', email, force),
  getAccounts: (): Promise<Account[]> => ipcRenderer.invoke('accounts:get'),
  addAccount: (account: Omit<Account, 'id'>): Promise<Account> => ipcRenderer.invoke('accounts:add', account),
  updateAccount: (accountId: string, accountData: Partial<Omit<Account, 'id'>>): Promise<Account> => ipcRenderer.invoke('accounts:update', accountId, accountData),
  deleteAccount: (accountId: string): Promise<{ success: boolean }> => ipcRenderer.invoke('accounts:delete', accountId),
  importFromFile: (): Promise<unknown> => ipcRenderer.invoke('accounts:import-from-file'),
  importFromFileInstant: (): Promise<unknown> => ipcRenderer.invoke('accounts:import-from-file-instant'),
  importFromFileContent: (content: string): Promise<unknown> => ipcRenderer.invoke('accounts:import-from-file-content', content),

  // Domain management
  getDomains: (): Promise<Record<string, DiscoveredConfig>> => ipcRenderer.invoke('domains:get'),
  saveDomain: (domain: string, config: DiscoveredConfig): Promise<{ success: boolean }> => ipcRenderer.invoke('domains:save', domain, config),
  
  // IMAP operations
  watchInbox: (accountId: string): Promise<unknown> => ipcRenderer.invoke('imap:watch-inbox', accountId),
  selectMailbox: (accountId: string, mailboxName: string, limit: number): Promise<unknown> => ipcRenderer.invoke('imap:select-mailbox', accountId, mailboxName, limit),
  getMailboxes: (accountId: string): Promise<unknown> => ipcRenderer.invoke('imap:getMailboxes', accountId),
  getEmails: (accountId: string, mailboxName: string, offset: number, limit: number): Promise<unknown> => ipcRenderer.invoke('imap:getEmails', accountId, mailboxName, offset, limit),
  getEmailBody: (accountId: string, mailbox: string, emailUid: number): Promise<unknown> => ipcRenderer.invoke('imap:getEmailBody', accountId, mailbox, emailUid),
  deleteEmail: (accountId: string, mailbox: string, emailUid: number): Promise<unknown> => ipcRenderer.invoke('imap:deleteEmail', accountId, mailbox, emailUid),
  markAsSeen: (accountId: string, mailbox: string, emailUid: number): Promise<unknown> => ipcRenderer.invoke('imap:markAsSeen', accountId, mailbox, emailUid),
  markAsUnseen: (accountId: string, mailbox: string, emailUid: number): Promise<unknown> => ipcRenderer.invoke('imap:markAsUnseen', accountId, mailbox, emailUid),
  deleteEmails: (accountId: string, mailbox: string, uids: number[]): Promise<unknown> => ipcRenderer.invoke('imap:deleteEmails', accountId, mailbox, uids),

  // Global proxy management
  proxy: {
    getGlobal: (): Promise<unknown> => ipcRenderer.invoke('proxy:get-global'),
    setGlobal: (config: unknown): Promise<unknown> => ipcRenderer.invoke('proxy:set-global', config),
    onStatusUpdate: (callback: (_event: unknown, _status: unknown) => void): (() => void) => {
      ipcRenderer.on('proxy:status-update', callback);
      return (): void => { ipcRenderer.removeListener('proxy:status-update', callback); };
    },
  },
  
  // Proxy list management
  getProxyList: (): Promise<unknown> => ipcRenderer.invoke('proxy:get-list'),
  saveProxyList: (proxies: unknown[]): Promise<unknown> => ipcRenderer.invoke('proxy:save-list', proxies),
  testProxy: (proxy: unknown): Promise<unknown> => ipcRenderer.invoke('proxy:test', proxy),

  // User configuration management
  getUserConfig: (): Promise<unknown> => ipcRenderer.invoke('config:get-user'),
  saveUserConfig: (config: unknown): Promise<unknown> => ipcRenderer.invoke('config:save-user', config),
  resetAllConfig: (): Promise<unknown> => ipcRenderer.invoke('config:reset-all'),

  // Listener for logs from the main process
  onLog: (callback: (_event: unknown, _log: { level: 'info' | 'success' | 'error'; message: string }) => void): (() => void) => {
    ipcRenderer.on('log:add', callback);
    // Return a cleanup function to remove the listener
    return (): void => { ipcRenderer.removeListener('log:add', callback); };
  },

  onNewMail: (callback: (_event: unknown, _data: { accountId: string; mailboxName: string; newMailCount: number }) => void): (() => void) => {
    const handler = (_event: unknown, data: unknown): void => callback(_event, data as { accountId: string; mailboxName: string; newMailCount: number });
    ipcRenderer.on('mail:new', handler);
    return (): void => { ipcRenderer.removeListener('mail:new', handler); };
  },

  // Notify main process that the renderer is ready
  rendererReady: (): Promise<unknown> => ipcRenderer.invoke('renderer:ready'),

  // Listeners for events from the main process
  on: (channel: string, callback: (..._args: unknown[]) => void): void => {
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
  },
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('ipcApi', ipcApi);


---

// FILE: src\renderer.tsx

import './index.css';
import { createRoot } from 'react-dom/client';

import App from './app';

const container = document.getElementById('root');

if (container) {
  const root = createRoot(container);
  root.render(
    <App />
  );
} else {
  throw new Error("Could not find root element to mount to");
} 

---

// FILE: src\services\accountImportService.ts

/**
 * @file High-performance account import service with data normalization
 * Handles parsing and normalization of IMAP account data from various formats
 */

import { Buffer } from 'buffer';
import fs from 'fs';

import { z } from 'zod';

import type { Account } from '../shared/types/account';

// Validation schema for parsed account data
const parsedAccountSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
  domain: z.string().optional(),
});

export type ParsedAccount = z.infer<typeof parsedAccountSchema>;

export interface EmailServerConfig {
  imap?: {
    host: string;
    port: number;
    secure: boolean;
  };
  smtp?: {
    host: string;
    port: number;
    secure: boolean;
  };
}

export interface ImportProgress {
  totalLines: number;
  processedLines: number;
  validAccounts: number;
  skippedLines: number;
  currentLine?: string;
  phase: 'reading' | 'parsing' | 'validating' | 'configuring' | 'complete';
}

export interface ImportResult {
  success: boolean;
  accounts: ParsedAccount[];
  skippedLines: number;
  totalLines: number;
  errors: string[];
}

export interface ImportPreview {
  totalLines: number;
  sampleAccounts: ParsedAccount[];
  estimatedValidAccounts: number;
  detectedSeparators: string[];
  fileSize: number;
}

/**
 * Advanced data normalizer for IMAP account files
 */
export class AccountImportService {
  private static readonly SUPPORTED_SEPARATORS = [':', ';', '|', '\t', ','];
  private static readonly EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  private static readonly CHUNK_SIZE = 1024 * 64; // 64KB chunks for streaming
  private static readonly MAX_PREVIEW_LINES = 100;

  /**
   * Detects the most likely separator used in the file
   */
  private static detectSeparator(lines: string[]): string {
    const separatorCounts = new Map<string, number>();
    
    for (const line of lines.slice(0, 50)) { // Check first 50 lines
      if (line.trim().length === 0) continue;
      
      for (const sep of this.SUPPORTED_SEPARATORS) {
        const parts = line.split(sep);
        if (parts.length >= 2 && this.EMAIL_REGEX.test(parts[0].trim())) {
          separatorCounts.set(sep, (separatorCounts.get(sep) ?? 0) + 1);
        }
      }
    }

    // Return the separator with the highest count
    let bestSeparator = ':';
    let maxCount = 0;
    
    for (const [sep, count] of separatorCounts) {
      if (count > maxCount) {
        maxCount = count;
        bestSeparator = sep;
      }
    }

    return bestSeparator;
  }

  /**
   * Normalizes and cleans a single line of account data
   */
  private static normalizeLine(line: string, separator: string): ParsedAccount | null {
    // Remove common junk characters and normalize whitespace
    const cleanLine = line
      .replace(/[^\w@.\-:;|,\s]/g, '') // Remove special chars except email-safe ones
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    if (!cleanLine) return null;

    const parts = cleanLine.split(separator);
    if (parts.length < 2) return null;

    const email = parts[0].trim();
    const password = parts.slice(1).join(separator).trim();

    // Validate email format
    if (!this.EMAIL_REGEX.test(email)) return null;
    if (!password) return null;

    try {
      const parsed = parsedAccountSchema.parse({
        email,
        password,
        domain: email.split('@')[1],
      });
      return parsed;
    } catch {
      return null;
    }
  }

  /**
   * Generates a preview of the import file
   */
  static async generatePreview(filePath: string): Promise<ImportPreview> {
    const stats = await fs.promises.stat(filePath);
    const fileSize = stats.size;

    // Read first chunk for preview
    const buffer = Buffer.alloc(Math.min(this.CHUNK_SIZE, fileSize));
    const fd = await fs.promises.open(filePath, 'r');
    await fd.read(buffer, 0, buffer.length, 0);
    await fd.close();

    const content = buffer.toString('utf8');
    const lines = content.split(/\r?\n/).filter(line => line.trim().length > 0);
    
    // Estimate total lines based on average line length
    const avgLineLength = content.length / lines.length;
    const estimatedTotalLines = Math.ceil(fileSize / avgLineLength);

    // Detect separators
    const detectedSeparators = this.SUPPORTED_SEPARATORS.filter(sep => {
      return lines.some(line => {
        const parts = line.split(sep);
        return parts.length >= 2 && this.EMAIL_REGEX.test(parts[0].trim());
      });
    });

    const primarySeparator = this.detectSeparator(lines);
    
    // Parse sample accounts
    const sampleAccounts: ParsedAccount[] = [];
    let validCount = 0;

    for (const line of lines.slice(0, this.MAX_PREVIEW_LINES)) {
      const parsed = this.normalizeLine(line, primarySeparator);
      if (parsed) {
        validCount++;
        if (sampleAccounts.length < 10) {
          sampleAccounts.push(parsed);
        }
      }
    }

    // Estimate valid accounts in entire file
    const sampleValidRatio = validCount / Math.min(lines.length, this.MAX_PREVIEW_LINES);
    const estimatedValidAccounts = Math.ceil(estimatedTotalLines * sampleValidRatio);

    return {
      totalLines: estimatedTotalLines,
      sampleAccounts,
      estimatedValidAccounts,
      detectedSeparators,
      fileSize,
    };
  }

  /**
   * Parses the entire file with progress reporting
   */
  static async parseFile(
    filePath: string,
    onProgress?: (progress: ImportProgress) => void
  ): Promise<ImportResult> {
    const errors: string[] = [];
    const accounts: ParsedAccount[] = [];
    let totalLines = 0;
    let processedLines = 0;
    let skippedLines = 0;

    try {
      // First pass: count total lines for progress tracking
      onProgress?.({
        totalLines: 0,
        processedLines: 0,
        validAccounts: 0,
        skippedLines: 0,
        phase: 'reading',
      });

      const content = await fs.promises.readFile(filePath, 'utf8');
      const lines = content.split(/\r?\n/).filter(line => line.trim().length > 0);
      totalLines = lines.length;

      // Detect separator
      const separator = this.detectSeparator(lines);

      onProgress?.({
        totalLines,
        processedLines: 0,
        validAccounts: 0,
        skippedLines: 0,
        phase: 'parsing',
      });

      // Process lines in chunks to avoid blocking
      const chunkSize = 1000;
      for (let i = 0; i < lines.length; i += chunkSize) {
        const chunk = lines.slice(i, i + chunkSize);
        
        for (const line of chunk) {
          processedLines++;
          
          const parsed = this.normalizeLine(line, separator);
          if (parsed) {
            accounts.push(parsed);
          } else {
            skippedLines++;
          }

          // Report progress every 100 lines
          if (processedLines % 100 === 0) {
            onProgress?.({
              totalLines,
              processedLines,
              validAccounts: accounts.length,
              skippedLines,
              currentLine: `${line.substring(0, 50)  }...`,
              phase: 'parsing',
            });
          }
        }

        // Yield control to prevent blocking
        await new Promise(resolve => setTimeout(resolve, 0));
      }

      onProgress?.({
        totalLines,
        processedLines,
        validAccounts: accounts.length,
        skippedLines,
        phase: 'complete',
      });

      return {
        success: true,
        accounts,
        skippedLines,
        totalLines,
        errors,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(errorMessage);
      
      return {
        success: false,
        accounts,
        skippedLines,
        totalLines,
        errors,
      };
    }
  }

  /**
   * Converts parsed accounts to full Account objects with server discovery
   */
  static async configureAccounts(
    parsedAccounts: ParsedAccount[],
    getEmailConfig: (email: string) => Promise<EmailServerConfig | null>,
    onProgress?: (progress: ImportProgress) => void
  ): Promise<Omit<Account, 'id' | 'connectionStatus'>[]> {
    const configuredAccounts: Omit<Account, 'id' | 'connectionStatus'>[] = [];
    
    for (let i = 0; i < parsedAccounts.length; i++) {
      const parsed = parsedAccounts[i];
      
      onProgress?.({
        totalLines: parsedAccounts.length,
        processedLines: i,
        validAccounts: configuredAccounts.length,
        skippedLines: 0,
        currentLine: parsed.email,
        phase: 'configuring',
      });

      try {
        const config = await getEmailConfig(parsed.email);
        
        const account: Omit<Account, 'id' | 'connectionStatus'> = {
          displayName: parsed.email.split('@')[0],
          email: parsed.email,
          password: parsed.password,
          incoming: (config?.imap !== null && config?.imap !== undefined) ? {
            protocol: 'imap',
            host: config.imap.host,
            port: config.imap.port,
            useTls: config.imap.secure,
          } : {
            protocol: 'imap',
            host: 'imap.example.com',
            port: 993,
            useTls: true,
          },
          useProxy: false,
        };

        if (config?.smtp !== null && config?.smtp !== undefined) {
          account.outgoing = {
            protocol: 'smtp',
            host: config.smtp.host,
            port: config.smtp.port,
            useTls: config.smtp.secure,
          };
        }

        configuredAccounts.push(account);
      } catch (error) {
        // Skip accounts that fail configuration
        // eslint-disable-next-line no-console
        console.warn(`Failed to configure account ${parsed.email}:`, error);
      }

      // Yield control periodically
      if (i % 10 === 0) {
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    }

    onProgress?.({
      totalLines: parsedAccounts.length,
      processedLines: parsedAccounts.length,
      validAccounts: configuredAccounts.length,
      skippedLines: 0,
      phase: 'complete',
    });

    return configuredAccounts;
  }
}


---

// FILE: src\services\autoDiscoveryService.ts

/**
 * @file Service for auto-discovering email server settings, now with parallel execution and caching.
 */

import { discoverViaDns } from './discovery/dnsDiscovery';
import { discoverViaExchangeAutodiscover } from './discovery/exchangeDiscovery';
import { discoverViaProviderList } from './discovery/providerDiscovery';
import type { Logger, DiscoveredConfig, DiscoveryOptions } from './discovery/types';

// In-memory cache for discovered configurations
const configCache = new Map<string, DiscoveredConfig>();

/**
 * Main email configuration discovery function.
 * Tries multiple strategies in parallel and returns the first successful result.
 */
export const discoverEmailConfig = async (
  domain: string,
  logger: Logger = () => { /* default no-op logger */ },
  options: DiscoveryOptions = {}
): Promise<DiscoveredConfig | null> => {
  logger('info', `Starting email discovery for domain: ${domain}`);

  if (!domain || domain.length === 0) {
    logger('error', 'Invalid domain provided');
    return null;
  }

  const normalizedDomain = domain.replace(/^(https?:\/\/)?(www\.)?/, '').toLowerCase().trim();
  logger('info', `Normalized domain: ${normalizedDomain}`);

  const finalDomain = normalizedDomain;

  logger('info', `Final domain for discovery: ${finalDomain}`);

  // Check cache first (skip if force is true)
  if (!options.force && configCache.has(finalDomain)) {
    logger('info', `Returning cached configuration for ${finalDomain}`);
    return configCache.get(finalDomain)!;
  }

  // Clear cache if force is true
  if (options.force && configCache.has(finalDomain)) {
    logger('info', `Force discovery requested - clearing cache for ${finalDomain}`);
    configCache.delete(finalDomain);
  }

  // Create AbortController to cancel remaining strategies when one succeeds
  const abortController = new AbortController();

  const discoveryStrategies: Array<{name: string, promise: Promise<DiscoveredConfig | null>}> = [];

  if (!options.skipProviderList) {
    discoveryStrategies.push({
      name: 'Provider List',
      promise: discoverViaProviderList(finalDomain, logger)
    });
  }
  if (!options.skipDnsGuessing) {
    discoveryStrategies.push({
      name: 'DNS Discovery',
      promise: discoverViaDns(finalDomain, logger)
    });
  }
  if (!options.skipExchangeAutodiscover) {
    discoveryStrategies.push({
      name: 'Exchange Autodiscover',
      promise: discoverViaExchangeAutodiscover(finalDomain, logger)
    });
  }

  // Race all strategies and take the first one that resolves with a non-null value
  logger('info', `Running ${discoveryStrategies.length} discovery strategies for ${finalDomain}`);

  return new Promise<DiscoveredConfig | null>((resolve) => {
    let completedStrategies = 0;
    let hasResolved = false;

    discoveryStrategies.forEach((strategy, index) => {
      strategy.promise.then((res: DiscoveredConfig | null) => {
        completedStrategies++;
        logger('info', `Strategy ${index + 1} (${strategy.name}) completed for ${finalDomain}: ${res ? 'SUCCESS' : 'FAILED'}`);

        if (res && !hasResolved) {
          hasResolved = true;
          // Cancel all other strategies
          logger('info', `Cancelling remaining discovery strategies for ${finalDomain}`);
          abortController.abort();
          logger('success', `Discovery successful for ${finalDomain}`);
          configCache.set(finalDomain, res); // Cache the successful result
          resolve(res);
          return;
        }

        // If all strategies completed and none succeeded
        if (completedStrategies === discoveryStrategies.length && !hasResolved) {
          logger('info', `All discovery strategies failed for ${finalDomain}`);
          abortController.abort(); // Clean up
          resolve(null);
        }
      }).catch((error: any) => {
        completedStrategies++;
        logger('info', `Strategy ${index + 1} (${strategy.name}) failed for ${finalDomain}: ${error.message}`);

        // If all strategies completed and none succeeded
        if (completedStrategies === discoveryStrategies.length && !hasResolved) {
          logger('info', `All discovery strategies failed for ${finalDomain}`);
          abortController.abort(); // Clean up
          resolve(null);
        }
      });
    });
  });

  logger('info', `No email configuration found for ${finalDomain}`);
  return null;
};

/**
 * Simplified discovery function for common use cases.
 */
export const quickDiscoverEmailConfig = async (
  domain: string,
  logger?: Logger
): Promise<DiscoveredConfig | null> => {
  return discoverEmailConfig(domain, logger, {
    skipExchangeAutodiscover: true, // Skip complex Exchange discovery for quick results
  });
};

// Re-export types for convenience
export type { Logger, DiscoveredConfig, DiscoveryOptions } from './discovery/types';

---

// FILE: src\services\clipboardService.ts

/**
 * @file Service for clipboard operations and credential parsing
 */

// Browser API declarations for Electron renderer
declare const navigator: {
  clipboard: {
    writeText: (text: string) => Promise<void>;
    readText: () => Promise<string>;
  };
};

export interface ParsedCredentials {
  email: string;
  password: string;
}

export interface ClipboardParseResult {
  success: boolean;
  credentials?: ParsedCredentials;
  error?: string;
}

/**
 * Service for handling clipboard operations
 */
export class ClipboardService {
  /**
   * Attempts to read text from clipboard
   */
  static async readText(): Promise<string | null> {
    try {
      return await navigator.clipboard.readText();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('Could not read clipboard content:', error);
      return null;
    }
  }

  /**
   * Writes text to clipboard
   */
  static async writeText(text: string): Promise<boolean> {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Could not write to clipboard:', error);
      return false;
    }
  }

  /**
   * Parses credentials string with common separators
   */
  static parseCredentialsString(text: string): ClipboardParseResult {
    // Regex to find common separators
    const separators = /[:;|]/;

    // Check if the input value contains a separator
    if (separators.test(text)) {
      // Split the string into email and password parts
      const parts = text.split(separators);
      if (parts.length >= 2) {
        const extractedEmail = parts[0].trim();
        // Join the rest of the parts in case the separator exists in the password
        const extractedPassword = parts.slice(1).join(parts[0].match(separators)?.[0] ?? '').trim();

        // Validate email format
        if (/^\S+@\S+\.\S+$/.test(extractedEmail) && (extractedPassword?.length ?? 0) > 0) {
          return {
            success: true,
            credentials: {
              email: extractedEmail,
              password: extractedPassword
            }
          };
        }
      }
    }

    return {
      success: false,
      error: 'Invalid credentials format'
    };
  }

  /**
   * Attempts to detect and parse credentials from clipboard
   */
  static async detectCredentialsFromClipboard(): Promise<ClipboardParseResult> {
    const clipboardText = await this.readText();
    
    if (clipboardText === null || clipboardText === undefined || clipboardText.length === 0) {
      return {
        success: false,
        error: 'Could not read clipboard'
      };
    }

    const separators = /[:;|]/;

    if (separators.test(clipboardText)) {
      const parts = clipboardText.split(separators);
      const email = parts[0].trim();
      const password = parts.slice(1).join(parts[0].match(separators)?.[0] ?? '').trim();

      const emailRegex = /^\S+@\S+\.\S+$/;

      if (emailRegex.test(email) && (password?.length ?? 0) > 0) {
        return {
          success: true,
          credentials: { email, password }
        };
      } else {
        return {
          success: false,
          error: 'Clipboard content resembles credentials but format is invalid'
        };
      }
    }

    return {
      success: false,
      error: 'No credentials pattern found in clipboard'
    };
  }

  /**
   * Formats account credentials for clipboard
   */
  static formatAccountCredentials(email: string, password: string): string {
    return `${email}:${password}`;
  }

  /**
   * Copies account credentials to clipboard
   */
  static async copyAccountCredentials(email: string, password: string): Promise<boolean> {
    const formatted = this.formatAccountCredentials(email, password);
    return await this.writeText(formatted);
  }
}


---

// FILE: src\services\discovery\connectionTesting.ts

/**
 * @file Connection testing utilities for email discovery
 */

import dns from 'dns/promises';
import net from 'net';
import tls from 'tls';

import type { Logger, ConnectionTestResult } from './types';

/**
 * Checks if a hostname exists in DNS before attempting connection.
 */
export const checkHostExists = async (hostname: string): Promise<boolean> => {
  try {
    await dns.lookup(hostname);
    return true;
  } catch {
    return false;
  }
};

/**
 * Tests if a host is reachable on a given port and validates it's actually an email server.
 */
export const testConnection = async (
  host: string,
  port: number,
  secure: boolean,
  timeout = 5000
): Promise<ConnectionTestResult> => {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    let resolved = false;

    const cleanup = (): void => {
      if (!resolved) {
        resolved = true;
        socket.destroy();
      }
    };

    const timer = setTimeout(() => {
      cleanup();
      resolve({
        success: false,
        error: 'Connection timeout',
        details: { host, port, secure }
      });
    }, timeout);

    socket.setTimeout(timeout);

    socket.on('connect', () => {
      clearTimeout(timer);
      cleanup();
      resolve({
        success: true,
        details: { host, port, secure }
      });
    });

    socket.on('error', (err) => {
      clearTimeout(timer);
      cleanup();
      resolve({
        success: false,
        error: err.message,
        details: { host, port, secure }
      });
    });

    socket.on('timeout', () => {
      clearTimeout(timer);
      cleanup();
      resolve({
        success: false,
        error: 'Socket timeout',
        details: { host, port, secure }
      });
    });

    try {
      socket.connect(port, host);
    } catch (err) {
      clearTimeout(timer);
      cleanup();
      resolve({
        success: false,
        error: err instanceof Error ? err.message : 'Unknown error',
        details: { host, port, secure }
      });
    }
  });
};

/**
 * Validates if a server is actually an email server by checking response
 */
export const isValidEmailServer = async (
  host: string,
  port: number,
  secure: boolean,
  logger: Logger
): Promise<boolean> => {
  const result = await testConnection(host, port, secure);

  if (!result.success) {
    logger('info', `Connection failed to ${host}:${port} - ${result.error ?? 'Unknown error'}`);
    return false;
  }

  // For now, just check if connection is successful
  // In the future, we could add protocol-specific validation
  logger('success', `Successfully connected to ${host}:${port} (secure: ${secure})`);
  return true;
};

/**
 * Gets the real hostname from TLS certificate for secure connections
 */
export const getRealHostnameFromTLS = async (
  host: string,
  port: number,
  timeout = 5000
): Promise<string | null> => {
  return new Promise((resolve) => {
    const socket = tls.connect({
      host,
      port,
      rejectUnauthorized: false, // We just want to read the certificate
      timeout
    });

    socket.on('secureConnect', () => {
      try {
        const cert = socket.getPeerCertificate();
        socket.destroy();

        if (cert && cert.subject && cert.subject.CN) {
          const cn = cert.subject.CN;
          // Skip wildcard certificates
          if (!cn.startsWith('*.')) {
            resolve(cn);
            return;
          }
        }

        if (cert && cert.subjectaltname) {
          // Extract DNS names from Subject Alternative Names
          const dnsMatches = cert.subjectaltname.match(/DNS:([^,]+)/g);
          if (dnsMatches) {
            // First, try to find non-wildcard hostnames
            for (const match of dnsMatches) {
              const hostname = match.replace('DNS:', '');
              if (!hostname.startsWith('*.')) {
                resolve(hostname);
                return;
              }
            }

            // If only wildcards found, return the first wildcard
            // The caller will handle constructing the proper hostname
            for (const match of dnsMatches) {
              const hostname = match.replace('DNS:', '');
              if (hostname.startsWith('*.')) {
                resolve(hostname);
                return;
              }
            }
          }
        }

        resolve(null);
      } catch (error) {
        socket.destroy();
        resolve(null);
      }
    });

    socket.on('error', () => {
      socket.destroy();
      resolve(null);
    });

    socket.on('timeout', () => {
      socket.destroy();
      resolve(null);
    });
  });
};

/**
 * Enhanced validation that checks TLS certificate for secure connections
 * and returns the real hostname if different from the tested hostname
 */
export const isValidEmailHostWithRealHostname = async (
  host: string,
  port: number,
  secure: boolean,
  logger: Logger
): Promise<{ isValid: boolean; realHostname?: string }> => {
  // First check if host exists in DNS
  if (!(await checkHostExists(host))) {
    logger('info', `Host ${host} does not exist in DNS`);
    return { isValid: false };
  }

  // Then check if port is open
  const result = await testConnection(host, port, secure, 3000);

  if (!result.success) {
    logger('info', `Connection failed to ${host}:${port} - ${result.error ?? 'Unknown error'}`);
    return { isValid: false };
  }

  // For secure connections, check the real hostname from TLS certificate
  if (secure) {
    const realHostname = await getRealHostnameFromTLS(host, port);
    if (realHostname && realHostname !== host) {
      logger('info', `Host ${host}:${port} redirects to real hostname: ${realHostname}`);

      // If we got a wildcard certificate, construct proper hostname
      if (realHostname.startsWith('*.')) {
        const baseDomain = realHostname.substring(2); // Remove '*.'
        const constructedHost = `imap.${baseDomain}`;
        logger('info', `Wildcard certificate detected, using constructed hostname: ${constructedHost}`);
        return { isValid: true, realHostname: constructedHost };
      }

      // If we got a base domain (like firstmail.ltd), construct IMAP hostname
      if (realHostname && !realHostname.includes('imap') && !realHostname.includes('mail')) {
        // Check if this looks like a base domain by testing if imap.domain works
        const constructedHost = `imap.${realHostname}`;
        logger('info', `Base domain detected (${realHostname}), trying constructed hostname: ${constructedHost}`);
        return { isValid: true, realHostname: constructedHost };
      }

      return { isValid: true, realHostname };
    }
  }

  logger('success', `Host ${host}:${port} is reachable (secure: ${secure})`);
  return { isValid: true };
};

/**
 * Quick validation that just checks if the host exists and port is open
 * This is more permissive and faster than full email server validation
 */
export const isValidEmailHost = async (
  host: string,
  port: number,
  secure: boolean,
  logger: Logger
): Promise<boolean> => {
  const result = await isValidEmailHostWithRealHostname(host, port, secure, logger);
  return result.isValid;
};


---

// FILE: src\services\discovery\dnsDiscovery.ts


/**
 * @file DNS-based email discovery using modern techniques.
 */

import dns from 'dns/promises';
import { isValidEmailServer, isValidEmailHost, isValidEmailHostWithRealHostname } from './connectionTesting';
import type { Logger, DiscoveredConfig, ServerConfig } from './types';

// SRV records for standard email services
const srvRecords = [
  { service: 'imap', protocol: 'tcp', srv: '_imaps._tcp' },
  { service: 'imap', protocol: 'tcp', srv: '_imap._tcp' },
  { service: 'pop3', protocol: 'tcp', srv: '_pop3s._tcp' },
  { service: 'pop3', protocol: 'tcp', srv: '_pop3._tcp' },
  { service: 'smtp', protocol: 'tcp', srv: '_submission._tcp' }, // For SMTP
];

// Extract hostname from MX record and generate IMAP/SMTP guesses
const generateHostnamesFromMX = (mxHostname: string, domain: string): string[] => {
  const hosts = new Set<string>();

  // If MX is like mail.domain.com, try imap.domain.com, smtp.domain.com
  if (mxHostname.startsWith('mail.')) {
    const baseDomain = mxHostname.substring(5); // Remove 'mail.'
    hosts.add(`imap.${baseDomain}`);
    hosts.add(`smtp.${baseDomain}`);
    hosts.add(`pop3.${baseDomain}`);
    hosts.add(`pop.${baseDomain}`);
  }

  // If MX is like mx.domain.com or mx1.domain.com, try mail.domain.com variants
  if (mxHostname.match(/^mx\d*\./)) {
    const baseDomain = mxHostname.replace(/^mx\d*\./, '');
    hosts.add(`mail.${baseDomain}`);
    hosts.add(`imap.${baseDomain}`);
    hosts.add(`smtp.${baseDomain}`);
  }

  // Try replacing common prefixes
  const prefixReplacements = [
    { from: /^mail\./, to: ['imap.', 'smtp.', 'pop3.', 'pop.'] },
    { from: /^smtp\./, to: ['imap.', 'mail.', 'pop3.'] },
    { from: /^mx\d*\./, to: ['imap.', 'mail.', 'smtp.'] }
  ];

  for (const replacement of prefixReplacements) {
    if (replacement.from.test(mxHostname)) {
      const baseDomain = mxHostname.replace(replacement.from, '');
      for (const prefix of replacement.to) {
        hosts.add(`${prefix}${baseDomain}`);
      }
    }
  }

  // Also try the MX hostname itself as IMAP/SMTP server
  hosts.add(mxHostname);

  return Array.from(hosts);
};

// Get MX records and extract hostnames for email server guessing
const getMXBasedHostnames = async (domain: string, logger: Logger): Promise<string[]> => {
  try {
    logger('info', `[Discovery/S2-DNS-MX] Looking up MX records for ${domain}`);

    // Add timeout to MX lookup
    const mxPromise = dns.resolveMx(domain);
    const timeoutPromise = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('MX lookup timeout')), 10000)
    );

    const mxRecords = await Promise.race([mxPromise, timeoutPromise]);

    if (mxRecords.length === 0) {
      logger('info', `[Discovery/S2-DNS-MX] No MX records found for ${domain}`);
      return [];
    }

    // Sort by priority (lower number = higher priority)
    mxRecords.sort((a, b) => a.priority - b.priority);

    const allHosts = new Set<string>();

    for (const mx of mxRecords) {
      logger('info', `[Discovery/S2-DNS-MX] Found MX record: ${mx.exchange} (priority: ${mx.priority})`);
      const hostsFromMX = generateHostnamesFromMX(mx.exchange, domain);
      hostsFromMX.forEach(host => allHosts.add(host));
    }

    const result = Array.from(allHosts);
    logger('info', `[Discovery/S2-DNS-MX] Generated ${result.length} hostnames from MX records: ${result.join(', ')}`);
    return result;

  } catch (error) {
    logger('info', `[Discovery/S2-DNS-MX] Failed to resolve MX records for ${domain}: ${error}`);
    return [];
  }
};

/**
 * Strategy 2, Modernized: DNS-based discovery using SRV records and falling back to guessing.
 */
export const discoverViaDns = async (domain: string, logger: Logger): Promise<DiscoveredConfig | null> => {
  logger('info', `[Discovery/S2-DNS] Starting DNS discovery for ${domain}`);
  logger('info', `[Discovery/S2-DNS] Will test hosts: ${[
    `imap.${domain}`, `mail.${domain}`, `smtp.${domain}`, `pop.${domain}`,
    `pop3.${domain}`, `mx.${domain}`, `email.${domain}`, `mailserver.${domain}`, domain
  ].join(', ')}`);

  // First, try SRV records - the modern, standard way
  const srvResult = await discoverViaSrvRecords(domain, logger);
  if (srvResult) {
    logger('success', `[Discovery/S2-DNS] Found config via SRV records for ${domain}`);
    return srvResult;
  }

  // Fallback to guessing common hostnames
  logger('info', `[Discovery/S2-DNS] SRV lookup failed, falling back to DNS guessing for ${domain}`);
  const guessResult = await discoverViaDnsGuessing(domain, logger);
  if (guessResult) {
    logger('success', `[Discovery/S2-DNS] Found config via DNS guessing for ${domain}`);
    return guessResult;
  }

  logger('info', `[Discovery/S2-DNS] No working servers found for ${domain}`);
  return null;
};

/**
 * Discover email configuration using DNS SRV records.
 */
const discoverViaSrvRecords = async (domain: string, logger: Logger): Promise<DiscoveredConfig | null> => {
  const discovered: DiscoveredConfig = {};

  const resolutions = await Promise.all(
    srvRecords.map(async ({ service, srv }) => {
      try {
        const records = await dns.resolveSrv(`${srv}.${domain}`);
        if (records && records.length > 0) {
          // Sort by priority and weight
          records.sort((a, b) => (a.priority - b.priority) || (b.weight - a.weight));
          
          for (const record of records) {
            const { name, port } = record;
            const secure = srv.includes('s'); // _imaps, _pop3s imply SSL/TLS
            
            const isValid = await isValidEmailServer(name, port, secure, logger);
            if (isValid) {
              logger('success', `[Discovery/S2-DNS-SRV] Found working ${service.toUpperCase()} server: ${name}:${port}`);
              return { service, config: { host: name, port, secure } };
            }
          }
        }
      } catch (error: any) {
        if (error.code !== 'ENODATA' && error.code !== 'ENOTFOUND') {
          logger('warn', `[Discovery/S2-DNS-SRV] SRV lookup for ${srv}.${domain} failed: ${error.message}`);
        }
      }
      return null;
    })
  );

  resolutions.forEach(res => {
    if (res) {
      if (res.service === 'imap' && !discovered.imap) discovered.imap = res.config;
      if (res.service === 'pop3' && !discovered.pop3) discovered.pop3 = res.config;
      if (res.service === 'smtp' && !discovered.smtp) discovered.smtp = res.config;
    }
  });

  return discovered.imap || discovered.pop3 ? discovered : null;
};

/**
 * Fallback Strategy: DNS-based guessing of common server names.
 */
const discoverViaDnsGuessing = async (domain: string, logger: Logger): Promise<DiscoveredConfig | null> => {
  // Get hostnames from MX records first
  const mxBasedHosts = await getMXBasedHostnames(domain, logger);

  // Common server patterns - expanded for better coverage
  const commonHosts = [
    `imap.${domain}`,
    `mail.${domain}`,
    `smtp.${domain}`,
    `pop.${domain}`,
    `pop3.${domain}`,
    `mx.${domain}`,
    `email.${domain}`,
    `mailserver.${domain}`,
    domain,
    // Additional patterns for custom email providers
    `webmail.${domain}`,
    `server.${domain}`,
    `mail1.${domain}`,
    `mail2.${domain}`,
    `imap1.${domain}`,
    `imap2.${domain}`,
    `secure.${domain}`,
    `ssl.${domain}`
  ];

  // Combine MX-based hosts with common patterns, prioritizing MX-based
  const allHosts = [...mxBasedHosts, ...commonHosts];

  logger('info', `[Discovery/S2-DNS-Guess] Testing ${allHosts.length} hostnames: ${allHosts.join(', ')}`);
  const imapConfigs = [{ port: 993, secure: true }, { port: 143, secure: false }];
  const pop3Configs = [{ port: 995, secure: true }, { port: 110, secure: false }];
  const smtpConfigs = [{ port: 587, secure: true }, { port: 465, secure: true }, { port: 25, secure: false }];

  const testConfig = async (host: string, configs: {port: number, secure: boolean}[], type: 'imap' | 'pop3' | 'smtp'): Promise<ServerConfig | null> => {
    logger('info', `[Discovery/S2-DNS-Guess] Testing ${type.toUpperCase()} host: ${host}`);

    try {
      // Use enhanced host validation that checks TLS certificates
      for (const config of configs) {
        logger('info', `[Discovery/S2-DNS-Guess] Testing ${host}:${config.port} (secure: ${config.secure})`);

        // Add timeout to each host test
        const testPromise = isValidEmailHostWithRealHostname(host, config.port, config.secure, logger);
        const timeoutPromise = new Promise<{ isValid: boolean; realHostname?: string }>((_, reject) =>
          setTimeout(() => reject(new Error('Host test timeout')), 8000)
        );

        const result = await Promise.race([testPromise, timeoutPromise]);

        if (result.isValid) {
          let finalHost = result.realHostname || host;

          // If we got a real hostname from TLS certificate, check if it looks like a proper mail server
          if (result.realHostname && type === 'imap') {
            const realHost = result.realHostname.toLowerCase();
            logger('info', `[Discovery/S2-DNS-Guess] Checking real hostname: "${realHost}" for type: "${type}"`);
            // If the real hostname doesn't look like a mail server, add imap prefix
            if (!realHost.includes('imap') && !realHost.includes('mail') && !realHost.includes('mx')) {
              finalHost = `imap.${result.realHostname}`;
              logger('info', `[Discovery/S2-DNS-Guess] Real hostname doesn't look like mail server, using: ${finalHost}`);
            } else {
              logger('info', `[Discovery/S2-DNS-Guess] Using real hostname from TLS certificate: ${result.realHostname}`);
            }
          }

          logger('success', `[Discovery/S2-DNS-Guess] Found working ${type.toUpperCase()} server: ${finalHost}:${config.port}`);
          return { host: finalHost, ...config };
        }
      }
    } catch (error) {
      logger('info', `[Discovery/S2-DNS-Guess] Error testing ${type.toUpperCase()} host ${host}: ${error}`);
    }

    logger('info', `[Discovery/S2-DNS-Guess] No working ${type.toUpperCase()} server found for host: ${host}`);
    return null;
  };

  logger('info', `[Discovery/S2-DNS-Guess] Starting parallel testing of ${allHosts.length} hosts for IMAP/POP3/SMTP`);

  // Test IMAP first (priority), then POP3, then SMTP
  const discovered: DiscoveredConfig = {};

  // Test IMAP servers first - stop as soon as we find one
  logger('info', `[Discovery/S2-DNS-Guess] Testing IMAP servers first (priority)`);
  for (const host of allHosts) {
    const imapResult = await testConfig(host, imapConfigs, 'imap');
    if (imapResult) {
      discovered.imap = imapResult;
      logger('success', `[Discovery/S2-DNS-Guess] Found IMAP server, stopping further IMAP tests`);
      break;
    }
  }

  // Test POP3 servers if no IMAP found
  if (!discovered.imap) {
    logger('info', `[Discovery/S2-DNS-Guess] No IMAP found, testing POP3 servers`);
    for (const host of allHosts) {
      const pop3Result = await testConfig(host, pop3Configs, 'pop3');
      if (pop3Result) {
        discovered.pop3 = pop3Result;
        logger('success', `[Discovery/S2-DNS-Guess] Found POP3 server, stopping further POP3 tests`);
        break;
      }
    }
  }

  // Test SMTP servers (always useful to have)
  if (discovered.imap || discovered.pop3) {
    logger('info', `[Discovery/S2-DNS-Guess] Testing SMTP servers`);
    for (const host of allHosts) {
      const smtpResult = await testConfig(host, smtpConfigs, 'smtp');
      if (smtpResult) {
        discovered.smtp = smtpResult;
        logger('success', `[Discovery/S2-DNS-Guess] Found SMTP server, stopping further SMTP tests`);
        break;
      }
    }
  }

  logger('info', `[Discovery/S2-DNS-Guess] Sequential testing completed for ${domain}`);

  const hasResults = discovered.imap || discovered.pop3;
  logger('info', `[Discovery/S2-DNS-Guess] DNS guessing completed for ${domain}. Found: ${hasResults ? 'YES' : 'NO'}`);
  if (hasResults) {
    if (discovered.imap) logger('success', `[Discovery/S2-DNS-Guess] IMAP: ${discovered.imap.host}:${discovered.imap.port}`);
    if (discovered.pop3) logger('success', `[Discovery/S2-DNS-Guess] POP3: ${discovered.pop3.host}:${discovered.pop3.port}`);
    if (discovered.smtp) logger('success', `[Discovery/S2-DNS-Guess] SMTP: ${discovered.smtp.host}:${discovered.smtp.port}`);
  }

  return hasResults ? discovered : null;
};


---

// FILE: src\services\discovery\exchangeDiscovery.ts

/**
 * @file Microsoft Exchange Autodiscover implementation using modern techniques.
 */

import dns from 'dns/promises';
import { checkHostExists, isValidEmailServer } from './connectionTesting';
import type { Logger, DiscoveredConfig } from './types';

declare const fetch: (_url: string, _options?: RequestInit) => Promise<Response>;

/**
 * Strategy 3, Modernized: Microsoft Exchange Autodiscover.
 */
export const discoverViaExchangeAutodiscover = async (domain: string, logger: Logger): Promise<DiscoveredConfig | null> => {
  logger('info', `[Discovery/S3-Exchange] Starting Exchange Autodiscover for ${domain}`);

  const autodiscoverEndpoints = await getAutodiscoverEndpoints(domain, logger);

  if (autodiscoverEndpoints.length === 0) {
    logger('info', `[Discovery/S3-Exchange] No Autodiscover endpoints found for ${domain}`);
    return null;
  }

  const results = await Promise.all(
    autodiscoverEndpoints.map(url => tryExchangeAutodiscoverUrl(url, domain, logger))
  );

  const successfulResult = results.find(result => result !== null);

  if (successfulResult) {
    logger('success', `[Discovery/S3-Exchange] Successfully discovered configuration for ${domain}`);
    return successfulResult;
  }

  logger('info', `[Discovery/S3-Exchange] No working Exchange configuration found for ${domain}`);
  return null;
};

/**
 * Gets potential Autodiscover endpoints from SRV records and common URLs.
 */
const getAutodiscoverEndpoints = async (domain: string, logger: Logger): Promise<string[]> => {
  const endpoints = new Set<string>();

  // 1. SRV record
  try {
    const records = await dns.resolveSrv(`_autodiscover._tcp.${domain}`);
    if (records && records.length > 0) {
      records.sort((a, b) => a.priority - b.priority || b.weight - a.weight);
      for (const record of records) {
        endpoints.add(`https://${record.name}:${record.port}/autodiscover/autodiscover.xml`);
      }
      logger('info', `[Discovery/S3-Exchange] Found Autodiscover SRV records for ${domain}`);
    }
  } catch (error: any) {
    if (error.code !== 'ENODATA' && error.code !== 'ENOTFOUND') {
      logger('warn', `[Discovery/S3-Exchange] SRV lookup failed: ${error.message}`);
    }
  }

  // 2. Standard URLs
  endpoints.add(`https://autodiscover.${domain}/autodiscover/autodiscover.xml`);
  endpoints.add(`https://${domain}/autodiscover/autodiscover.xml`);

  return Array.from(endpoints);
};

/**
 * Attempts Exchange Autodiscover for a specific URL.
 */
async function tryExchangeAutodiscoverUrl(url: string, domain: string, logger: Logger): Promise<DiscoveredConfig | null> {
  try {
    const hostname = new URL(url).hostname;
    if (!await checkHostExists(hostname)) {
      logger('info', `[Discovery/S3-Exchange] Host ${hostname} does not exist.`);
      return null;
    }

    logger('info', `[Discovery/S3-Exchange] Trying URL: ${url}`);
    const requestBody = createAutodiscoverRequestXml(domain);

    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'text/xml; charset=utf-8' },
      body: requestBody,
      signal: AbortSignal.timeout(10000),
    });

    if (!response.ok) {
      logger('info', `[Discovery/S3-Exchange] HTTP ${response.status} from ${url}`);
      return null;
    }

    const xmlText = await response.text();
    const config = parseExchangeAutodiscoverXml(xmlText, logger);

    if (config && config.imap) {
        const { host, port, secure } = config.imap;
        if (await isValidEmailServer(host, port, secure, logger)) {
            logger('success', `[Discovery/S3-Exchange] Validated IMAP server at ${host}:${port}`);
            return config;
        }
    }
    return null;

  } catch (error) {
    logger('warn', `[Discovery/S3-Exchange] Request to ${url} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return null;
  }
}

/**
 * Creates the XML request body for Exchange Autodiscover.
 */
function createAutodiscoverRequestXml(emailAddress: string): string {
    const email = `user@${emailAddress}`;
    return `<?xml version="1.0" encoding="utf-8"?>
<Autodiscover xmlns="http://schemas.microsoft.com/exchange/autodiscover/mobilesync/requestschema/2006">
    <Request>
        <EMailAddress>${email}</EMailAddress>
        <AcceptableResponseSchema>http://schemas.microsoft.com/exchange/autodiscover/mobilesync/responseschema/2006</AcceptableResponseSchema>
    </Request>
</Autodiscover>`;
}

/**
 * Parses Exchange Autodiscover XML response with improved robustness.
 */
function parseExchangeAutodiscoverXml(xmlText: string, logger: Logger): DiscoveredConfig | null {
  try {
    const config: DiscoveredConfig = {};

    const extractValue = (tagName: string) => {
        const match = xmlText.match(new RegExp(`<${tagName}>(.*?)</${tagName}>`, 'i'));
        return match ? match[1] : null;
    };

    const server = extractValue('Server');
    const type = extractValue('Type');

    if (server && type) {
        if (type.toLowerCase() === 'imap') {
            config.imap = {
                host: server,
                port: 993, // Default IMAPS port
                secure: true,
            };
            logger('info', `[Discovery/S3-Exchange] Parsed IMAP server: ${server}`);
        }
        // Can be extended for POP3/SMTP if needed
    }

    return config.imap ? config : null;
  } catch (error) {
    logger('error', `[Discovery/S3-Exchange] Failed to parse XML: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return null;
  }
}

---

// FILE: src\services\discovery\providerDiscovery.ts

/**
 * @file Provider-based email discovery
 */

import { imapProviders } from '../../shared/store/imapProviders';

import { isValidEmailServer } from './connectionTesting';
import type { Logger, DiscoveredConfig } from './types';

/**
 * Strategy 1: Look up in our predefined list of common providers.
 */
export const discoverViaProviderList = async (domain: string, logger: Logger): Promise<DiscoveredConfig | null> => {
  logger('info', `[Discovery/S1-Providers] Checking known providers for ${domain}`);
  
  const provider = imapProviders.find(p => p.domains.includes(domain));
  if (!provider) {
    logger('info', `[Discovery/S1-Providers] No known provider found for ${domain}`);
    return null;
  }

  logger('info', `[Discovery/S1-Providers] Found provider: ${provider.name}`);
  
  // Test the provider's IMAP configuration
  if (provider.config.imap !== null && provider.config.imap !== undefined) {
    const isValid = await isValidEmailServer(
      provider.config.imap.host,
      provider.config.imap.port,
      provider.config.imap.secure,
      logger
    );

    if (isValid) {
      logger('success', `[Discovery/S1-Providers] Successfully validated ${provider.name} IMAP server`);
      return {
        imap: {
          host: provider.config.imap.host,
          port: provider.config.imap.port,
          secure: provider.config.imap.secure
        },
        smtp: provider.config.smtp ? {
          host: provider.config.smtp.host,
          port: provider.config.smtp.port,
          secure: provider.config.smtp.secure
        } : undefined
      };
    }
  }

  logger('info', `[Discovery/S1-Providers] Provider ${provider.name} validation failed`);
  return null;
};


---

// FILE: src\services\discovery\types.ts

/**
 * @file Types for email discovery services
 */

import type { DiscoveredConfig, ServerConfig } from '../../shared/types/protocol';

export type Logger = (_level: 'info' | 'success' | 'error' | 'warn', _message: string) => void;

export interface ConnectionTestResult {
  success: boolean;
  error?: string;
  details?: {
    host: string;
    port: number;
    secure: boolean;
  };
}

export interface DiscoveryStrategy {
  name: string;
  discover: (_domain: string, _logger: Logger) => Promise<DiscoveredConfig | null>;
}

export interface DiscoveryOptions {
  timeout?: number;
  retries?: number;
  skipProviderList?: boolean;
  skipDnsGuessing?: boolean;
  skipExchangeAutodiscover?: boolean;
  force?: boolean;
}

export { type DiscoveredConfig, type ServerConfig };

---

// FILE: src\services\emailSanitizationService.ts

/**
 * @file Email content sanitization service
 * Simple service for handling email content display
 */

export interface EmailContent {
  html?: string | false;
  text?: string;
  textAsHtml?: string;
}

/**
 * Simple service for email content handling
 */
export class EmailSanitizationService {
  /**
   * Legacy method for backward compatibility
   * Now just returns a simple message since logic moved to component
   */
  static getSanitizedHtml(email: EmailContent | null): string {
    if (!email) return '<div style="color: white; padding: 20px;"><p>No email content available</p></div>';
    
    // Simple fallback - prefer textAsHtml, then text
    if (email.textAsHtml !== null && email.textAsHtml !== undefined && typeof email.textAsHtml === 'string' && email.textAsHtml.length > 0) {
      return email.textAsHtml;
    }

    if (email.text !== null && email.text !== undefined && typeof email.text === 'string' && email.text.length > 0) {
      return `<div style="white-space: pre-wrap; word-wrap: break-word; font-family: inherit; line-height: 1.6; color: white;">${email.text}</div>`;
    }
    
    return '<div style="color: white; padding: 20px;"><p>No content available</p></div>';
  }

  /**
   * Checks if email content contains suspicious elements
   */
  static hasSuspiciousContent(email: EmailContent | null): boolean {
    if (!email) return false;

    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe/i,
      /<object/i,
      /<embed/i,
    ];

    const contentToCheck = [
      email.html,
      email.textAsHtml,
      email.text
    ].filter(Boolean).join(' ');

    return suspiciousPatterns.some(pattern => pattern.test(contentToCheck));
  }
}


---

// FILE: src\services\imapFlowConnectionManager.ts

/**
 * @file Manages active IMAP connections for user accounts using ImapFlow.
 */
import type { ImapFlow } from 'imapflow';

const activeConnections = new Map<string, ImapFlow>();

export const imapFlowConnectionManager = {
  get(accountId: string): ImapFlow | undefined {
    const imap = activeConnections.get(accountId);
    // Проверка на активное соединение
    if (imap?.usable === true) {
      return imap;
    }
    // Если соединение неактивно, удаляем его
    if (imap) {
      activeConnections.delete(accountId);
    }
    return undefined;
  },

  set(accountId: string, imap: ImapFlow): void {
    const existingConnection = activeConnections.get(accountId);
    if (existingConnection && existingConnection !== imap && existingConnection.usable) {
      existingConnection.logout().catch(err => {
        // eslint-disable-next-line no-console
        console.error(`Error logging out existing connection for ${accountId}:`, err);
        existingConnection.close();
      });
    }

    activeConnections.set(accountId, imap);
    
    // Добавляем обработчик ошибок
    imap.on('error', (err) => {
      // eslint-disable-next-line no-console
      console.error(`IMAP error for account ${accountId}:`, err);
    });

    // Обработчик закрытия соединения
    imap.on('close', () => {
      // eslint-disable-next-line no-console
      console.log(`IMAP connection closed for account ${accountId}`);
      activeConnections.delete(accountId);
    });
  },

  async end(accountId: string): Promise<void> {
    const imap = activeConnections.get(accountId);
    if (imap) {
      try {
        // Корректное закрытие сессии
        if (imap.usable) {
          await imap.logout();
        } else {
          imap.close();
        }
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error(`Error closing connection for account ${accountId}:`, err);
        // В случае ошибки принудительно закрываем соединение
        imap.close();
      }
      activeConnections.delete(accountId);
      // eslint-disable-next-line no-console
      console.log(`Closed connection for account ${accountId}`);
    }
  },

  async endAll(): Promise<void> {
    // Создаем массив промисов для закрытия всех соединений
    const closePromises = Array.from(activeConnections.entries()).map(
      async ([accountId, imap]) => {
        try {
          // eslint-disable-next-line no-console
          console.log(`Closing connection for ${accountId}`);
          if (imap.usable) {
            await imap.logout();
          } else {
            imap.close();
          }
        } catch (err) {
          // eslint-disable-next-line no-console
          console.error(`Error closing connection for ${accountId}:`, err);
          imap.close();
        }
      }
    );

    // Ждем завершения всех закрытий
    await Promise.allSettled(closePromises);
    activeConnections.clear();
    // eslint-disable-next-line no-console
    console.log('All active IMAP connections have been closed.');
  }
}; 

---

// FILE: src\services\imapFlowService.ts

import { ImapFlow } from 'imapflow';
import { simpleParser } from 'mailparser';

// Proxy agents - imported but not yet implemented
// import { HttpsProxyAgent } from 'https-proxy-agent';
// import { SocksProxyAgent } from 'socks-proxy-agent';

import type { Account } from '../shared/types/account';
import type { EmailHeader } from '../shared/types/email';


import { validateMailbox, calculateMessageRange, createEmailHeader } from './utils/emailProcessing';
import { logImapError, createImapConfig, configureProxy } from './utils/imapErrorHandling';

// Interface for IMAP mailbox object
interface ImapMailboxInfo {
  exists: number;
  recent: number;
  flags: Set<string>;
  permanentFlags: Set<string>;
  uidValidity: number;
  uidNext: number;
}

/**
 * @file Service for handling IMAP connections and operations using ImapFlow.
 */

/**
 * Connects to an IMAP server using the provided account details.
 * @param {Account} account - The account to connect with.
 * @param {(message: string, level?: 'info' | 'error' | 'success') => void} logCallback - Optional callback for logging.
 * @returns {Promise<ImapFlow>} A promise that resolves with the connected ImapFlow instance.
 */
export function connectToAccount(
  account: Account,
  logCallback: (message: string, level?: 'info' | 'error' | 'success') => void = () => {}
): Promise<{ imap: ImapFlow; proxyUsed: boolean }> {
  return new Promise((resolve, reject) => {
    // We can only connect if the configured protocol is IMAP.
    if (account.incoming.protocol !== 'imap') {
      return reject(new Error(`Account is configured for ${account.incoming.protocol}, not IMAP.`));
    }

    // Proxy configuration
    const { proxy, proxyUsed } = configureProxy(account, logCallback);

    const imap = new ImapFlow(createImapConfig(account, proxy));

    // Настройка обработчиков событий
    imap.on('error', (err) => {
      logImapError(err, account.email, account.incoming.host, logCallback);
      reject(err);
    });

    // Подключаемся
    // eslint-disable-next-line no-console
    console.log(`Attempting to connect to ${account.incoming.host} for ${account.email}...`);
    imap.connect()
      .then(() => {
        // eslint-disable-next-line no-console
        console.log(`IMAP connection ready for ${account.email}`);
        resolve({ imap, proxyUsed });
      })
      .catch(reject);
  });
}

/**
 * Fetches the list of mailboxes from a connected IMAP instance.
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @returns {Promise<Array>} A promise that resolves with the mailboxes.
 */
export async function getMailboxes(imap: ImapFlow): Promise<unknown[]> {
  const list = await imap.list();
  return list;
}

/**
 * Fetches a paginated list of email headers from a mailbox.
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @param {string} mailboxName - The name of the mailbox to fetch from.
 * @param {number} offset - The number of emails to skip from the newest.
 * @param {number} limit - The maximum number of emails to fetch.
 * @returns {Promise<EmailHeader[]>} A promise that resolves with an array of email headers.
 */
export async function fetchEmails(imap: ImapFlow, mailboxName: string, offset: number, limit: number): Promise<EmailHeader[]> {
  // Открываем почтовый ящик
  const lock = await imap.getMailboxLock(mailboxName);
  try {
    validateMailbox(imap);

    const totalMessages = (imap.mailbox as unknown as ImapMailboxInfo).exists ?? 0;
    const { start, end } = calculateMessageRange(totalMessages, offset, limit);

    if (start > end) {
      return [];
    }

    const headers: EmailHeader[] = [];
    for await (const message of imap.fetch(`${start}:${end}`, { envelope: true, flags: true })) {
      if (message.envelope && message.flags) {
        // Адаптируем FetchMessageObject к ожидаемому типу
        const adaptedMessage = {
          uid: message.uid,
          envelope: {
            from: message.envelope.from,
            subject: message.envelope.subject,
            date: message.envelope.date
          },
          flags: message.flags
        };
        headers.push(createEmailHeader(adaptedMessage));
      }
    }

    return headers.reverse(); // Реверс для показа новых первыми
  } finally {
    lock.release();
  }
}

/**
 * Fetches the full body of a specific email.
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @param {string} mailboxName - The name of the mailbox.
 * @param {number} uid - The UID of the email to fetch.
 * @returns {Promise<any>} A promise that resolves with the parsed email body.
 */
export async function fetchEmailBody(
  imap: ImapFlow,
  mailboxName: string,
  uid: number,
  logCallback: (message: string, level?: 'info' | 'error' | 'success') => void = () => {}
): Promise<unknown> {
  const lock = await imap.getMailboxLock(mailboxName);
  try {
    logCallback(`UID ${uid}: Fetching metadata...`);
    const metadataPromise = imap.fetchOne(String(uid), { envelope: true, flags: true }, { uid: true });

    logCallback(`UID ${uid}: Downloading content...`);
    const { content } = await imap.download(String(uid), undefined, { uid: true });
    
    if (content === null || content === undefined) {
      throw new Error(`Could not download content for message UID ${uid}`);
    }

    logCallback(`UID ${uid}: Parsing content and waiting for metadata...`);
    const [parsed, messageMeta] = await Promise.all([
      simpleParser(content),
      metadataPromise
    ]);

    if (messageMeta === null || messageMeta === undefined) {
      throw new Error(`Could not fetch metadata for message UID ${uid}`);
    }

    logCallback(`UID ${uid}: Assembling final email object...`);
    const finalDate = parsed.date ?? messageMeta.envelope?.date;

    return {
      ...parsed,
      date: finalDate ? finalDate.toISOString() : new Date().toISOString(),
      flags: Array.from(messageMeta.flags ?? []),
      uid: messageMeta.uid,
    };
  } finally {
    lock.release();
  }
}

/**
 * Marks a specific email for deletion.
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @param {string} mailboxName - The name of the mailbox.
 * @param {number} uid - The UID of the email to delete.
 * @returns {Promise<void>} A promise that resolves when the email is marked as deleted.
 */
export async function deleteEmail(imap: ImapFlow, mailboxName: string, uid: number): Promise<void> {
  const lock = await imap.getMailboxLock(mailboxName, { readonly: false });
  try {
    await imap.messageDelete(String(uid), { uid: true });
  } finally {
    lock.release();
  }
}

/**
 * Marks an email as read (seen).
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @param {string} mailboxName - The name of the mailbox.
 * @param {number} uid - The UID of the email.
 * @returns {Promise<void>}
 */
export async function markAsSeen(imap: ImapFlow, mailboxName: string, uid: number): Promise<void> {
  const lock = await imap.getMailboxLock(mailboxName, { readonly: false });
  try {
    await imap.messageFlagsAdd(String(uid), ['\\Seen'], { uid: true });
  } finally {
    lock.release();
  }
}

/**
 * Marks an email as unread (unseen).
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @param {string} mailboxName - The name of the mailbox.
 * @param {number} uid - The UID of the email.
 * @returns {Promise<void>}
 */
export async function markAsUnseen(imap: ImapFlow, mailboxName: string, uid: number): Promise<void> {
  const lock = await imap.getMailboxLock(mailboxName, { readonly: false });
  try {
    await imap.messageFlagsRemove(String(uid), ['\\Seen'], { uid: true });
  } finally {
    lock.release();
  }
}

/**
 * Marks multiple emails for deletion.
 * @param {ImapFlow} imap - The connected IMAP instance.
 * @param {string} mailboxName - The name of the mailbox.
 * @param {number[]} uids - Array of UIDs to delete.
 * @returns {Promise<void>}
 */
export async function deleteEmails(imap: ImapFlow, mailboxName: string, uids: number[]): Promise<void> {
  if (uids.length === 0) return;
  
  const lock = await imap.getMailboxLock(mailboxName, { readonly: false });
  try {
    // ImapFlow принимает строку или массив строк для UID
    const uidString = uids.map(uid => String(uid)).join(',');
    await imap.messageDelete(uidString, { uid: true });
  } finally {
    lock.release();
  }
} 

---

// FILE: src\services\instantImportService.ts

/**
 * @file Instant import service with background DNS discovery
 * Imports accounts immediately and discovers server settings in background
 */

import fs from 'fs';

import { z } from 'zod';

import { imapProviders } from '../shared/store/imapProviders';
import type { Account } from '../shared/types/account';

import type { DiscoveredConfig } from './autoDiscoveryService';
import { addAccounts, updateAccount } from './storeService';

// Validation schema for parsed account data
const parsedAccountSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
  domain: z.string().optional(),
});

export type ParsedAccount = z.infer<typeof parsedAccountSchema>;

export interface InstantImportResult {
  success: boolean;
  addedCount: number;
  skippedCount: number;
  totalCount: number;
  error?: string;
}

/**
 * Instant import service that adds accounts immediately and discovers DNS in background
 */
export class InstantImportService {
  private static readonly SUPPORTED_SEPARATORS = [':', ';', '|', '\t', ','];
  private static readonly EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  private static readonly domainConfigCache = new Map<string, DiscoveredConfig>();

  /**
   * Detects the most likely separator used in the file
   */
  private static detectSeparator(lines: string[]): string {
    const separatorCounts = new Map<string, number>();
    
    for (const line of lines.slice(0, 50)) { // Check first 50 lines
      if (line.trim().length === 0) continue;
      
      for (const sep of this.SUPPORTED_SEPARATORS) {
        const parts = line.split(sep);
        if (parts.length >= 2 && this.EMAIL_REGEX.test(parts[0].trim())) {
          separatorCounts.set(sep, (separatorCounts.get(sep) ?? 0) + 1);
        }
      }
    }

    // Return the separator with the highest count
    let bestSeparator = ':';
    let maxCount = 0;
    
    for (const [sep, count] of separatorCounts) {
      if (count > maxCount) {
        maxCount = count;
        bestSeparator = sep;
      }
    }

    return bestSeparator;
  }

  /**
   * Normalizes and cleans a single line of account data
   */
  private static normalizeLine(line: string, separator: string): ParsedAccount | null {
    // Remove common junk characters and normalize whitespace
    const cleanLine = line
      .replace(/[^\w@.\-:;|,\s]/g, '') // Remove special chars except email-safe ones
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    if (!cleanLine) return null;

    const parts = cleanLine.split(separator);
    if (parts.length < 2) return null;

    const email = parts[0].trim();
    const password = parts.slice(1).join(separator).trim();

    // Validate email format
    if (!this.EMAIL_REGEX.test(email)) return null;
    if (!password) return null;

    try {
      const parsed = parsedAccountSchema.parse({
        email,
        password,
        domain: email.split('@')[1],
      });
      return parsed;
    } catch {
      return null;
    }
  }

  /**
   * Creates basic account objects with smart default server settings
   * Uses built-in provider configs when available, falls back to generic defaults
   */
  private static createBasicAccounts(parsedAccounts: ParsedAccount[]): Omit<Account, 'id' | 'connectionStatus'>[] {
    return parsedAccounts.map(parsed => {
      const domain = parsed.domain ?? parsed.email.split('@')[1];
      const providerConfig = this.findProviderConfig(domain);

      let incoming, outgoing;

      if (providerConfig?.imap) {
        // Use known provider configuration
        incoming = {
          protocol: 'imap' as const,
          host: providerConfig.imap.host,
          port: providerConfig.imap.port,
          useTls: providerConfig.imap.secure,
        };

        if (providerConfig.smtp !== null && providerConfig.smtp !== undefined) {
          outgoing = {
            protocol: 'smtp' as const,
            host: providerConfig.smtp.host,
            port: providerConfig.smtp.port,
            useTls: providerConfig.smtp.secure,
          };
        }

        // Using provider config for domain
      } else {
        // Fall back to generic defaults
        incoming = {
          protocol: 'imap' as const,
          host: `imap.${domain}`,
          port: 993,
          useTls: true,
        };

        outgoing = {
          protocol: 'smtp' as const,
          host: `smtp.${domain}`,
          port: 587,
          useTls: true,
        };

        // Using generic defaults for domain
      }

      return {
        displayName: parsed.email.split('@')[0],
        email: parsed.email,
        password: parsed.password,
        incoming,
        outgoing,
        useProxy: false,
      };
    });
  }

  /**
   * Finds provider configuration from built-in providers list
   */
  private static findProviderConfig(domain: string): DiscoveredConfig | null {
    const provider = imapProviders.find(p => p.domains.includes(domain));
    if (provider) {
      // Found built-in config for domain
      return provider.config;
    }
    return null;
  }

  /**
   * Discovers and caches server configuration for a domain
   * First checks built-in providers, then falls back to DNS discovery
   */
  private static async discoverDomainConfig(
    domain: string,
    getEmailConfig: (email: string) => Promise<DiscoveredConfig | null>
  ): Promise<DiscoveredConfig | null> {
    if (this.domainConfigCache.has(domain)) {
      return this.domainConfigCache.get(domain) ?? null;
    }

    // First, check built-in providers
    const providerConfig = this.findProviderConfig(domain);
    if (providerConfig) {
      this.domainConfigCache.set(domain, providerConfig);
      return providerConfig;
    }

    // Fall back to DNS discovery for unknown providers
    try {
      // Running DNS discovery for domain
      const config = await getEmailConfig(`test@${domain}`);
      if (config !== null) {
        this.domainConfigCache.set(domain, config);
      }
      return config;
    } catch {
      // Failed to discover config for domain
      return null;
    }
  }

  /**
   * Updates accounts with discovered server configurations in background
   */
  private static async updateAccountsWithDiscoveredConfigs(
    accounts: Account[],
    getEmailConfig: (email: string) => Promise<DiscoveredConfig | null>
  ): Promise<void> {
    // Group accounts by domain to avoid duplicate discoveries
    const domainGroups = new Map<string, Account[]>();
    
    for (const account of accounts) {
      const domain = account.email.split('@')[1];
      if (!domainGroups.has(domain)) {
        domainGroups.set(domain, []);
      }
      const domainAccountsList = domainGroups.get(domain);
      if (domainAccountsList) {
        domainAccountsList.push(account);
      }
    }

    // Process each domain group
    for (const [domain, domainAccounts] of domainGroups) {
      try {
        // Processing domain accounts

        // Skip domains that already have provider configs (they're already correct)
        const hasProviderConfig = this.findProviderConfig(domain) !== null;
        if (hasProviderConfig) {
          // Skipping domain - already has provider config
          continue;
        }

        const config = await this.discoverDomainConfig(domain, getEmailConfig);

        if (config) {
          // Found config for domain
          // Update all accounts for this domain
          for (const account of domainAccounts) {
            const updatedAccount: Partial<Account> = {};

            if (config.imap) {
              updatedAccount.incoming = {
                protocol: 'imap',
                host: config.imap.host,
                port: config.imap.port,
                useTls: config.imap.secure,
              };
            }

            if (config.smtp) {
              updatedAccount.outgoing = {
                protocol: 'smtp',
                host: config.smtp.host,
                port: config.smtp.port,
                useTls: config.smtp.secure,
              };
            }

            // Updating account with discovered config
            // Update account in store
            updateAccount(account.id, updatedAccount);
          }
        } else {
          // No config found for domain
        }
      } catch {
        // Failed to update accounts for domain
      }

      // Add small delay between domain discoveries to avoid overwhelming servers
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  /**
   * Instantly imports accounts from file content and starts background DNS discovery
   */
  static importFromContent(
    content: string,
    getEmailConfig: (email: string) => Promise<DiscoveredConfig | null>
  ): InstantImportResult {
    try {
      // Parse content
      const lines = content.split(/\r?\n/).filter(line => line.trim().length > 0);

      if (lines.length === 0) {
        return {
          success: false,
          addedCount: 0,
          skippedCount: 0,
          totalCount: 0,
          error: 'Content is empty',
        };
      }

      return this.processLines(lines, getEmailConfig);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Import failed';
      return {
        success: false,
        addedCount: 0,
        skippedCount: 0,
        totalCount: 0,
        error: errorMessage,
      };
    }
  }

  /**
   * Instantly imports accounts and starts background DNS discovery
   */
  static async importFromFile(
    filePath: string,
    getEmailConfig: (email: string) => Promise<DiscoveredConfig | null>
  ): Promise<InstantImportResult> {
    try {
      // Read and parse file
      const content = await fs.promises.readFile(filePath, 'utf8');
      return this.importFromContent(content, getEmailConfig);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Import failed';
      return {
        success: false,
        addedCount: 0,
        skippedCount: 0,
        totalCount: 0,
        error: errorMessage,
      };
    }
  }

  /**
   * Processes lines of account data
   */
  private static processLines(
    lines: string[],
    getEmailConfig: (email: string) => Promise<DiscoveredConfig | null>
  ): InstantImportResult {
    // Detect separator and parse accounts
    const separator = this.detectSeparator(lines);
    const parsedAccounts: ParsedAccount[] = [];
    let skippedCount = 0;

    for (const line of lines) {
      const parsed = this.normalizeLine(line, separator);
      if (parsed) {
        parsedAccounts.push(parsed);
      } else {
        skippedCount++;
      }
    }

    if (parsedAccounts.length === 0) {
      return {
        success: false,
        addedCount: 0,
        skippedCount,
        totalCount: lines.length,
        error: 'No valid accounts found',
      };
    }

    // Create basic accounts with default settings
    const basicAccounts = this.createBasicAccounts(parsedAccounts);

    // Add accounts to store immediately
    const addedAccounts = addAccounts(basicAccounts);

    // Start background DNS discovery (don't await)
    this.updateAccountsWithDiscoveredConfigs(addedAccounts, getEmailConfig).catch(() => {
      // Background DNS discovery failed
    });

    return {
      success: true,
      addedCount: addedAccounts.length,
      skippedCount,
      totalCount: lines.length,
    };
  }

  /**
   * Clears the domain configuration cache
   */
  static clearDomainCache(): void {
    this.domainConfigCache.clear();
  }

  /**
   * Gets cached domain configuration
   */
  static getDomainConfig(domain: string): DiscoveredConfig | null {
    return this.domainConfigCache.get(domain) ?? null;
  }
}


---

// FILE: src\services\storeService.ts

/**
 * @file Service for managing storage using simple text files.
 * This stores everything in the application's root directory for portability.
 */
import fs from 'fs';
import path from 'path';

import { app } from 'electron';
import { v5 as uuidv5 } from 'uuid';

import { imapProviders } from '../shared/store/imapProviders';
import type { Account, ProxyConfig, GlobalProxyConfig } from '../shared/types/account';

import type { DiscoveredConfig } from './autoDiscoveryService'; // Import the type for configs

interface ProviderConfig {
  imap: {
    host: string;
    port: number;
    secure: boolean;
  };
  smtp?: {
    host: string;
    port: number;
    secure: boolean;
  };
}


// A constant namespace for generating deterministic UUIDs from email addresses.
// This ensures that the ID for a given email is always the same.
const ACCOUNT_ID_NAMESPACE = 'fd5a1e70-03e3-4d40-b8b0-3f7b9d10c0f2';

// Define file paths relative to the application root
const getBasePath = (): string => {
  // In development, use the project root
  // In production, use the directory where the executable is located
  return app.isPackaged ? path.dirname(app.getPath('exe')) : app.getAppPath();
};

const DATA_DIR = path.join(getBasePath(), 'data');
const ACCOUNTS_FILE = path.join(DATA_DIR, 'accounts.txt');
const DOMAINS_FILE = path.join(DATA_DIR, 'domains.txt');
const CONFIG_FILE = path.join(DATA_DIR, 'config.json');
const PROXIES_FILE = path.join(DATA_DIR, 'proxies.txt');

// Store the current index for proxy rotation
let currentProxyIndex = 0;

// Track if infrastructure has been initialized to avoid repeated calls
let infrastructureInitialized = false;

// Ensure files and directory exist
const ensureDataInfrastructure = (): void => {
  if (infrastructureInitialized) {
    return; // Already initialized, skip
  }

  // 1. Create data directory if it doesn't exist
  if (!fs.existsSync(DATA_DIR)) {
    try {
      fs.mkdirSync(DATA_DIR, { recursive: true });
      console.log('Created data directory:', DATA_DIR);
    } catch (error) {
      console.error('Failed to create data directory:', error);
      return; // Stop if we can't create the essential directory
    }
  }

  // 2. Ensure all config/data files exist inside the data directory
  const files = [ACCOUNTS_FILE, DOMAINS_FILE, CONFIG_FILE, PROXIES_FILE];
  files.forEach(file => {
    if (!fs.existsSync(file)) {
      try {
        if (file.endsWith('.json')) {
          fs.writeFileSync(file, '{}', 'utf-8');
        } else {
          fs.writeFileSync(file, '', 'utf-8');
        }
        console.log('Created file:', file);
      } catch (error) {
        console.error(`Failed to create file ${file}:`, error);
      }
    }
  });

  infrastructureInitialized = true;
};

// Initialize files on startup
ensureDataInfrastructure();

/**
 * Finds provider configuration from built-in providers list
 */
const findProviderConfig = (domain: string): unknown | null => {
  const provider = imapProviders.find(p => p.domains.includes(domain));
  return provider ? provider.config : null;
};

// Account management functions
export const getAccounts = (): Account[] => {
  try {
    const content = fs.readFileSync(ACCOUNTS_FILE, 'utf8');
    const savedDomains = getDomains(); // Get all saved domain configs at once
    const accounts: Account[] = [];
    const seenEmails = new Set<string>(); // Track seen emails to prevent duplicates

    content.split('\n').forEach(line => {
      line = line.trim();
      if (!line) return;

      const [email, password] = line.split(':');
      if (email && password) {
        // Skip if we've already seen this email
        if (seenEmails.has(email)) {
          // eslint-disable-next-line no-console
          console.warn(`Duplicate email found in accounts.txt: ${email} - skipping`);
          return;
        }
        seenEmails.add(email);
        const domain = email.split('@')[1];

        // Default values - use placeholder values that indicate manual configuration needed
        let incoming: Account['incoming'] = { host: 'imap.example.com', port: 993, useTls: true, protocol: 'imap' };
        let outgoing: Account['outgoing'] | undefined = undefined;

        // First, check saved domain configs (user customizations have priority)
        if ((domain?.length ?? 0) > 0 && savedDomains[domain] !== null && savedDomains[domain] !== undefined) {
          const config = savedDomains[domain];
          console.log(`Using saved domain config for ${domain}:`, config);
          if (config.imap !== null && config.imap !== undefined) {
            incoming = {
              protocol: 'imap',
              host: config.imap.host,
              port: config.imap.port,
              useTls: config.imap.secure,
            };
            console.log(`Set IMAP host to ${config.imap.host} for ${email}`);
          }
          if (config.smtp) {
            outgoing = {
              protocol: 'smtp',
              host: config.smtp.host,
              port: config.smtp.port,
              useTls: config.smtp.secure,
            };
          }
        }
        // If no saved domain config, check built-in providers
        else {
          const providerConfig = findProviderConfig(domain);
          if (providerConfig !== null && providerConfig !== undefined) {
            const config = providerConfig as ProviderConfig;
            incoming = {
              protocol: 'imap',
              host: config.imap.host,
              port: config.imap.port,
              useTls: config.imap.secure,
            };

            if (config.smtp !== null && config.smtp !== undefined) {
              outgoing = {
                protocol: 'smtp',
                host: config.smtp.host,
                port: config.smtp.port,
                useTls: config.smtp.secure,
              };
            }
          }
        }
        
        accounts.push({
          id: uuidv5(email, ACCOUNT_ID_NAMESPACE),
          email,
          password,
          connectionStatus: 'disconnected',
          incoming, // Use the potentially updated values
          outgoing, // Use the potentially updated values
        });
      }
    });
    
    return accounts;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error reading accounts file:', error);
    return [];
  }
};

/**
 * Saves an array of Account objects to the store.
 * @param accounts The array of Account objects to save.
 */
export const setAccounts = (accounts: Account[]): void => {
  try {
    // Convert accounts to email:password format
    const content = accounts
      .map(a => `${a.email}:${a.password}`)
      .join('\n');
    
    fs.writeFileSync(ACCOUNTS_FILE, content, 'utf8');
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error saving accounts:', error);
  }
};

/**
 * Adds a new account to the store.
 * @param accountData The account data to add (id will be generated).
 * @returns The newly created Account object.
 */
export const addAccount = (accountData: Omit<Account, 'id'>): Account => {
  console.log('addAccount called with:', accountData);
  const accounts = getAccounts();
  console.log('Current accounts:', accounts);

  // Check if account with this email already exists
  const existingAccount = accounts.find(acc => acc.email === accountData.email);
  if (existingAccount) {
    // eslint-disable-next-line no-console
    console.warn(`Account with email ${accountData.email} already exists`);
    throw new Error(`Account with email ${accountData.email} already exists`);
  }

  // Construct a full Account object with a new ID
  const newAccount = {
    id: uuidv5(accountData.email, ACCOUNT_ID_NAMESPACE),
    ...accountData,
  };
  console.log('New account created:', newAccount);

  accounts.push(newAccount);
  console.log('Saving accounts to store:', accounts);
  setAccounts(accounts);
  console.log('Account saved successfully, returning:', newAccount);
  return newAccount;
};

/**
 * Updates a single account in the store.
 * @param accountId The ID of the account to update.
 * @param updates The partial account data to apply.
 * @returns The updated Account object or null if not found.
 */
export const updateAccount = (accountId: string, updates: Partial<Omit<Account, 'id'>>): Account | null => {
  console.log('updateAccount called with accountId:', accountId);
  console.log('updateAccount updates:', JSON.stringify(updates, null, 2));

  const accounts = getAccounts();
  const accountIndex = accounts.findIndex(acc => acc.id === accountId);

  if (accountIndex === -1) {
    return null;
  }

  const updatedAccountData = {
    ...accounts[accountIndex],
    ...updates,
  };

  console.log('updatedAccountData before saving:', JSON.stringify(updatedAccountData, null, 2));

  accounts[accountIndex] = updatedAccountData;
  setAccounts(accounts);

  // If IMAP settings were updated, sync domain configuration
  if (updates.incoming && updatedAccountData.email) {
    const domain = updatedAccountData.email.split('@')[1];
    if (domain && !domain.includes('example.com')) {
      const config: DiscoveredConfig = {};

      if (updatedAccountData.incoming) {
        config.imap = {
          host: updatedAccountData.incoming.host,
          port: updatedAccountData.incoming.port,
          secure: updatedAccountData.incoming.useTls,
        };
      }

      if (updatedAccountData.outgoing) {
        config.smtp = {
          host: updatedAccountData.outgoing.host,
          port: updatedAccountData.outgoing.port,
          secure: updatedAccountData.outgoing.useTls,
        };
      }

      console.log(`Saving domain config for ${domain}:`, config);
      saveDomain(domain, config);
      console.log(`Domain config saved for ${domain}`);
    }
  }

  return updatedAccountData;
};

/**
 * Removes an account from the store by its ID.
 * @param accountId The ID of the account to remove.
 */
export const removeAccount = (accountId: string): void => {
  const accounts = getAccounts();
  const filteredAccounts = accounts.filter(a => a.id !== accountId);
  setAccounts(filteredAccounts);
};

/**
 * Adds multiple new accounts to the store in a single, optimized operation.
 * @param accountsData An array of account data to add.
 * @returns An array of the newly created Account objects.
 */
export const addAccounts = (accountsData: Omit<Account, 'id' | 'connectionStatus'>[]): Account[] => {
  const existingAccounts = getAccounts();
  const existingEmails = new Set(existingAccounts.map(a => a.email));

  const newAccounts = accountsData
    .filter(data => !existingEmails.has(data.email)) // Prevent adding duplicates
    .map(data => {
      const newAccount: Account = {
        id: uuidv5(data.email, ACCOUNT_ID_NAMESPACE),
        ...data,
        connectionStatus: 'disconnected' as const,
      };
      return newAccount;
    });

  if (newAccounts.length > 0) {
    const allAccounts = [...existingAccounts, ...newAccounts];
    // Directly save the combined list of accounts
    setAccounts(allAccounts);
  }
  
  return newAccounts;
};

/**
 * Gets the list of proxies from the proxies.txt file.
 * @returns An array of proxy configurations.
 */
export const getProxyList = (): ProxyConfig[] => {
  try {
    const content = fs.readFileSync(PROXIES_FILE, 'utf8');
    const proxies: ProxyConfig[] = [];
    
    content.split('\n').forEach(line => {
      line = line.trim();
      if (!line || line.startsWith('#')) return; // Skip empty lines and comments
      
      // Format: type:host:port:username:password
      // Example: socks5:***********:1080:user:pass
      // Example: socks5:***********:1080:: (no auth)
      const parts = line.split(':');
      if (parts.length < 3) return; // Need at least type, host, port
      
      const [type, host, port, username, password] = parts;
      const hasAuth = !!(username && password);
      
      proxies.push({
        enabled: true,
        type: type as 'socks5' | 'socks4' | 'https',
        host,
        port: parseInt(port, 10),
        hostPort: `${host}:${port}`,
        auth: hasAuth,
        username: username || undefined,
        password: password || undefined,
      });
    });
    
    return proxies;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error reading proxy list:', error);
    return [];
  }
};

/**
 * Saves a list of proxies to the proxies.txt file.
 * @param proxies The array of proxy configurations to save.
 */
export const saveProxyList = (proxies: ProxyConfig[]): void => {
  try {
    const content = proxies.map(proxy => {
      const type = proxy.type;
      const host = (proxy.hostPort?.length ?? 0) > 0 ? (proxy.hostPort ?? '').split(':')[0] : proxy.host;
      const port = (proxy.hostPort?.length ?? 0) > 0 ? (proxy.hostPort ?? '').split(':')[1] : proxy.port.toString();
      const username = proxy.auth === true ? proxy.username ?? '' : '';
      const password = proxy.auth === true ? proxy.password ?? '' : '';

      return `${type}:${host}:${port}:${username}:${password}`;
    }).join('\n');
    
    fs.writeFileSync(PROXIES_FILE, content, 'utf8');
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error saving proxy list:', error);
  }
};

/**
 * Gets the next proxy from the rotation list.
 * @returns The next proxy configuration or null if no proxies are available.
 */
export const getNextProxy = (): ProxyConfig | null => {
  const proxies = getProxyList().filter(p => p.enabled);
  if (proxies.length === 0) {
    return null;
  }
  
  // Reset index if it's out of bounds
  if (currentProxyIndex >= proxies.length) {
    currentProxyIndex = 0;
  }
  
  // Get the proxy and increment the index
  const proxy = proxies[currentProxyIndex];
  currentProxyIndex = (currentProxyIndex + 1) % proxies.length;
  
  return proxy;
};

/**
 * Tests if a proxy is working by attempting to connect to a test server.
 * @param proxy The proxy configuration to test.
 * @returns A promise that resolves to true if the proxy is working, false otherwise.
 */
export const testProxy = async (_proxy: ProxyConfig): Promise<boolean> => {
  try {
    // This is a placeholder. In a real implementation, you would:
    // 1. Create a socket connection through the proxy
    // 2. Try to connect to a known server (like google.com)
    // 3. Return true if successful, false otherwise
    
    // For now, we'll just simulate a test with a random result
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(Math.random() > 0.2); // 80% chance of success for demo
      }, 500);
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error testing proxy:', error);
    return false;
  }
};

// Domain management functions
export const getDomains = (): Record<string, DiscoveredConfig> => {
  try {
    const content = fs.readFileSync(DOMAINS_FILE, 'utf8');
    const domains: Record<string, DiscoveredConfig> = {};
    
    content.split('\n').forEach(line => {
      line = line.trim();
      if (!line) return;
      
      const separatorIndex = line.indexOf(':');
      if (separatorIndex === -1) return;

      const domain = line.substring(0, separatorIndex);
      const configString = line.substring(separatorIndex + 1);
      
      if (domain && configString) {
        const [imapStr, smtpStr] = configString.split('|');
        const config: DiscoveredConfig = {};

        if (imapStr) {
          const [host, port, secure] = imapStr.split(':');
          if (host && port && secure) {
            config.imap = { host, port: parseInt(port, 10), secure: secure === 'true' };
          }
        }
        if (smtpStr) {
          const [host, port, secure] = smtpStr.split(':');
          if (host && port && secure) {
            config.smtp = { host, port: parseInt(port, 10), secure: secure === 'true' };
          }
        }
        domains[domain] = config;
      }
    });
    
    return domains;
  } catch (error) {
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
        return {};
    }
    // eslint-disable-next-line no-console
    console.error('Error reading domains file:', error);
    return {};
  }
};

/**
 * Removes a domain from the cache
 * @param domain The domain name to remove
 */
export const removeDomain = (domain: string): void => {
  try {
    const domains = getDomains();
    delete domains[domain];

    const content = Object.entries(domains)
      .map(([d, c]) => {
        let line = '';
        if (c.imap) {
          line += `${c.imap.host}:${c.imap.port}:${c.imap.secure}`;
        }
        if (c.smtp) {
          line += `|${c.smtp.host}:${c.smtp.port}:${c.smtp.secure}`;
        }
        return `${d}:${line}`;
      })
      .join('\n');

    fs.writeFileSync(DOMAINS_FILE, content, 'utf8');
    console.log(`Removed domain from cache: ${domain}`);
  } catch (error) {
    console.error('Error removing domain:', error);
  }
};

/**
 * Saves a domain and its configuration to the store.
 * @param domain The domain name (e.g., 'gmail.com').
 * @param config The configuration object from auto-discovery.
 */
export const saveDomain = (domain: string, config: DiscoveredConfig): void => {
  try {
    // Don't save example domains or invalid configurations
    if (!domain || domain.includes('example.com') || domain.includes('example.org')) {
      console.log('Skipping save for example domain:', domain);
      return;
    }

    // Don't save configurations with example hosts
    if (config.imap?.host?.includes('example.com') ||
        config.smtp?.host?.includes('example.com') ||
        config.pop3?.host?.includes('example.com')) {
      console.log('Skipping save for configuration with example hosts:', config);
      return;
    }

    const domains = getDomains();

    // Create the simple string format
    // Configuration will be saved as JSON object

    // This will update the existing domain or add a new one
    const newDomains = { ...domains, [domain]: config };
    
    const content = Object.entries(newDomains)
      .map(([d, c]) => {
        let line = '';
        if (c.imap) {
          line += `${c.imap.host}:${c.imap.port}:${c.imap.secure}`;
        }
        if (c.smtp) {
          line += `|${c.smtp.host}:${c.smtp.port}:${c.smtp.secure}`;
        }
        return `${d}:${line}`;
      })
      .join('\n');
    
    fs.writeFileSync(DOMAINS_FILE, content, 'utf8');
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error saving domain:', error);
  }
};

// Config management functions
export const getConfig = (): Record<string, unknown> => {
  try {
    const content = fs.readFileSync(CONFIG_FILE, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error reading config file:', error);
    return {};
  }
};

export const saveConfig = (config: Record<string, unknown>): void => {
  try {
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error saving config:', error);
  }
};

// Proxy configuration functions
export const getGlobalProxy = (): GlobalProxyConfig | null => {
  const config = getConfig();
  return (config.proxy as GlobalProxyConfig) ?? null;
};

export const setGlobalProxy = (proxy: GlobalProxyConfig | null): void => {
  const config = getConfig();
  config.proxy = proxy;
  saveConfig(config);
};

---

// FILE: src\services\utils\emailProcessing.ts

/**
 * @file Email processing utilities
 */

import type { EmailHeader } from '../../shared/types/email';

/**
 * Validates that the IMAP mailbox is properly opened
 */
export function validateMailbox(imap: { mailbox: unknown }): void {
  if (imap.mailbox === null || imap.mailbox === undefined || typeof imap.mailbox === 'boolean') {
    throw new Error('Mailbox is not properly opened');
  }
}

/**
 * Calculates message range for fetching emails
 */
export function calculateMessageRange(totalMessages: number, offset: number, limit: number): { start: number; end: number } {
  if (totalMessages === 0 || offset >= totalMessages) {
    return { start: 1, end: 0 }; // Invalid range
  }
  
  const start = Math.max(1, totalMessages - offset - limit + 1);
  const end = totalMessages - offset;
  
  return { start, end };
}

/**
 * Processes email envelope to extract sender information
 */
export function extractSenderInfo(fromAddress: { name?: string; address?: string } | null | undefined): string {
  if (fromAddress === null || fromAddress === undefined) {
    return 'Unknown Sender';
  }
  
  if ((fromAddress.name?.length ?? 0) > 0) {
    return `${fromAddress.name} <${fromAddress.address}>`;
  } else {
    return fromAddress.address ?? 'Unknown Sender';
  }
}

/**
 * Creates an EmailHeader object from IMAP message data
 */
export function createEmailHeader(message: {
  uid: number;
  envelope: {
    from?: Array<{ name?: string; address?: string }>;
    subject?: string;
    date?: Date;
  };
  flags?: Set<string>;
}): EmailHeader {
  const fromAddress = message.envelope.from?.[0];
  const fromText = extractSenderInfo(fromAddress);
  
  return {
    uid: message.uid,
    subject: message.envelope.subject ?? 'No Subject',
    from: { text: fromText },
    date: message.envelope.date?.toISOString() ?? new Date().toISOString(),
    seen: message.flags?.has('\\Seen') ?? false,
  };
}


---

// FILE: src\services\utils\imapErrorHandling.ts

/**
 * @file IMAP error handling utilities
 */

import { getGlobalProxy, getNextProxy } from '../storeService';

/**
 * Creates user-friendly error messages from IMAP errors
 */
export function createUserFriendlyErrorMessage(err: Error, _accountEmail: string, hostName: string): string {
  if ((err.message?.toLowerCase().includes('authentication')) === true) {
    return 'Authentication failed. Please check your email and password. If you use 2-Factor Authentication, you may need to generate an App Password.';
  } else if ((err.message?.toLowerCase().includes('timeout')) === true) {
    return `Connection to ${hostName} timed out. Please check your network and server address.`;
  } else {
    return `A connection error occurred: ${err.message}`;
  }
}

/**
 * Logs IMAP connection errors with user-friendly messages
 */
export function logImapError(err: Error, accountEmail: string, hostName: string, logCallback: (message: string, level?: 'info' | 'error' | 'success') => void): void {
  // eslint-disable-next-line no-console
  console.error(`IMAP connection error for ${accountEmail}:`, err);

  const userFriendlyMessage = createUserFriendlyErrorMessage(err, accountEmail, hostName);
  logCallback(userFriendlyMessage, 'error');
}

interface AccountConfig {
  incoming: {
    host: string;
    port: number;
    useTls: boolean;
  };
  email: string;
  password: string;
  useProxy?: boolean;
  displayName?: string;
}

interface ImapConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  proxy?: string;
  logger: false;
  connTimeout: number;
  authTimeout: number;
}

/**
 * Creates IMAP connection configuration
 */
export function createImapConfig(account: AccountConfig, proxy?: string): ImapConfig {
  return {
    host: account.incoming.host,
    port: account.incoming.port,
    secure: account.incoming.useTls,
    auth: {
      user: account.email,
      pass: account.password,
    },
    proxy,
    logger: false,
    connTimeout: 30000,
    authTimeout: 30000,
  };
}

/**
 * Configures proxy for IMAP connection
 */
export function configureProxy(account: AccountConfig, logCallback: (message: string, level?: 'info' | 'error' | 'success') => void): { proxy: string | undefined; proxyUsed: boolean } {
  let proxy: string | undefined;
  let proxyUsed = false;

  if (account.useProxy === true) {
    // Try to get a proxy from the rotation list first
    const nextProxy = getNextProxy();

    // If there's no proxy in the rotation list, fall back to global proxy
    const proxyConfig = nextProxy ?? getGlobalProxy();

    if (proxyConfig && proxyConfig.enabled === true && (proxyConfig.hostPort?.length ?? 0) > 0) {
      logCallback(`Connecting account '${(account.displayName?.length ?? 0) > 0 ? account.displayName : account.email}' via proxy ${proxyConfig.hostPort}`, 'info');

      const authPart = (proxyConfig.auth === true && (proxyConfig.username?.length ?? 0) > 0) ?
        `${encodeURIComponent(proxyConfig.username ?? '')}:${encodeURIComponent(proxyConfig.password ?? '')}@` : '';
      proxy = `${proxyConfig.type}://${authPart}${proxyConfig.hostPort}`;
      proxyUsed = true;
    } else {
      logCallback(`Proxy is enabled for '${(account.displayName?.length ?? 0) > 0 ? account.displayName : account.email}', but no proxy is available. Connecting directly.`, 'info');
    }
  }

  return { proxy, proxyUsed };
}


---

// FILE: src\shared\hooks\useAccountForm.ts

/**
 * @file Hook for managing account form state and logic with improved validation.
 */
import { zodResolver } from '@hookform/resolvers/zod';
import { useState, useEffect, useCallback, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { ClipboardService } from '../../services/clipboardService';
import { accountSchema, type Account } from '../types/account';
import type { DiscoveredConfig } from '../types/protocol';

import { useEmailDiscovery } from './useEmailDiscovery';

// New form schema with separate host and port for better validation and UX
const formSchema = accountSchema.omit({ id: true, incoming: true, outgoing: true, connectionStatus: true, useProxy: true, proxy: true }).extend({
  incoming: z.object({
    protocol: z.enum(['imap', 'pop3']),
    host: z.string().min(1, 'Host cannot be empty'),
    port: z.number().min(1, 'Port cannot be empty'),
    useTls: z.boolean(),
  }),
  outgoing: z.object({
    protocol: z.literal('smtp'),
    host: z.string().min(1, 'Host cannot be empty'),
    port: z.number().min(1, 'Port cannot be empty'),
    useTls: z.boolean(),
  }).optional(),
});

export type AccountFormType = z.infer<typeof formSchema>;

interface UseAccountFormProps {
  accountToEdit?: Account | null;
  initialData?: { email: string; password: string } | null;
  onSave?: (data: Omit<Account, 'id'>) => Promise<void>;
}
interface UseAccountFormReturn {
    form: ReturnType<typeof useForm<AccountFormType>>;
    isPasswordVisible: boolean;
    setIsPasswordVisible: (visible: boolean) => void;
    error: string | null;
    setError: (error: string | null) => void;
    showProviderSuggestions: boolean;
    setShowProviderSuggestions: (show: boolean) => void;
    discovery: ReturnType<typeof useEmailDiscovery>;
    handleProviderSelect: (config: DiscoveredConfig) => void;
    handleManualDiscovery: () => Promise<void>;
    handleEmailBlur: (e: React.FocusEvent<HTMLInputElement>) => Promise<void>;
    handleSubmit: (e?: React.BaseSyntheticEvent) => Promise<void>;
    parseCredentialsString: (text: string) => Promise<boolean>;
}


export const useAccountForm = (props: UseAccountFormProps): UseAccountFormReturn => {
  const { accountToEdit, initialData, onSave } = props;
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showProviderSuggestions, setShowProviderSuggestions] = useState(false);
  const isDiscoveringRef = useRef(false);

  const discovery = useEmailDiscovery();
  const { discoverEmailSettings } = discovery;

  const form = useForm<AccountFormType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      displayName: '',
      email: '',
      password: '',
      incoming: {
        protocol: 'imap',
        host: 'imap.example.com',
        port: 993,
        useTls: true,
      },
    },
  });

  const { reset, setValue, trigger, handleSubmit: formHandleSubmit, getValues } = form;

  useEffect(() => {
    if (accountToEdit && !isDiscoveringRef.current) {
      console.log('Resetting form with accountToEdit:', accountToEdit);
      reset(accountToEdit);
      return;
    }
    if (initialData) {
      setValue('email', initialData.email, { shouldValidate: true });
      setValue('password', initialData.password, { shouldValidate: true });
      setTimeout(() => {
        void discoverEmailSettings(initialData.email, false, setValue);
      }, 500);
      return;
    }
    reset({
        displayName: '',
        email: '',
        password: '',
        incoming: { protocol: 'imap', host: '', port: 993, useTls: true },
    });
  }, [accountToEdit, initialData, reset, setValue]);

  const handleProviderSelect = useCallback((config: DiscoveredConfig) => {
    discovery.applyDiscoveredConfig(config, setValue);
    setShowProviderSuggestions(false);
  }, [discovery, setValue]);

  const handleManualDiscovery = useCallback(async () => {
    console.log('handleManualDiscovery called');
    const email = getValues('email');
    console.log(`Email from form: ${email}`);
    if (!email || !/^\S+@\S+\.\S+$/.test(email)) {
      setError('Please enter a valid email to run discovery.');
      return;
    }
    setError(null);
    isDiscoveringRef.current = true;
    console.log(`Forcing manual discovery for ${email}... (bypassing all caches)`);
    try {
      // The `true` parameter is crucial to force a new DNS lookup,
      // ignoring any cached results from previous discoveries.
      await discovery.discoverEmailSettings(email, true, setValue);

      // After discovery, the form's internal state is updated via `setValue`.
      // No need to reset - setValue should have already updated the form state
      console.log('Discovery completed, form should be updated via setValue');
    } finally {
      isDiscoveringRef.current = false;
    }
  }, [discovery, getValues, setValue, setError]);

  const parseCredentialsString = useCallback(async (text: string): Promise<boolean> => {
    const result = ClipboardService.parseCredentialsString(text);
    if (result.success && result.credentials) {
      setValue('email', result.credentials.email, { shouldValidate: true });
      setValue('password', result.credentials.password, { shouldValidate: true });
      // Run discovery when credentials are detected from clipboard (only for new accounts)
      if (!accountToEdit && /^\S+@\S+\.\S+$/.test(result.credentials.email)) {
        console.log('Running discovery for credentials from clipboard:', result.credentials.email);
        await discovery.discoverEmailSettings(result.credentials.email, false, setValue);
      }
      return true;
    }
    return false;
  }, [setValue, discovery, accountToEdit]);

  // No automatic discovery - only manual discovery via button or clipboard detection

  const handleEmailBlur = useCallback(async (e: React.FocusEvent<HTMLInputElement>) => {
    const email = e.target.value.trim();
    // Only parse credentials from clipboard, no automatic discovery
    await parseCredentialsString(email);
  }, [parseCredentialsString]);

  const handleSubmit = useCallback(async (e?: React.BaseSyntheticEvent) => {
    if (e) e.preventDefault();

    console.log('handleSubmit called');

    return formHandleSubmit(async (data: AccountFormType) => {
      console.log('Form data:', data);
      console.log('Incoming data:', JSON.stringify(data.incoming, null, 2));
      setError(null);

      // Only run discovery for new accounts with empty/example hosts
      // Don't run discovery when editing existing accounts - respect user's manual changes
      const isNewAccount = !accountToEdit;
      const needsDiscovery = isNewAccount && (
        !data.incoming.host ||
        data.incoming.host.includes('example.com') ||
        data.incoming.host === 'imap.example.com' ||
        data.incoming.host === ''
      );

      if (needsDiscovery && /^\S+@\S+\.\S+$/.test(data.email)) {
        console.log('Running discovery for new account...');
        try {
          await discovery.discoverEmailSettings(data.email, true, setValue);

          // Use updated data if discovery was successful
          if (discovery.discoveryStatus === 'found') {
            // Trigger validation to update the form's internal state and UI
            await trigger();
            const updatedData = getValues();
            console.log('Updated data after discovery:', updatedData);
            Object.assign(data, updatedData);
          }
        } catch (discoveryError) {
          console.warn('Discovery failed, proceeding with manual settings:', discoveryError);
        }
      } else if (accountToEdit) {
        console.log('Editing existing account - using manual settings without discovery');
      }

      const finalData: Omit<Account, 'id'> = {
        ...data,
        displayName: data.displayName || data.email.split('@')[0],
      };

      console.log('Final data to save:', finalData);
      console.log('Final incoming data:', JSON.stringify(finalData.incoming, null, 2));

      try {
        await onSave?.(finalData);
        console.log('Account saved successfully');
      } catch (e: unknown) {
        console.error('Failed to save account:', e);
        const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
        setError(errorMessage);
      }
    })(e);
  }, [formHandleSubmit, onSave, discovery, setValue, getValues, accountToEdit]);

  return {
    form,
    isPasswordVisible,
    setIsPasswordVisible,
    error,
    setError,
    showProviderSuggestions,
    setShowProviderSuggestions,
    discovery,
    handleProviderSelect,
    handleManualDiscovery,
    handleEmailBlur,
    handleSubmit,
    parseCredentialsString,
  };
};

---

// FILE: src\shared\hooks\useAccountManager.ts

/**
 * @file Hook for managing account operations and state
 */
import { useState, useCallback } from 'react';
import type { z } from 'zod';

import { ClipboardService } from '../../services/clipboardService';
import { useAccountStore } from '../store/accountStore';
import { useLogStore } from '../store/logStore';
import { accountSchema, type Account } from '../types/account';

// The form schema mirrors the main account schema but omits the ID
const formSchema = accountSchema.omit({ id: true });

export type AccountFormData = z.infer<typeof formSchema>;

interface DeletedAccountEntry {
  account: Account;
  deletedAt: number;
}

interface UseAccountManagerReturn {
  // State
  view: 'list' | 'form';
  setView: (_view: 'list' | 'form') => void;
  editingAccount: Account | null;
  setEditingAccount: (_account: Account | null) => void;
  error: string | null;
  setError: (_error: string | null) => void;
  prefillData: { email: string; password: string } | null;
  setPrefillData: (_data: { email: string; password: string } | null) => void;
  isImportDialogOpen: boolean;
  setIsImportDialogOpen: (_open: boolean) => void;
  deletedAccounts: DeletedAccountEntry[];
  setDeletedAccounts: (_accounts: DeletedAccountEntry[]) => void;

  // Handlers
  handleSave: (_data: AccountFormData) => Promise<void>;
  handleAddNew: () => Promise<void>;
  handleEdit: (_account: Account) => void;
  handleDelete: (_accountId: string) => Promise<void>;
  handleCancel: () => void;
  handleCopyCredentials: (_account: Account) => Promise<void>;
  handleImport: () => void;
  handleImportComplete: (_result: { addedCount: number; skippedCount: number }) => void;
  handleUndoDelete: (_accountId: string) => Promise<void>;
  handleDismissUndo: (_accountId: string) => void;
  handleClearAllUndo: () => void;
}

/**
 * Hook for managing account operations
 */
export const useAccountManager = (): UseAccountManagerReturn => {
  const {
    accounts,
    addAccountToStore,
    updateAccountInStore,
    deleteAccountInStore,
    setAccounts
  } = useAccountStore();
  
  const addLog = useLogStore((state) => state.addLog);
  
  const [view, setView] = useState<'list' | 'form'>('list');
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [prefillData, setPrefillData] = useState<{ email: string; password: string } | null>(null);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [deletedAccounts, setDeletedAccounts] = useState<DeletedAccountEntry[]>([]);

  const handleSave = useCallback(async (data: AccountFormData) => {
    console.log('handleSave called with data:', data);
    console.log('handleSave incoming data:', JSON.stringify(data.incoming, null, 2));
    setError(null);
    const dataToSave = { ...data };

    try {
      if (editingAccount) {
        console.log('Updating existing account');
        const updatedAccount = await window.ipcApi.updateAccount(editingAccount.id, dataToSave);
        addLog(`Account "${(updatedAccount.displayName?.length ?? 0) > 0 ? updatedAccount.displayName : updatedAccount.email}" updated.`, 'success');
      } else {
        console.log('Adding new account');
        if ((dataToSave.displayName?.length ?? 0) === 0) {
          const emailDomain = dataToSave.email.split('@')[1]?.split('.')[0] || 'Email';
          const newAccountName = `Account ${accounts.length + 1} ${emailDomain.toUpperCase()}`;
          dataToSave.displayName = newAccountName;
        }

        console.log('Calling window.ipcApi.addAccount with:', dataToSave);
        const newAccount = await window.ipcApi.addAccount(dataToSave);
        console.log('Account added successfully:', newAccount);
        addAccountToStore(newAccount);
        addLog(`Account "${newAccount.displayName}" added.`, 'success');
      }

      // ALWAYS reload all accounts from file to ensure store is in sync.
      const allAccounts = await window.ipcApi.getAccounts();
      setAccounts(allAccounts);

      setView('list');
      setEditingAccount(null);
    } catch (err: unknown) {
      console.error('Error in handleSave:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to save account.';
      setError(errorMessage);
      addLog(errorMessage, 'error');
    }
  }, [editingAccount, accounts.length, addAccountToStore, addLog, setAccounts]);

  const handleAddNew = useCallback(async () => {
    console.log('handleAddNew called');
    let prefill: { email: string; password: string } | null = null;

    try {
      const result = await ClipboardService.detectCredentialsFromClipboard();

      if (result.success && result.credentials) {
        prefill = result.credentials;
        addLog('Credentials detected in clipboard, pre-filling form.', 'info');
      } else if ((result.error?.includes('resembles credentials')) === true) {
        addLog(result.error, 'info');
      }
    } catch (_err) {
      // eslint-disable-next-line no-console
      console.log('Could not read clipboard content.');
      // Don't block the UI if clipboard reading fails
    }

    // Use setTimeout to prevent UI blocking
    setTimeout(() => {
      console.log('Setting view to form with prefill:', prefill);
      setPrefillData(prefill);
      setEditingAccount(null);
      setView('form');
    }, 0);
  }, [addLog]);

  const handleEdit = useCallback((account: Account) => {
    setEditingAccount(account);
    setView('form');
  }, []);

  const handleDelete = useCallback(async (accountId: string) => {
    const accountToDelete = accounts.find(acc => acc.id === accountId);
    if (!accountToDelete) return;

    const accountName = (accountToDelete.displayName?.length ?? 0) > 0 ? accountToDelete.displayName : accountToDelete.email;

    try {
      await window.ipcApi.deleteAccount(accountId);
      deleteAccountInStore(accountId);

      // Add to deleted accounts stack (max 10 accounts)
      setDeletedAccounts(prev => {
        const newEntry: DeletedAccountEntry = {
          account: accountToDelete,
          deletedAt: Date.now()
        };

        // Keep only last 10 deleted accounts
        const updated = [newEntry, ...prev].slice(0, 10);

        // Auto-cleanup entries older than 5 minutes
        return updated.filter(entry => Date.now() - entry.deletedAt < 5 * 60 * 1000);
      });

      addLog(`Account "${accountName}" deleted.`, 'success');
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete account.';
      setError(errorMessage);
      addLog(errorMessage, 'error');
    }
  }, [accounts, deleteAccountInStore, addLog]);

  const handleCancel = useCallback(() => {
    setEditingAccount(null);
    setView('list');
    setError(null);
  }, []);

  const handleCopyCredentials = useCallback(async (account: Account) => {
    try {
      const success = await ClipboardService.copyAccountCredentials(account.email, account.password);
      if (success) {
        addLog(`Credentials for "${(account.displayName?.length ?? 0) > 0 ? account.displayName : account.email}" copied to clipboard.`, 'success');
      } else {
        addLog('Failed to copy credentials to clipboard.', 'error');
      }
    } catch (_error) {
      addLog('Failed to copy credentials to clipboard.', 'error');
    }
  }, [addLog]);

  const handleImport = useCallback(() => {
    setIsImportDialogOpen(true);
  }, []);

  const handleImportComplete = useCallback((result: { addedCount: number; skippedCount: number }) => {
    addLog(`Successfully imported ${result.addedCount} accounts. ${result.skippedCount} lines were skipped.`, 'success');

    // Refresh accounts from store asynchronously
    void Promise.resolve(window.ipcApi.getAccounts()).then((updatedAccounts: Account[]) => {
      setAccounts(updatedAccounts);
    }).catch((error: unknown) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh accounts.';
      addLog(errorMessage, 'error');
    });

    // Close the dialog
    setIsImportDialogOpen(false);
  }, [addLog, setAccounts]);

  const handleUndoDelete = useCallback(async (accountId: string) => {
    const deletedEntry = deletedAccounts.find(entry => entry.account.id === accountId);
    if (!deletedEntry) return;

    try {
      const restoredAccount = await window.ipcApi.addAccount(deletedEntry.account);
      addAccountToStore(restoredAccount);

      // Remove from deleted accounts stack
      setDeletedAccounts(prev => prev.filter(entry => entry.account.id !== accountId));

      addLog(`Account "${(deletedEntry.account.displayName?.length ?? 0) > 0 ? deletedEntry.account.displayName : deletedEntry.account.email}" restored.`, 'success');
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to restore account.';
      setError(errorMessage);
      addLog(errorMessage, 'error');
    }
  }, [deletedAccounts, addAccountToStore, addLog]);

  const handleDismissUndo = useCallback((accountId: string) => {
    // Remove specific account from deleted stack (user explicitly dismissed)
    setDeletedAccounts(prev => prev.filter(entry => entry.account.id !== accountId));
  }, []);

  const handleClearAllUndo = useCallback(() => {
    // Clear all deleted accounts (e.g., on app close)
    setDeletedAccounts([]);
  }, []);

  return {
    view,
    setView,
    editingAccount,
    setEditingAccount,
    error,
    setError,
    prefillData,
    setPrefillData,
    isImportDialogOpen,
    setIsImportDialogOpen,
    deletedAccounts,
    setDeletedAccounts,
    handleSave,
    handleAddNew,
    handleEdit,
    handleDelete,
    handleCancel,
    handleCopyCredentials,
    handleImport,
    handleImportComplete,
    handleUndoDelete,
    handleDismissUndo,
    handleClearAllUndo,
  };
};


---

// FILE: src\shared\hooks\useAppInitialization.ts

/**
 * @file Hook for managing application initialization
 */
import { useState, useEffect, useCallback, useRef } from 'react';

import { useAccountStore } from '../store/accountStore';
import { useLogStore } from '../store/logStore';
import { useMainSettingsStore } from '../store/mainSettingsStore';
import { useProxyStore } from '../store/proxyStore';

interface UseAppInitializationReturn {
  // Initialization state
  isInitialized: boolean;
  initializationError: string | null;

  // Loading states
  isLoadingAccounts: boolean;

  // Actions
  retryInitialization: () => Promise<void>;
}

/**
 * Hook for managing application initialization
 */
export const useAppInitialization = (): UseAppInitializationReturn => {
  const { setAccounts, accounts } = useAccountStore();
  const { initializeProxy } = useProxyStore();
  const { settings } = useMainSettingsStore();
  const addLog = useLogStore((state) => state.addLog);

  const [isInitialized, setIsInitialized] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);
  const [isLoadingAccounts, setIsLoadingAccounts] = useState(false);

  // Prevent double initialization
  const hasInitialized = useRef(false);

  const loadAccounts = useCallback(async () => {
    setIsLoadingAccounts(true);
    try {
      const accounts = await window.ipcApi.getAccounts();
      setAccounts(accounts);
      addLog(`Loaded ${accounts.length} accounts`, 'info');
      return accounts;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to load accounts:', error);
      addLog('Failed to load accounts', 'error');
      throw new Error('Failed to load accounts');
    } finally {
      setIsLoadingAccounts(false);
    }
  }, [setAccounts, addLog]);

  const autoConnectAccounts = useCallback(async (accountsToConnect: typeof accounts) => {
    if (!settings.autoLoginOnStartup || accountsToConnect.length === 0) {
      return;
    }

    addLog(`Auto-connecting to ${accountsToConnect.length} accounts...`, 'info');

    try {
      // Start watching all accounts in the background
      const connectPromises = accountsToConnect.map(account =>
        window.ipcApi.watchInbox(account.id).catch(error => {
          addLog(`Failed to connect to ${account.email}: ${error instanceof Error ? error.message : String(error)}`, 'error');
        })
      );

      await Promise.allSettled(connectPromises);
      addLog(`Auto-connect completed for ${accountsToConnect.length} accounts`, 'success');
    } catch (error) {
      addLog(`Auto-connect failed: ${error instanceof Error ? error.message : String(error)}`, 'error');
    }
  }, [settings.autoLoginOnStartup, addLog]);

  const initializeApp = useCallback(async () => {
    setInitializationError(null);
    addLog('Initializing application...', 'info');

    try {
      const [loadedAccounts] = await Promise.all([
        loadAccounts(),
        initializeProxy(),
      ]);

      // Auto-connect to accounts if enabled (after accounts are loaded)
      await autoConnectAccounts(loadedAccounts);

      setIsInitialized(true);
      addLog('Application initialized successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';
      setInitializationError(errorMessage);
      addLog(`Application initialization failed: ${errorMessage}`, 'error');
    }
  }, [loadAccounts, initializeProxy, autoConnectAccounts, addLog]);

  const retryInitialization = useCallback(async () => {
    hasInitialized.current = false;
    setIsInitialized(false);
    await initializeApp();
  }, [initializeApp]);

  // Initialize app on mount (only once)
  useEffect(() => {
    if (hasInitialized.current) return;
    hasInitialized.current = true;
    void initializeApp();
  }, [initializeApp]);

  return {
    isInitialized,
    initializationError,
    isLoadingAccounts,
    retryInitialization,
  };
};


---

// FILE: src\shared\hooks\useEmailDiscovery.ts

/**
 * @file Hook for email server auto-discovery functionality
 */
import { useState, useCallback } from 'react';

import type { DiscoveredConfig } from '../types/protocol';

export type DiscoveryStatus = 'idle' | 'searching' | 'found' | 'failed' | 'manual';

// Use the DiscoveredConfig type directly
type EmailConfig = DiscoveredConfig;

interface SetValueFunction {
  (name: string, value: unknown, options?: { shouldValidate?: boolean }): void;
}

interface UseEmailDiscoveryReturn {
  isDiscovering: boolean;
  discoveryStatus: DiscoveryStatus;
  discoveryMessage: string;
  discoveryCache: Map<string, DiscoveredConfig | null>;
  discoveryInProgress: Set<string>;
  discoverEmailSettings: (email: string, force?: boolean, setValue?: unknown) => Promise<void>;
  applyDiscoveredConfig: (config: DiscoveredConfig, setValue: unknown) => void;
  handleManualSetup: () => void;
  handleRetryDiscovery: (email: string, setValue?: unknown) => void;
  clearDiscoveryCache: () => void;
  setDiscoveryStatus: (status: DiscoveryStatus) => void;
  setDiscoveryMessage: (message: string) => void;
}

/**
 * Hook for managing email server auto-discovery
 */
export const useEmailDiscovery = (): UseEmailDiscoveryReturn => {
  const [isDiscovering, setIsDiscovering] = useState(false);
  const [discoveryStatus, setDiscoveryStatus] = useState<DiscoveryStatus>('idle');
  const [discoveryMessage, setDiscoveryMessage] = useState<string>('');
  const [discoveryCache, setDiscoveryCache] = useState<Map<string, DiscoveredConfig | null>>(new Map());
  const [discoveryInProgress, setDiscoveryInProgress] = useState<Set<string>>(new Set());

  const applyDiscoveredConfig = useCallback((config: DiscoveredConfig, setValue: unknown) => {
    console.log('applyDiscoveredConfig called with config:', config);
    console.log('setValue function:', setValue);
    const configObj = config;
    const setValueFn = setValue as SetValueFunction;
    if (configObj.imap !== null && configObj.imap !== undefined) {
      console.log(`Setting IMAP host to: ${configObj.imap.host}`);
      setValueFn('incoming.protocol', 'imap', { shouldValidate: true });
      setValueFn('incoming.host', configObj.imap.host, { shouldValidate: true });
      setValueFn('incoming.port', configObj.imap.port, { shouldValidate: true });
      setValueFn('incoming.useTls', configObj.imap.secure, { shouldValidate: true });
    } else if (configObj.pop3 !== null && configObj.pop3 !== undefined) {
      console.log(`Setting POP3 host to: ${configObj.pop3.host}`);
      setValueFn('incoming.protocol', 'pop3', { shouldValidate: true });
      setValueFn('incoming.host', configObj.pop3.host, { shouldValidate: true });
      setValueFn('incoming.port', configObj.pop3.port, { shouldValidate: true });
      setValueFn('incoming.useTls', configObj.pop3.secure, { shouldValidate: true });
    }

    if (configObj.smtp !== null && configObj.smtp !== undefined) {
      console.log(`Setting SMTP host to: ${configObj.smtp.host}`);
      setValueFn('outgoing', {
        protocol: 'smtp',
        host: configObj.smtp.host,
        port: configObj.smtp.port,
        useTls: configObj.smtp.secure,
      }, { shouldValidate: true });
    } else {
      console.log('No SMTP config found, clearing outgoing settings');
      setValueFn('outgoing', undefined, { shouldValidate: true });
    }
  }, []);

  const discoverEmailSettings = useCallback(async (email: string, force: boolean = false, setValue?: unknown) => {
    console.log(`discoverEmailSettings called for ${email} with force=${force}`);
    const domain = email.split('@')[1];
    if (!domain) return;

    // Check cache first
    if (!force && discoveryCache.has(domain)) {
      const cachedConfig = discoveryCache.get(domain);
      if (cachedConfig !== null && cachedConfig !== undefined) {
        setDiscoveryStatus('found');
        setDiscoveryMessage(`Settings found for ${domain}`);
        // Auto-apply cached config if setValue is provided
        if (setValue !== null && setValue !== undefined) {
          applyDiscoveredConfig(cachedConfig, setValue);
        }
      } else {
        setDiscoveryStatus('failed');
        setDiscoveryMessage(`No settings found for ${domain}`);
      }
      return;
    }

    // Prevent duplicate discovery
    if (discoveryInProgress.has(domain)) {
      return;
    }

    setDiscoveryInProgress(prev => new Set(prev).add(domain));
    setIsDiscovering(true);
    setDiscoveryStatus('searching');
    setDiscoveryMessage(`Searching for email settings for ${domain}...`);

    try {
      const config = await window.ipcApi.discoverEmailConfig(domain, force);
      // Cache the result
      setDiscoveryCache(prev => new Map(prev).set(domain, config));

      if (config !== null && config !== undefined && (config.imap !== null && config.imap !== undefined || config.pop3 !== null && config.pop3 !== undefined)) {
        setDiscoveryStatus('found');
        const serverType = (config.imap !== null && config.imap !== undefined) ? 'IMAP' : 'POP3';
        const serverHost = (config.imap !== null && config.imap !== undefined) ? config.imap.host : config.pop3?.host ?? 'Unknown';
        setDiscoveryMessage(`✅ Found ${serverType} server: ${serverHost}`);
        // Auto-apply discovered config if setValue is provided
        if (setValue !== null && setValue !== undefined) {
          applyDiscoveredConfig(config, setValue);
        }
      } else {
        setDiscoveryStatus('failed');
        setDiscoveryMessage(`❌ No email servers found for ${domain}`);
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Email discovery failed:', error);
      setDiscoveryStatus('failed');
      setDiscoveryMessage(`❌ Discovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setDiscoveryCache(prev => new Map(prev).set(domain, null));
    } finally {
      setIsDiscovering(false);
      setDiscoveryInProgress(prev => {
        const newSet = new Set(prev);
        newSet.delete(domain);
        return newSet;
      });
    }
  }, [discoveryCache, discoveryInProgress, applyDiscoveredConfig]);

  const handleManualSetup = useCallback(() => {
    setDiscoveryStatus('manual');
    setDiscoveryMessage('Configure email settings manually');
  }, []);

  const handleRetryDiscovery = useCallback((email: string, setValue?: unknown) => {
    const domain = email.split('@')[1];
    if (domain) {
      // Clear cache and retry
      setDiscoveryCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(domain);
        return newCache;
      });
      void discoverEmailSettings(email, true, setValue);
    }
  }, [discoverEmailSettings]);

  const clearDiscoveryCache = useCallback(() => {
    setDiscoveryCache(new Map());
    setDiscoveryStatus('idle');
    setDiscoveryMessage('');
  }, []);

  return {
    isDiscovering,
    discoveryStatus,
    discoveryMessage,
    discoveryCache,
    discoveryInProgress,
    discoverEmailSettings,
    applyDiscoveredConfig,
    handleManualSetup,
    handleRetryDiscovery,
    clearDiscoveryCache,
    setDiscoveryStatus,
    setDiscoveryMessage,
  };
};


---

// FILE: src\shared\hooks\useEmailList.ts

/**
 * @file Hook for managing email list operations and state
 */
import { useEffect, useState, useRef, useCallback, useMemo } from 'react';

import { useAccountStore } from '../store/accountStore';
import type { EmailHeader } from '../types/email';

interface MailboxResult {
  emails?: EmailHeader[];
  totalCount?: number;
}

interface EmailFrom {
  text?: string;
}

interface UseEmailListProps {
  searchQuery?: string;
}

interface UseEmailListReturn {
  // State
  isLoading: boolean;
  isFetchingMore: boolean;
  error: string | null;
  hasLoadedOnce: boolean;
  selectedUids: number[];
  selectAll: boolean;
  isToolbarVisible: boolean;
  keyboardSelectedIndex: number;

  // Data
  emailHeaders: EmailHeader[];
  filteredEmails: EmailHeader[];
  hasMoreEmails: boolean;
  totalEmailCount: number;
  
  // Refs
  observer: React.MutableRefObject<IntersectionObserver | null>;
  lastEmailElementRef: React.MutableRefObject<HTMLDivElement | null>;
  
  // Handlers
  loadMoreEmails: () => Promise<void>;
  handleSelectEmail: (_uid: number, _event?: React.MouseEvent) => void;
  handleSelectAll: () => void;
  handleDeleteSelected: () => Promise<void>;
  setKeyboardSelectedIndex: (_index: number) => void;
  setSelectedUids: (_uids: number[] | ((_prev: number[]) => number[])) => void;
  setSelectAll: (_selectAll: boolean) => void;

  // Utils
  formatDate: (_dateString: string) => string;
  hasAttachments: (_email: EmailHeader) => boolean;
  isStarred: (_email: EmailHeader) => boolean;
}

const PAGE_SIZE = 100;

/**
 * Hook for managing email list functionality
 */
export const useEmailList = ({ searchQuery = '' }: UseEmailListProps): UseEmailListReturn => {
  const {
    selectedAccountId,
    selectedMailbox,
    emailHeadersByMailbox,
    setEmailHeadersForMailbox,
    appendEmailHeadersToMailbox,
    prependEmailHeaders,
    hasMoreEmailsByMailbox,
    setHasMoreEmailsForMailbox,
    setEmailCountForMailbox,
    removeEmailHeaders,
  } = useAccountStore();
  
  const mailboxKey = useMemo(() => {
    return (selectedAccountId?.length ?? 0) > 0 && (selectedMailbox?.length ?? 0) > 0
      ? `${selectedAccountId}-${selectedMailbox}`
      : null;
  }, [selectedAccountId, selectedMailbox]);

  const emailHeaders = useMemo(() => {
    return mailboxKey !== null && mailboxKey !== undefined && mailboxKey.length > 0 ? emailHeadersByMailbox[mailboxKey] ?? [] : [];
  }, [mailboxKey, emailHeadersByMailbox]);

  const hasMoreEmails = useMemo(() => {
    return mailboxKey !== null && mailboxKey !== undefined && mailboxKey.length > 0 ? hasMoreEmailsByMailbox[mailboxKey] === true : false;
  }, [mailboxKey, hasMoreEmailsByMailbox]);

  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  const [totalEmailCount, setTotalEmailCount] = useState(0);
  
  const observer = useRef<IntersectionObserver | null>(null);
  const lastEmailElementRef = useRef<HTMLDivElement>(null);

  const [selectedUids, setSelectedUids] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [isToolbarVisible, setIsToolbarVisible] = useState(false);
  const [keyboardSelectedIndex, setKeyboardSelectedIndex] = useState(-1);

  // Effect to handle selecting a mailbox
  useEffect(() => {
    if (selectedAccountId !== null && selectedAccountId !== undefined && selectedAccountId.length > 0 &&
        selectedMailbox !== null && selectedMailbox !== undefined && selectedMailbox.length > 0) {
      const loadMailboxContent = async (): Promise<void> => {
        // eslint-disable-next-line no-console
        console.log(`Loading emails for ${selectedAccountId} - ${selectedMailbox}`);
        setIsLoading(true);
        setError(null);
        try {
          const result = await window.ipcApi.selectMailbox(selectedAccountId, selectedMailbox, PAGE_SIZE);
          const resultObj = result as MailboxResult;
          const initialEmails = Array.isArray(result) ? result : (resultObj.emails ?? []);
          const totalCount = Array.isArray(result) ? initialEmails.length : (resultObj.totalCount ?? 0);
          setEmailHeadersForMailbox(selectedAccountId, selectedMailbox, initialEmails);
          setHasMoreEmailsForMailbox(selectedAccountId, selectedMailbox, initialEmails.length === PAGE_SIZE);

          setEmailCountForMailbox(selectedAccountId, selectedMailbox, totalCount);
          setTotalEmailCount(totalCount);
        } catch (e: unknown) {
          // eslint-disable-next-line no-console
          console.error(`Failed to load emails for ${selectedAccountId} - ${selectedMailbox}:`, e);
          setError(e instanceof Error ? e.message : "Failed to load emails");
        } finally {
          // eslint-disable-next-line no-console
          console.log(`Finished loading emails for ${selectedAccountId} - ${selectedMailbox}`);
          setIsLoading(false);
          setHasLoadedOnce(true);
        }
      };
      void loadMailboxContent();
    } else {
      setIsLoading(false);
      setError(null);
      setHasLoadedOnce(false);
    }
  }, [selectedAccountId, selectedMailbox, setEmailHeadersForMailbox, setHasMoreEmailsForMailbox, setEmailCountForMailbox]);

  // Effect to listen for new mail events
  useEffect(() => {
    const cleanup = window.ipcApi.onNewMail((_, { accountId, mailboxName, newMailCount }) => {
      if (accountId === selectedAccountId && mailboxName === selectedMailbox) {
        void (async (): Promise<void> => {
          try {
            const newEmails = await window.ipcApi.getEmails(accountId, mailboxName, 0, newMailCount);
            if (newEmails.length > 0) {
              prependEmailHeaders(accountId, mailboxName, newEmails);
            }
          } catch (e) {
            // eslint-disable-next-line no-console
            console.error("Failed to fetch new emails:", e);
          }
        })();
      }
    });

    return cleanup;
  }, [selectedAccountId, selectedMailbox, prependEmailHeaders]);

  // Filter emails based on search query
  const filteredEmails = useMemo(() => {
    if (!searchQuery) return emailHeaders;

    const query = searchQuery.toLowerCase();
    return emailHeaders.filter((email: EmailHeader) =>
      email.subject?.toLowerCase().includes(query) ||
      (typeof email.from === 'string'
        ? (email.from as string).toLowerCase().includes(query)
        : (email.from as EmailFrom)?.text?.toLowerCase()?.includes(query))
    );
  }, [emailHeaders, searchQuery]);

  // Infinite scroll loader
  const loadMoreEmails = useCallback(async () => {
    if ((selectedAccountId?.length ?? 0) === 0 || (selectedMailbox?.length ?? 0) === 0 || hasMoreEmails === false || isFetchingMore === true ||
        selectedAccountId === null || selectedAccountId === undefined || selectedMailbox === null || selectedMailbox === undefined) {
      return;
    }

    setIsFetchingMore(true);
    try {
      const currentOffset = emailHeaders.length;
      const newEmails = await window.ipcApi.getEmails(selectedAccountId, selectedMailbox, currentOffset, PAGE_SIZE);

      appendEmailHeadersToMailbox(selectedAccountId, selectedMailbox, newEmails);

      if (newEmails.length < PAGE_SIZE) {
        setHasMoreEmailsForMailbox(selectedAccountId, selectedMailbox, false);
      }
    } catch (e: unknown) {
      setError(e instanceof Error ? e.message : "Failed to load more emails");
    } finally {
      setIsFetchingMore(false);
    }
  }, [selectedAccountId, selectedMailbox, hasMoreEmails, isFetchingMore, emailHeaders.length, appendEmailHeadersToMailbox, setHasMoreEmailsForMailbox]);

  // Reset selection when changing mailbox or account
  useEffect(() => {
    setSelectedUids([]);
    setSelectAll(false);
    setIsToolbarVisible(false);
    setKeyboardSelectedIndex(-1);
    setError(null);
    setHasLoadedOnce(emailHeaders.length > 0);
  }, [selectedAccountId, selectedMailbox, emailHeaders.length]);

  // Update toolbar visibility when selection changes
  useEffect(() => {
    setIsToolbarVisible(selectedUids.length > 0);
  }, [selectedUids]);

  const handleSelectEmail = useCallback((uid: number, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }

    setSelectedUids((prev) =>
      prev.includes(uid) ? prev.filter((id) => id !== uid) : [...prev, uid]
    );
  }, []);

  const handleSelectAll = useCallback(() => {
    if (selectAll) {
      setSelectedUids([]);
      setSelectAll(false);
    } else {
      setSelectedUids(filteredEmails.map((email) => email.uid));
      setSelectAll(true);
    }
  }, [selectAll, filteredEmails]);

  const handleDeleteSelected = useCallback(async () => {
    if ((selectedAccountId?.length ?? 0) === 0 || selectedUids.length === 0 || (selectedMailbox?.length ?? 0) === 0 ||
        selectedAccountId === null || selectedAccountId === undefined || selectedMailbox === null || selectedMailbox === undefined) return;

    try {
      await window.ipcApi.deleteEmails(selectedAccountId, selectedMailbox, selectedUids);
      removeEmailHeaders(selectedUids);
      setSelectedUids([]);
      setSelectAll(false);
    } catch (e: unknown) {
      // eslint-disable-next-line no-console
      console.error('Failed to delete selected emails:', e);
      setError(e instanceof Error ? e.message : 'Failed to delete selected emails');
    }
  }, [selectedAccountId, selectedUids, selectedMailbox, removeEmailHeaders]);

  const formatDate = useCallback((dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    const isThisYear = date.getFullYear() === now.getFullYear();
    if (isThisYear) {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
    
    return date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });
  }, []);
  
  const hasAttachments = useCallback((email: EmailHeader): boolean => {
    return (email.flags?.includes('\\HasAttachment') ?? false) ||
           (email.attributes?.hasAttachment === true) ||
           false;
  }, []);

  const isStarred = useCallback((email: EmailHeader): boolean => {
    return email.flags?.includes('\\Flagged') ?? false;
  }, []);

  return {
    isLoading,
    isFetchingMore,
    error,
    hasLoadedOnce,
    selectedUids,
    selectAll,
    isToolbarVisible,
    keyboardSelectedIndex,
    emailHeaders,
    filteredEmails,
    hasMoreEmails,
    totalEmailCount,
    observer,
    lastEmailElementRef,
    loadMoreEmails,
    handleSelectEmail,
    handleSelectAll,
    handleDeleteSelected,
    setKeyboardSelectedIndex,
    setSelectedUids,
    setSelectAll,
    formatDate,
    hasAttachments,
    isStarred,
  };
};


---

// FILE: src\shared\hooks\useEmailViewer.ts

/**
 * @file Hook for managing email viewer functionality
 */
import { useState, useEffect, useCallback } from 'react';

import { useAccountStore } from '../store/accountStore';
import { useLogStore } from '../store/logStore';
import type { Email } from '../types/email';


interface EmailContent {
  html?: string | false;
  text?: string;
  textAsHtml?: string;
}

interface UseEmailViewerReturn {
  // State
  emailContent: EmailContent | null;
  isLoading: boolean;
  error: string | null;
  isStarred: boolean;

  // Computed values
  hasContent: boolean;
  hasSuspiciousContent: boolean;

  // Actions
  loadEmailContent: (_accountId: string, _mailboxName: string, _uid: number) => Promise<void>;
  clearContent: () => void;
  retryLoad: () => void;
  handleDelete: () => Promise<void>;
  handleStar: () => void;
  formatDate: (_dateString?: string) => string;
}

/**
 * Hook for managing email viewer functionality
 */
export const useEmailViewer = (): UseEmailViewerReturn => {
  const {
    selectedAccountId,
    selectedMailbox,
    selectedEmailId,
    removeEmailHeader,
    selectEmail,
    setCurrentEmail,
  } = useAccountStore();
  const addLog = useLogStore((state) => state.addLog);

  const [emailContent, setEmailContent] = useState<EmailContent | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isStarred, setIsStarred] = useState(false);
  const [lastLoadParams, setLastLoadParams] = useState<{
    accountId: string;
    mailboxName: string;
    uid: number;
  } | null>(null);

  const loadEmailContent = useCallback(async (accountId: string, mailboxName: string, uid: number) => {
    setIsLoading(true);
    setError(null);
    setLastLoadParams({ accountId, mailboxName, uid });

    try {
      // eslint-disable-next-line no-console
      console.log(`Loading email content for UID ${uid} in ${accountId}/${mailboxName}`);
      const content = await window.ipcApi.getEmailBody(accountId, mailboxName, uid) as EmailContent;
      // eslint-disable-next-line no-console
      console.log('Email content loaded:', content);
      setEmailContent(content);
      // Also update currentEmail in store with full email data including subject
      setCurrentEmail(content as Email);
    } catch (err: unknown) {
      // eslint-disable-next-line no-console
      console.error('Failed to load email content:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load email content';
      setError(errorMessage);
      setEmailContent(null);
    } finally {
      setIsLoading(false);
    }
  }, [setCurrentEmail]);

  const clearContent = useCallback(() => {
    setEmailContent(null);
    setError(null);
    setIsLoading(false);
    setLastLoadParams(null);
  }, []);

  const retryLoad = useCallback(() => {
    if (lastLoadParams !== null && lastLoadParams !== undefined) {
      void loadEmailContent(lastLoadParams.accountId, lastLoadParams.mailboxName, lastLoadParams.uid);
    }
  }, [lastLoadParams, loadEmailContent]);

  // Auto-load content when selected email changes
  useEffect(() => {
    if (selectedAccountId !== null && selectedAccountId !== undefined && selectedAccountId.length > 0 &&
        selectedMailbox !== null && selectedMailbox !== undefined && selectedMailbox.length > 0 &&
        selectedEmailId !== null && selectedEmailId !== undefined && selectedEmailId > 0) {
      void loadEmailContent(selectedAccountId, selectedMailbox, selectedEmailId);
    } else {
      clearContent();
    }
  }, [selectedAccountId, selectedMailbox, selectedEmailId, loadEmailContent, clearContent]);

  // Computed values
  const hasContent = Boolean(emailContent !== null && emailContent !== undefined);
  
  const hasSuspiciousContent = false; // Simplified - no longer using EmailSanitizationService

  // Email action handlers
  const handleDelete = useCallback(async () => {
    if ((selectedAccountId?.length ?? 0) === 0 || (selectedEmailId ?? 0) === 0 || (selectedMailbox?.length ?? 0) === 0 ||
        selectedAccountId === null || selectedAccountId === undefined ||
        selectedEmailId === null || selectedEmailId === undefined ||
        selectedMailbox === null || selectedMailbox === undefined) return;

    try {
      await window.ipcApi.deleteEmail(selectedAccountId, selectedMailbox, selectedEmailId);
      removeEmailHeader(selectedEmailId);
      selectEmail(null);
    } catch (err: unknown) {
      // eslint-disable-next-line no-console
      console.error('Failed to delete email:', err);
      addLog(err instanceof Error ? err.message : 'Failed to delete email', 'error');
    }
  }, [selectedAccountId, selectedEmailId, selectedMailbox, removeEmailHeader, selectEmail, addLog]);

  const handleStar = useCallback(() => {
    try {
      // Toggle starred state
      setIsStarred(!isStarred);
      // TODO: Add actual API call here when implemented
    } catch (err: unknown) {
      // eslint-disable-next-line no-console
      console.error('Failed to toggle star:', err);
      addLog(err instanceof Error ? err.message : 'Failed to toggle star', 'error');
      // Revert the state change on error
      setIsStarred(isStarred);
    }
  }, [isStarred, addLog]);

  // Format readable date
  const formatDate = useCallback((dateString?: string) => {
    if (dateString === null || dateString === undefined || dateString.length === 0) return '';

    const date = new Date(dateString);
    const now = new Date();

    const isToday = date.toDateString() === now.toDateString();
    if (isToday) {
        // Just time, e.g., "4:56 PM"
        return new Intl.DateTimeFormat('en-US', {
            hour: 'numeric',
            minute: 'numeric',
        }).format(date);
    }

    const isThisYear = date.getFullYear() === now.getFullYear();
    if (isThisYear) {
        // e.g., "Apr 20"
        return new Intl.DateTimeFormat('en-US', {
            month: 'short',
            day: 'numeric',
        }).format(date);
    }

    // e.g., "Apr 20, 2023"
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
    }).format(date);
  }, []);

  return {
    emailContent,
    isLoading,
    error,
    isStarred,
    hasContent,
    hasSuspiciousContent,
    loadEmailContent,
    clearContent,
    retryLoad,
    handleDelete,
    handleStar,
    formatDate,
  };
};


---

// FILE: src\shared\hooks\useKeyboardNavigation.ts

/**
 * @file Custom hook for keyboard navigation support
 */
import { useEffect, useCallback } from 'react';

interface ListNavigationReturn {
  selectedIndex: number;
  handleArrowUp: () => void;
  handleArrowDown: () => void;
  handleEnter: () => void;
}

interface UseKeyboardNavigationOptions {
  onArrowUp?: () => void;
  onArrowDown?: () => void;
  onArrowLeft?: () => void;
  onArrowRight?: () => void;
  onEnter?: () => void;
  onEscape?: () => void;
  onSpace?: () => void;
  onDelete?: () => void;
  enabled?: boolean;
}

/**
 * Custom hook that provides keyboard navigation functionality
 * Supports arrow keys, Enter, Escape, Space, and Delete
 */
export const useKeyboardNavigation = (options: UseKeyboardNavigationOptions): void => {
  const {
    onArrowUp,
    onArrowDown,
    onArrowLeft,
    onArrowRight,
    onEnter,
    onEscape,
    onSpace,
    onDelete,
    enabled = true
  } = options;

  const handleKeyDown = useCallback((event: KeyboardEvent): void => {
    if (!enabled) return;

    // Don't handle keyboard events when user is typing in an input
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
      return;
    }

    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault();
        onArrowUp?.();
        break;
      case 'ArrowDown':
        event.preventDefault();
        onArrowDown?.();
        break;
      case 'ArrowLeft':
        event.preventDefault();
        onArrowLeft?.();
        break;
      case 'ArrowRight':
        event.preventDefault();
        onArrowRight?.();
        break;
      case 'Enter':
        event.preventDefault();
        onEnter?.();
        break;
      case 'Escape':
        event.preventDefault();
        onEscape?.();
        break;
      case ' ':
        event.preventDefault();
        onSpace?.();
        break;
      case 'Delete':
      case 'Backspace':
        event.preventDefault();
        onDelete?.();
        break;
    }
  }, [enabled, onArrowUp, onArrowDown, onArrowLeft, onArrowRight, onEnter, onEscape, onSpace, onDelete]);

  useEffect(() => {
    if (enabled) {
      document.addEventListener('keydown', handleKeyDown);
      return (): void => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [handleKeyDown, enabled]);
};

/**
 * Hook for managing focus within a list of items
 */
export const useListNavigation = <T>(
  items: T[],
  selectedIndex: number,
  onSelectionChange: (index: number) => void,
  onActivate?: (item: T, index: number) => void,
  enabled = true
): ListNavigationReturn => {
  const handleArrowUp = useCallback(() => {
    if (items.length === 0) return;
    const newIndex = selectedIndex > 0 ? selectedIndex - 1 : items.length - 1;
    onSelectionChange(newIndex);
  }, [items.length, selectedIndex, onSelectionChange]);

  const handleArrowDown = useCallback(() => {
    if (items.length === 0) return;
    const newIndex = selectedIndex < items.length - 1 ? selectedIndex + 1 : 0;
    onSelectionChange(newIndex);
  }, [items.length, selectedIndex, onSelectionChange]);

  const handleEnter = useCallback(() => {
    if (selectedIndex >= 0 && selectedIndex < items.length) {
      onActivate?.(items[selectedIndex], selectedIndex);
    }
  }, [items, selectedIndex, onActivate]);

  useKeyboardNavigation({
    onArrowUp: handleArrowUp,
    onArrowDown: handleArrowDown,
    onEnter: handleEnter,
    enabled
  });

  return {
    selectedIndex,
    handleArrowUp,
    handleArrowDown,
    handleEnter
  };
};


---

// FILE: src\shared\hooks\useMailboxManager.ts

/**
 * @file Hook for managing mailbox functionality
 */
import {
  Inbox, Trash, Archive, Bookmark, Send,
  AlertCircle, Folder
} from 'lucide-react';
import { useState, useEffect, useCallback, useMemo } from 'react';

import { useAccountStore } from '../store/accountStore';
import type { MailBoxes } from '../types/electron';

interface MailboxAttributes {
  attribs?: string[] | Record<string, unknown>;
  delimiter: string;
  children?: MailBoxes;
}

// UI representation of a folder
interface IFolder {
  name: string; // The actual name used for IMAP commands
  label: string; // The display name in the UI
  icon: React.ElementType;
  count?: number; // Number of emails in this folder
}

// Mapping from IMAP folder attributes to UI elements
const folderAttributeMap: Record<string, { icon: React.ElementType; label: string }> = {
  '\\Inbox': { icon: Inbox, label: 'Inbox' },
  '\\Sent': { icon: Send, label: 'Sent' },
  '\\Junk': { icon: AlertCircle, label: 'Spam' },
  '\\Trash': { icon: Trash, label: 'Trash' },
  '\\Drafts': { icon: Bookmark, label: 'Drafts' },
  '\\Archive': { icon: Archive, label: 'Archive' },
  '\\All': { icon: Archive, label: 'All Mail' },
};

/**
 * Finds the best default mailbox to open
 * Priority: All Mail > INBOX > first available mailbox
 */
function findDefaultMailbox(mailboxes: MailBoxes): string {
  const allMailboxNames: string[] = [];

  // Recursively collect all mailbox names
  function collectNames(boxes: MailBoxes, prefix = ''): void {
    Object.keys(boxes).forEach(name => {
      const box = boxes[name];
      const fullName = prefix ? `${prefix}${(box as { delimiter?: string }).delimiter ?? '/'}${name}` : name;

      // Only add selectable mailboxes (not containers)
      const boxWithAttribs = box as MailboxAttributes;
      const rawAttribs = boxWithAttribs.attribs;
      const attribs: string[] = Array.isArray(rawAttribs) ? rawAttribs : (rawAttribs !== null && rawAttribs !== undefined ? Object.keys(rawAttribs) : []);

      if (!attribs.includes('\\Noselect')) {
        allMailboxNames.push(fullName);
      }

      if (box.children !== undefined) {
        collectNames(box.children, fullName);
      }
    });
  }

  collectNames(mailboxes);

  // Look for "All Mail" variations (Gmail, Outlook, etc.)
  const allMailVariations = [
    '[Gmail]/All Mail',
    '[Google Mail]/All Mail',
    'All Mail',
    'All',
    'Archive',
    'Все письма', // Russian
    'Tous les messages', // French
    'Alle Nachrichten', // German
  ];

  for (const variation of allMailVariations) {
    const found = allMailboxNames.find(name =>
      name.toLowerCase().includes(variation.toLowerCase()) ||
      name === variation
    );
    if (found !== null && found !== undefined && found.length > 0) {
      // eslint-disable-next-line no-console
      console.log(`Found All Mail folder: ${found}`);
      return found;
    }
  }

  // Look for INBOX
  const inbox = allMailboxNames.find(name =>
    name.toUpperCase() === 'INBOX' ||
    name.toLowerCase() === 'inbox'
  );
  if (inbox !== null && inbox !== undefined && inbox.length > 0) {
    // eslint-disable-next-line no-console
    console.log(`Using INBOX: ${inbox}`);
    return inbox;
  }

  // Fallback to first available mailbox
  const fallback = allMailboxNames[0] || 'INBOX';
  // eslint-disable-next-line no-console
  console.log(`Using fallback mailbox: ${fallback}`);
  return fallback;
}

interface UseMailboxManagerReturn {
  // State
  isLoading: boolean;
  isRefreshing: boolean;
  showFolders: boolean;
  setShowFolders: (_show: boolean) => void;
  
  // Data
  mailboxes: MailBoxes | null;
  renderedFolders: IFolder[];
  
  // Actions
  handleRefresh: () => Promise<void>;
}

/**
 * Hook for managing mailbox functionality
 */
export const useMailboxManager = (): UseMailboxManagerReturn => {
  const {
    selectedAccountId,
    mailboxesByAccountId,
    setMailboxesForAccount,
    selectedMailbox,
    selectMailbox,
    clearEmailHeadersForMailbox,
    emailCountByMailbox,
  } = useAccountStore();

  const mailboxes = selectedAccountId !== null && selectedAccountId !== undefined && selectedAccountId.length > 0 ? mailboxesByAccountId[selectedAccountId] : null;

  const [showFolders, setShowFolders] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Load mailboxes when account changes
  useEffect(() => {
    if (selectedAccountId === null || selectedAccountId === undefined || selectedAccountId.length === 0) {
      return;
    }

    const cachedMailboxes = mailboxesByAccountId[selectedAccountId];
    if (cachedMailboxes !== null && cachedMailboxes !== undefined) {
      // Mailboxes are already in cache, no need to fetch.
      // Ensure default mailbox is selected if no mailbox is.
      if (selectedMailbox === null || selectedMailbox === undefined || selectedMailbox.length === 0) {
        const defaultMailbox = findDefaultMailbox(cachedMailboxes);
        selectMailbox(defaultMailbox);
      }
      return;
    }
    
    let isCancelled = false;

    const fetchAndSetMailboxes = async (): Promise<void> => {
      setIsLoading(true);
      try {
        const fetchedMailboxes = await window.ipcApi.getMailboxes(selectedAccountId);
        if (!isCancelled) {
          setMailboxesForAccount(selectedAccountId, fetchedMailboxes);
          // Automatically select default mailbox after fetching
          const defaultMailbox = fetchedMailboxes !== null && fetchedMailboxes !== undefined ? findDefaultMailbox(fetchedMailboxes) : null;
          if (defaultMailbox !== null && defaultMailbox !== undefined && defaultMailbox.length > 0) {
            selectMailbox(defaultMailbox);
          }
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('An error occurred while fetching mailboxes:', error);
        // Could add toast notification here if needed
      } finally {
        if (!isCancelled) {
          setIsLoading(false);
        }
      }
    };

    void fetchAndSetMailboxes();

    return (): void => {
      isCancelled = true;
    };
  }, [selectedAccountId, mailboxesByAccountId, setMailboxesForAccount, selectMailbox, selectedMailbox]);

  // Process mailboxes into rendered folders
  const renderedFolders = useMemo((): IFolder[] => {
    if (mailboxes === null || mailboxes === undefined) return [];

    const processed = new Map<string, IFolder>();

    // Use a recursive function to process all mailboxes including children
    function processBoxes(boxes: MailBoxes, prefix = ''): void {
        Object.keys(boxes).forEach(name => {
            const box = boxes[name];
            const fullName = prefix ? `${prefix}${(box as { delimiter?: string }).delimiter ?? '/'}${name}` : name;

            // Skip if already processed
            if (processed.has(fullName)) return;

            const boxWithAttribs = box as MailboxAttributes;
            const rawAttribs = boxWithAttribs.attribs;
            const attribs: string[] = Array.isArray(rawAttribs) ? rawAttribs : (rawAttribs !== null && rawAttribs !== undefined ? Object.keys(rawAttribs) : []);

            let folderData: IFolder | null = null;

            // Check for special use attributes
            const specialAttr = attribs.find(attr => folderAttributeMap[attr]);
            if (specialAttr !== null && specialAttr !== undefined && specialAttr.length > 0) {
                folderData = { name: fullName, ...folderAttributeMap[specialAttr] };
            } else if (name.toUpperCase() === 'INBOX') {
                folderData = { name: fullName, ...folderAttributeMap['\\Inbox'] };
            } else {
                // Generic folder for anything else that is not a container of other folders
                if (!attribs.includes('\\Noselect')) {
                   folderData = { name: fullName, label: name, icon: Folder };
                }
            }

            if (folderData) {
                // Add email count if available
                const countKey = selectedAccountId !== null && selectedAccountId !== undefined && selectedAccountId.length > 0 ? `${selectedAccountId}-${fullName}` : null;
                const count = countKey !== null && countKey !== undefined && countKey.length > 0 ? emailCountByMailbox[countKey] : undefined;
                folderData.count = count;
                processed.set(folderData.name, folderData);
            }

            if (box.children !== undefined) {
                processBoxes(box.children, fullName);
            }
        });
    }

    processBoxes(mailboxes);

    const folderList = Array.from(processed.values());

    // Sort to have special folders first and in a specific order
    const specialOrder = ['Inbox', 'Sent', 'Drafts', 'Spam', 'Trash', 'Archive'];
    folderList.sort((a, b) => {
        const aIndex = specialOrder.indexOf(a.label);
        const bIndex = specialOrder.indexOf(b.label);

        if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
        if (aIndex !== -1) return -1;
        if (bIndex !== -1) return 1;
        return a.label.localeCompare(b.label); // Sort other folders alphabetically
    });

    return folderList;
  }, [mailboxes, emailCountByMailbox, selectedAccountId]);

  const handleRefresh = useCallback(async () => {
    if ((selectedAccountId?.length ?? 0) === 0 || isRefreshing === true ||
        selectedAccountId === null || selectedAccountId === undefined) return;

    setIsRefreshing(true);
    try {
      // Always fetch fresh mailboxes on refresh
      const fetchedMailboxes = await window.ipcApi.getMailboxes(selectedAccountId);
      setMailboxesForAccount(selectedAccountId, fetchedMailboxes);

      // If a mailbox is selected, clear its emails to trigger a refresh
      if (selectedMailbox !== null && selectedMailbox !== undefined && selectedMailbox.length > 0) {
        clearEmailHeadersForMailbox(selectedAccountId, selectedMailbox);
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error refreshing mailboxes:', error);
      // Could add toast notification here if needed
    } finally {
      setIsRefreshing(false);
    }
  }, [selectedAccountId, isRefreshing, setMailboxesForAccount, selectedMailbox, clearEmailHeadersForMailbox]);

  return {
    isLoading,
    isRefreshing,
    showFolders,
    setShowFolders,
    mailboxes,
    renderedFolders,
    handleRefresh,
  };
};


---

// FILE: src\shared\hooks\useProxyManager.ts

/**
 * @file Hook for managing proxy operations and state
 */
import { zodResolver } from '@hookform/resolvers/zod';
import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useLogStore } from '../store/logStore';
import { useProxyListStore } from '../store/proxyListStore';
import { useProxyStore } from '../store/proxyStore';
import type { GlobalProxyConfig } from '../types/account';

// Proxy form validation schema
const proxyFormSchema = z.object({
  host: z.string().min(1, 'Host is required'),
  port: z.number().min(1, 'Port must be greater than 0').max(65535, 'Port must be less than 65536'),
  username: z.string().optional(),
  password: z.string().optional(),
  type: z.enum(['http', 'https', 'socks4', 'socks5']).optional(),
});

export type ProxyFormData = z.infer<typeof proxyFormSchema>;

interface UseProxyManagerReturn {
  // Form state
  form: ReturnType<typeof useForm<ProxyFormData>>;
  isEditing: boolean;
  editingIndex: number | null;

  // UI state
  isPasswordVisible: boolean;
  setIsPasswordVisible: (_visible: boolean) => void;

  // Actions
  handleAddProxy: (_data: ProxyFormData & { type?: 'http' | 'https' | 'socks4' | 'socks5' }) => void;
  handleEditProxy: (_index: number) => void;
  handleUpdateProxy: (_data: ProxyFormData) => void;
  handleDeleteProxy: (_index: number) => void;
  handleCancelEdit: () => void;
  handleTestProxy: (_index: number) => Promise<void>;
  handleTestAllProxies: () => Promise<void>;
  handleImportProxies: (_text: string) => Promise<void>;
  handleExportProxies: () => string;

  // Proxy settings
  enableProxies: boolean;
  setEnableProxies: (_enabled: boolean) => Promise<void>;

  // Source randomization
  randomizeSource: boolean;
  setRandomizeSource: (_enabled: boolean) => void;
  sourceUrl: string;
  setSourceUrl: (_url: string) => void;

  // Auto update
  autoUpdateEnabled: boolean;
  setAutoUpdateEnabled: (_enabled: boolean) => void;
  updateInterval: number;
  setUpdateInterval: (_interval: number) => void;

  // Retry settings
  maxRetries: number;
  setMaxRetries: (retries: number) => void;

  // Random proxy selection
  useRandomProxy: boolean;
  setUseRandomProxy: (enabled: boolean) => void;

  // Loading states
  isLoading: boolean;
  isTesting: Record<number, boolean>;

  // State from store
  proxies: Array<{ host: string; port: number; username?: string; password?: string; type?: 'http' | 'https' | 'socks4' | 'socks5' }>;
  currentProxyIndex: number;
  testResults: Record<number, { success: boolean; error?: string; timestamp: number }>;
}

/**
 * Hook for managing proxy functionality
 */
/* eslint-disable @typescript-eslint/strict-boolean-expressions, @typescript-eslint/prefer-nullish-coalescing, @typescript-eslint/require-await */
export const useProxyManager = (): UseProxyManagerReturn => {
  const {
    proxies,
    currentProxyIndex,
    testResults,
    addProxy,
    updateProxy,
    deleteProxy,
    testProxy,
    loadProxies,
    removeDuplicates,
    isLoading: storeLoading,
  } = useProxyListStore();

  const { config, setConfig } = useProxyStore();
  const addLog = useLogStore((state) => state.addLog);

  const [isEditing, setIsEditing] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isTesting, setIsTesting] = useState<Record<number, boolean>>({});

  // Derive enableProxies from global proxy store
  const enableProxies = config?.enabled ?? false;

  // Source randomization
  const [randomizeSource, setRandomizeSource] = useState(false);
  const [sourceUrl, setSourceUrl] = useState('');

  // Auto update
  const [autoUpdateEnabled, setAutoUpdateEnabled] = useState(false);
  const [updateInterval, setUpdateInterval] = useState(30);

  // Retry settings
  const [maxRetries, setMaxRetries] = useState(3);

  // Random proxy selection
  const [useRandomProxy, setUseRandomProxy] = useState(false);

  const form = useForm<ProxyFormData>({
    resolver: zodResolver(proxyFormSchema),
    defaultValues: {
      host: '127.0.0.1',
      port: 10808,
      username: '',
      password: '',
    },
  });

  const { reset } = form;

  // Load proxies on mount
  useEffect(() => {
    void loadProxies();
  }, [loadProxies]);

  // Function to enable/disable proxies globally
  const setEnableProxies = useCallback(async (enabled: boolean) => {
    try {
      // Check if IPC API is available
      if (window.ipcApi?.proxy?.setGlobal === null || window.ipcApi?.proxy?.setGlobal === undefined) {
        addLog('IPC API not available', 'error');
        return;
      }

      if (enabled) {
        if (proxies.length === 0) {
          addLog('Cannot enable proxy: No proxies configured', 'error');
          return;
        }

        // Enable proxy with the first available proxy
        const firstProxy = proxies[0];
        addLog(`Attempting to enable proxy: ${firstProxy.host}:${firstProxy.port}`, 'info');

        const proxyConfig: GlobalProxyConfig = {
          enabled: true,
          type: (firstProxy.type ?? 'socks5'),
          hostPort: `${firstProxy.host}:${firstProxy.port}`,
          auth: !!(firstProxy.username !== null && firstProxy.username !== undefined && firstProxy.username.length > 0 &&
                   firstProxy.password !== null && firstProxy.password !== undefined && firstProxy.password.length > 0),
          username: firstProxy.username,
          password: firstProxy.password,
        };

        // Update local state immediately to prevent UI flicker
        setConfig(proxyConfig);
        window.ipcApi.proxy.setGlobal(proxyConfig);
        addLog(`Proxy enabled successfully: ${firstProxy.host}:${firstProxy.port}`, 'success');
      } else {
        // Disable proxy
        addLog('Disabling proxy...', 'info');
        // Update local state immediately
        setConfig(null);
        window.ipcApi.proxy.setGlobal(null);
        addLog('Proxy disabled successfully', 'success');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      addLog(`Proxy operation failed: ${errorMessage}`, 'error');
      // eslint-disable-next-line no-console
      console.error('Proxy operation error:', error);
      // Don't re-throw the error to prevent component crashes
    }
  }, [proxies, setConfig, addLog]);

  const handleAddProxy = useCallback((data: ProxyFormData & { type?: 'http' | 'https' | 'socks4' | 'socks5' }) => {
    // Check for duplicates before adding
    const isDuplicate = proxies.some(proxy =>
      proxy.host.toLowerCase().trim() === data.host.toLowerCase().trim() &&
      proxy.port === data.port &&
      (proxy.username ?? '').toLowerCase().trim() === (data.username ?? '').toLowerCase().trim()
    );

    if (isDuplicate) {
      addLog(`Proxy ${data.host}:${data.port} already exists`, 'error');
      return;
    }

    // Add the proxy
    addProxy(data);
    addLog(`Proxy ${data.host}:${data.port} added`, 'success');

    // Immediately remove duplicates synchronously
    const removedCount = removeDuplicates();
    if (removedCount > 0) {
      addLog(`Removed ${removedCount} duplicate proxy(ies)`, 'info');
    }

    // Reset form
    reset({
      host: '127.0.0.1',
      port: 10808,
      username: '',
      password: '',
    });
  }, [addProxy, addLog, reset, proxies, removeDuplicates]);

  const handleEditProxy = useCallback((index: number) => {
    const proxy = proxies[index];
    if (proxy) {
      setIsEditing(true);
      setEditingIndex(index);
      reset({
        host: proxy.host,
        port: proxy.port,
        username: proxy.username || '',
        password: proxy.password || '',
      });
    }
  }, [proxies, reset]);

  const handleUpdateProxy = useCallback((data: ProxyFormData) => {
    if (editingIndex !== null) {
      updateProxy(editingIndex, data);
      addLog(`Proxy ${data.host}:${data.port} updated`, 'success');
      setIsEditing(false);
      setEditingIndex(null);
      reset();
    }
  }, [editingIndex, updateProxy, addLog, reset]);

  const handleDeleteProxy = useCallback((index: number) => {
    const proxy = proxies[index];
    if (proxy) {
      deleteProxy(index);
      addLog(`Proxy ${proxy.host}:${proxy.port} deleted`, 'success');
      
      // If we were editing this proxy, cancel the edit
      if (editingIndex === index) {
        setIsEditing(false);
        setEditingIndex(null);
        reset();
      }
    }
  }, [proxies, deleteProxy, addLog, editingIndex, reset]);

  const handleCancelEdit = useCallback(() => {
    setIsEditing(false);
    setEditingIndex(null);
    reset();
  }, [reset]);

  const handleTestProxy = useCallback(async (index: number) => {
    const proxy = proxies[index];
    if (proxy) {
      setIsTesting(prev => ({ ...prev, [index]: true }));
      addLog(`Testing proxy ${proxy.host}:${proxy.port}...`, 'info');
      try {
        await testProxy(index);
        const result = testResults[index];
        if (result?.success) {
          addLog(`Proxy ${proxy.host}:${proxy.port} test successful`, 'success');
        } else {
          addLog(`Proxy ${proxy.host}:${proxy.port} test failed: ${result?.error || 'Unknown error'}`, 'error');
        }
      } catch (error) {
        addLog(`Proxy ${proxy.host}:${proxy.port} test failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
      } finally {
        setIsTesting(prev => ({ ...prev, [index]: false }));
      }
    }
  }, [proxies, testProxy, testResults, addLog]);

  const handleTestAllProxies = useCallback(async () => {
    if (proxies.length === 0) {
      addLog('No proxies to test', 'info');
      return;
    }

    addLog(`Testing ${proxies.length} proxies...`, 'info');
    
    const testPromises = proxies.map((_, index) => handleTestProxy(index));
    await Promise.allSettled(testPromises);
    
    addLog('Finished testing all proxies', 'info');
  }, [proxies, handleTestProxy, addLog]);

  const handleImportProxies = useCallback(async (text: string) => {
    try {
      const lines = text.split('\n').filter(line => line.trim());
      let imported = 0;

      for (const line of lines) {
        const trimmed = line.trim();
        if (!trimmed) continue;

        // Support formats: host:port, host:port:username:password, type://host:port
        let host: string;
        let port: number;
        let username: string | undefined;
        let password: string | undefined;


        if (trimmed.includes('://')) {
          // Format: type://host:port or type://username:password@host:port
          const [, rest] = trimmed.split('://');

          if (rest.includes('@')) {
            const [auth, hostPort] = rest.split('@');
            [username, password] = auth.split(':');
            [host, port] = hostPort.split(':').map((v, i) => i === 1 ? parseInt(v) : v) as [string, number];
          } else {
            [host, port] = rest.split(':').map((v, i) => i === 1 ? parseInt(v) : v) as [string, number];
          }
        } else {
          // Format: host:port or host:port:username:password
          const parts = trimmed.split(':');
          if (parts.length >= 2) {
            host = parts[0];
            port = parseInt(parts[1]);
            if (parts.length >= 4) {
              username = parts[2];
              password = parts[3];
            }
          } else {
            continue;
          }
        }

        if (host && !isNaN(port)) {
          addProxy({ host, port, username, password });
          imported++;
        }
      }

      addLog(`Imported ${imported} proxies successfully`, 'success');
    } catch (error) {
      addLog(`Failed to import proxies: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
    }
  }, [addProxy, addLog]);

  const handleExportProxies = useCallback(() => {
    const lines = proxies.map(proxy => {
      const auth = proxy.username && proxy.password ? `${proxy.username}:${proxy.password}@` : '';
      return `socks5://${auth}${proxy.host}:${proxy.port}`;
    });
    return lines.join('\n');
  }, [proxies]);

  return {
    form,
    isEditing,
    editingIndex,
    isPasswordVisible,
    setIsPasswordVisible,
    handleAddProxy,
    handleEditProxy,
    handleUpdateProxy,
    handleDeleteProxy,
    handleCancelEdit,
    handleTestProxy,
    handleTestAllProxies,
    handleImportProxies,
    handleExportProxies,
    enableProxies,
    setEnableProxies,
    randomizeSource,
    setRandomizeSource,
    sourceUrl,
    setSourceUrl,
    autoUpdateEnabled,
    setAutoUpdateEnabled,
    updateInterval,
    setUpdateInterval,
    maxRetries,
    setMaxRetries,
    useRandomProxy,
    setUseRandomProxy,
    isLoading: storeLoading,
    isTesting,
    proxies,
    currentProxyIndex,
    testResults,
  };
};


---

// FILE: src\shared\hooks\useProxyStatus.ts

/**
 * @file Hook for managing proxy status functionality
 */
import {
  Power, PowerOff, Loader, AlertTriangle
} from 'lucide-react';
import type { ComponentType } from 'react';

import { useProxyStore } from '../store/proxyStore';
import type { ProxyStatus } from '../types/electron';

interface UseProxyStatusReturn {
  // Status data
  status: string;
  error: string | null;
  externalIp: string | null;
  config: unknown;
  
  // UI data
  statusInfo: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Icon: React.ComponentType<any>;
    color: string;
    baseLabel: string;
  };
  label: string;
  tooltip: string;
  textColor: string;
  
  // Actions
  handleRefresh: () => void;
}

/**
 * Hook for managing proxy status functionality
 */
export const useProxyStatus = (): UseProxyStatusReturn => {
  const { status, error, externalIp, config } = useProxyStore();

  const handleRefresh = (): void => {
    if (status !== 'connecting' && config?.enabled === true) {
      void window.ipcApi.proxy.setGlobal(config);
    }
  };

  const getStatusInfo = (currentStatus: ProxyStatus): { Icon: ComponentType<Record<string, unknown>>; color: string; baseLabel: string } => {
    switch (currentStatus) {
      case 'disabled':
        return { Icon: PowerOff, color: 'text-muted-foreground', baseLabel: 'Proxy Off' };
      case 'enabled':
        return { Icon: Power, color: 'text-primary', baseLabel: 'Proxy On' };
      case 'connecting':
        return { Icon: Loader, color: 'text-yellow-500', baseLabel: 'Connecting' };
      case 'connected':
        return { Icon: Power, color: 'text-green-500', baseLabel: 'Connected' };
      case 'error':
        return { Icon: AlertTriangle, color: 'text-destructive', baseLabel: 'Error' };
      default:
        return { Icon: AlertTriangle, color: 'text-destructive', baseLabel: 'Unknown' };
    }
  };

  const statusInfo = getStatusInfo(status);

  const label = ((): string => {
    const baseLabel = statusInfo.baseLabel;
    if (status === 'connected' && externalIp !== null && externalIp !== undefined && externalIp.length > 0) {
      return `${baseLabel}: ${externalIp}`;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } else if (status === 'enabled' && (config as any)?.hostPort !== null && (config as any)?.hostPort !== undefined && (config as any)?.hostPort.length > 0) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return `${baseLabel}: ${(config as any).hostPort}`;
    }
    return baseLabel;
  })();

  const tooltip = error !== null && error !== undefined && error.length > 0 ? `${statusInfo.baseLabel}: ${error}` : label;
  const textColor = statusInfo.color.split(' ')[0]; // Use the base color for text

  return {
    status,
    error,
    externalIp,
    config,
    statusInfo,
    label,
    tooltip,
    textColor,
    handleRefresh,
  };
};


---

// FILE: src\shared\store\accountStore.ts

/**
 * @file Zustand store for managing account-related state in the renderer process.
 * This store holds information about user accounts, selections, and email data.
 */

import { create } from 'zustand';

import type { Account, ProxyConfig } from '../types/account';
import type { MailBoxes } from '../types/electron';
import type { EmailHeader } from '../types/email';

type ConnectionStatus = 'connected' | 'disconnected' | 'connecting';

// We'll define a more detailed Email type later, for now, this is for the body
export interface Email extends EmailHeader {
  html?: string | false;
  text?: string;
  textAsHtml?: string;
  to?: {
    text: string;
  };
}

export interface AccountState {
  accounts: Account[];
  selectedAccountId: string | null;
  
  // Cache mailboxes per account
  mailboxesByAccountId: Record<string, MailBoxes | null>;
  
  selectedMailbox: string | null;
  
  // Cache email headers per mailbox, keyed by a composite key "accountId-mailboxName"
  emailHeadersByMailbox: Record<string, EmailHeader[]>;

  // Cache email count per mailbox, keyed by a composite key "accountId-mailboxName"
  emailCountByMailbox: Record<string, number>;

  selectedEmailId: number | null;
  currentEmail: Email | null;
  globalProxy: ProxyConfig | null;
  
  // Cache hasMoreEmails flag per mailbox
  hasMoreEmailsByMailbox: Record<string, boolean>;

  setAccounts: (accounts: Account[]) => void;
  addAccountToStore: (account: Account) => void;
  deleteAccountInStore: (accountId: string) => void;
  selectAccount: (accountId: string | null) => void;

  // Updated mailbox actions
  setMailboxesForAccount: (accountId: string, mailboxes: MailBoxes | null) => void;

  selectMailbox: (mailboxName: string | null) => void;

  // Updated email header actions
  clearEmailHeadersForMailbox: (accountId: string, mailboxName: string) => void;
  setEmailHeadersForMailbox: (accountId: string, mailboxName: string, headers: EmailHeader[]) => void;
  appendEmailHeadersToMailbox: (accountId: string, mailboxName: string, headers: EmailHeader[]) => void;
  prependEmailHeaders: (accountId: string, mailboxName: string, headers: EmailHeader[]) => void;

  removeEmailHeader: (uid: number) => void;
  removeEmailHeaders: (uids: number[]) => void;
  updateEmailHeader: (uid: number, updates: Partial<EmailHeader>) => void;

  selectEmail: (emailId: number | null) => void;
  setCurrentEmail: (email: Email | null) => void;
  updateAccountInStore: (accountId: string, updates: Partial<Account>) => void;
  setAccountConnectionStatus: (accountId: string, status: ConnectionStatus) => void;
  setGlobalProxyConfig: (proxy: ProxyConfig | null) => void;
  setAccountProxyConfig: (accountId: string, proxy: ProxyConfig | null) => void;

  // Updated hasMoreEmails actions
  setHasMoreEmailsForMailbox: (accountId: string, mailboxName: string, hasMore: boolean) => void;

  // Email count actions
  setEmailCountForMailbox: (accountId: string, mailboxName: string, count: number) => void;
}

// Helper functions to reduce main function complexity
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createAccountActions = (set: (fn: any) => void): any => ({
  setAccounts: (accounts: Account[]): void => set({ accounts }),
  addAccountToStore: (account: Account): void => set((state: AccountState) => ({
    accounts: [...state.accounts, { ...account, connectionStatus: 'disconnected' as const }],
  })),
  deleteAccountInStore: (accountId: string): void => set((state: AccountState) => {
    const newMailboxes = { ...state.mailboxesByAccountId };
    delete newMailboxes[accountId];
    const newEmailHeaders = { ...state.emailHeadersByMailbox };
    Object.keys(newEmailHeaders).forEach(key => {
      if (key.startsWith(`${accountId}-`)) {
        delete newEmailHeaders[key];
      }
    });
    return {
      accounts: state.accounts.filter((acc) => acc.id !== accountId),
      selectedAccountId: state.selectedAccountId === accountId ? null : state.selectedAccountId,
      mailboxesByAccountId: newMailboxes,
      emailHeadersByMailbox: newEmailHeaders,
    };
  }),
  selectAccount: (accountId: string | null): void => set({
    selectedAccountId: accountId,
    selectedMailbox: null,
    selectedEmailId: null,
    currentEmail: null,
  }),
  updateAccountInStore: (accountId: string, updates: Partial<Account>): void => set((state: AccountState) => ({
    accounts: state.accounts.map(acc =>
      acc.id === accountId ? { ...acc, ...updates } : acc
    ),
  })),
  setAccountConnectionStatus: (accountId: string, status: ConnectionStatus): void => set((state: AccountState) => ({
    accounts: state.accounts.map(acc =>
      acc.id === accountId ? { ...acc, connectionStatus: status } : acc
    ),
  })),
});

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createMailboxActions = (set: any): any => ({
  setMailboxesForAccount: (accountId: string, mailboxes: MailBoxes | null): void => set((state: AccountState) => ({
    mailboxesByAccountId: {
      ...state.mailboxesByAccountId,
      [accountId]: mailboxes
    }
  })),
  selectMailbox: (mailboxName: string | null): void => set(() => ({
    selectedMailbox: mailboxName,
    selectedEmailId: null,
    currentEmail: null,
  })),
});

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createEmailActions = (set: any): any => ({
  clearEmailHeadersForMailbox: (accountId: string, mailboxName: string): void => {
    const key = `${accountId}-${mailboxName}`;
    set((state: AccountState) => {
      const newEmailHeaders = { ...state.emailHeadersByMailbox };
      delete newEmailHeaders[key];
      const newHasMore = { ...state.hasMoreEmailsByMailbox };
      delete newHasMore[key];
      return {
        emailHeadersByMailbox: newEmailHeaders,
        hasMoreEmailsByMailbox: newHasMore
      };
    });
  },
  setEmailHeadersForMailbox: (accountId: string, mailboxName: string, headers: EmailHeader[]): void => {
    const key = `${accountId}-${mailboxName}`;
    set((state: AccountState) => ({
      emailHeadersByMailbox: {
        ...state.emailHeadersByMailbox,
        [key]: headers
      }
    }));
  },
  appendEmailHeadersToMailbox: (accountId: string, mailboxName: string, headers: EmailHeader[]): void => {
    const key = `${accountId}-${mailboxName}`;
    set((state: AccountState) => {
      const existingHeaders = state.emailHeadersByMailbox[key] ?? [];
      const existingUids = new Set(existingHeaders.map(h => h.uid));
      const uniqueNewHeaders = headers.filter(h => !existingUids.has(h.uid));
      return {
        emailHeadersByMailbox: {
          ...state.emailHeadersByMailbox,
          [key]: [...existingHeaders, ...uniqueNewHeaders]
        }
      };
    });
  },
  prependEmailHeaders: (accountId: string, mailboxName: string, headers: EmailHeader[]): void => {
    const key = `${accountId}-${mailboxName}`;
    set((state: AccountState) => {
      const existingHeaders = state.emailHeadersByMailbox[key] ?? [];
      const uniqueNewHeaders = headers.filter(h => !existingHeaders.some(e => e.uid === h.uid));
      return {
        emailHeadersByMailbox: {
          ...state.emailHeadersByMailbox,
          [key]: [...uniqueNewHeaders, ...existingHeaders]
        }
      };
    });
  },
  removeEmailHeader: (uid: number): void => set((state: AccountState) => {
    if (state.selectedAccountId === null || state.selectedAccountId === undefined ||
        state.selectedMailbox === null || state.selectedMailbox === undefined) return {};
    const key = `${state.selectedAccountId}-${state.selectedMailbox}`;
    const newHeaders = (state.emailHeadersByMailbox[key] ?? []).filter(email => email.uid !== uid);
    return {
      emailHeadersByMailbox: {
        ...state.emailHeadersByMailbox,
        [key]: newHeaders,
      }
    };
  }),
  removeEmailHeaders: (uids: number[]): void => set((state: AccountState) => {
    if (state.selectedAccountId === null || state.selectedAccountId === undefined ||
        state.selectedMailbox === null || state.selectedMailbox === undefined) return {};
    const key = `${state.selectedAccountId}-${state.selectedMailbox}`;
    const newHeaders = (state.emailHeadersByMailbox[key] ?? []).filter(email => !uids.includes(email.uid));
    return {
      emailHeadersByMailbox: {
        ...state.emailHeadersByMailbox,
        [key]: newHeaders,
      }
    };
  }),
  updateEmailHeader: (uid: number, updates: Partial<EmailHeader>): void => set((state: AccountState) => {
    if (state.selectedAccountId === null || state.selectedAccountId === undefined ||
        state.selectedMailbox === null || state.selectedMailbox === undefined) return {};
    const key = `${state.selectedAccountId}-${state.selectedMailbox}`;
    const newHeaders = (state.emailHeadersByMailbox[key] ?? []).map((h) =>
      h.uid === uid ? { ...h, ...updates } : h
    );
    return {
      emailHeadersByMailbox: {
        ...state.emailHeadersByMailbox,
        [key]: newHeaders,
      },
    };
  }),
  selectEmail: (emailId: number | null): void => set({ selectedEmailId: emailId }),
  setCurrentEmail: (email: Email | null): void => set({ currentEmail: email }),
});

export const useAccountStore = create<AccountState>((set) => ({
  accounts: [],
  selectedAccountId: null,
  mailboxesByAccountId: {},
  selectedMailbox: null,
  emailHeadersByMailbox: {},
  emailCountByMailbox: {},
  selectedEmailId: null,
  currentEmail: null,
  hasMoreEmailsByMailbox: {},
  globalProxy: null,

  ...createAccountActions(set),
  ...createMailboxActions(set),
  ...createEmailActions(set),

  setGlobalProxyConfig: (proxy: ProxyConfig | null): void => set({ globalProxy: proxy }),

  setAccountProxyConfig: (accountId: string, proxy: ProxyConfig | null): void => set((state) => ({
    accounts: state.accounts.map(acc =>
      acc.id === accountId ? { ...acc, proxy } : acc
    ),
  })),
  
  setHasMoreEmailsForMailbox: (accountId: string, mailboxName: string, hasMore: boolean): void => {
    const key = `${accountId}-${mailboxName}`;
    set(state => ({
      hasMoreEmailsByMailbox: {
        ...state.hasMoreEmailsByMailbox,
        [key]: hasMore
      }
    }));
  },

  setEmailCountForMailbox: (accountId: string, mailboxName: string, count: number): void => {
    const key = `${accountId}-${mailboxName}`;
    set(state => ({
      emailCountByMailbox: {
        ...state.emailCountByMailbox,
        [key]: count
      }
    }));
  },
}));

---

// FILE: src\shared\store\imapProviders.ts

/**
 * @file Contains a list of common email provider IMAP settings.
 * This is used for autodetecting server settings when a user adds an account.
 */

import type { DiscoveredConfig } from "../types/protocol";

interface ImapProvider {
  name: string;
  domains: string[];
  config: DiscoveredConfig;
}

export const imapProviders: ImapProvider[] = [
  {
    name: "Gmail",
    domains: ["gmail.com", "googlemail.com"],
    config: {
      imap: { host: "imap.gmail.com", port: 993, secure: true },
      smtp: { host: "smtp.gmail.com", port: 465, secure: true },
    },
  },
  {
    name: "Outlook",
    domains: ["outlook.com", "hotmail.com", "live.com", "msn.com"],
    config: {
      imap: { host: "outlook.office365.com", port: 993, secure: true },
      smtp: { host: "smtp.office365.com", port: 587, secure: true }, // STARTTLS
    },
  },
  {
    name: "Yahoo",
    domains: ["yahoo.com", "ymail.com", "rocketmail.com"],
    config: {
      imap: { host: "imap.mail.yahoo.com", port: 993, secure: true },
      smtp: { host: "smtp.mail.yahoo.com", port: 465, secure: true },
    },
  },
  {
    name: "iCloud",
    domains: ["icloud.com", "me.com", "mac.com"],
    config: {
      imap: { host: "imap.mail.me.com", port: 993, secure: true },
      smtp: { host: "smtp.mail.me.com", port: 587, secure: true }, // STARTTLS
    },
  },
  {
    name: "GMX",
    domains: ["gmx.com", "gmx.net", "gmx.ch", "gmx.de"],
    config: {
      imap: { host: "mail.gmx.net", port: 993, secure: true },
      smtp: { host: "mail.gmx.ch", port: 587, secure: true },
    },
  },
  {
    name: "AOL",
    domains: ["aol.com"],
    config: {
      imap: { host: "imap.aol.com", port: 993, secure: true },
      smtp: { host: "smtp.aol.com", port: 465, secure: true },
    },
  },
  {
    name: "Zoho Mail",
    domains: ["zoho.com"],
    config: {
      imap: { host: "imappro.zoho.com", port: 993, secure: true },
      smtp: { host: "smtppro.zoho.com", port: 465, secure: true },
    },
  },
  {
    name: "Mail.ru",
    domains: ["mail.ru", "bk.ru", "list.ru", "inbox.ru"],
    config: {
      imap: { host: "imap.mail.ru", port: 993, secure: true },
      smtp: { host: "smtp.mail.ru", port: 465, secure: true },
    },
  },
  {
    name: "Yandex",
    domains: ["yandex.com", "yandex.ru"],
    config: {
      imap: { host: "imap.yandex.com", port: 993, secure: true },
      smtp: { host: "smtp.yandex.com", port: 465, secure: true },
    },
  },
  {
    name: "Rambler",
    domains: ["rambler.ru", "lenta.ru", "autorambler.ru", "myrambler.ru", "ro.ru"],
    config: {
      imap: { host: "imap.rambler.ru", port: 993, secure: true },
      smtp: { host: "smtp.rambler.ru", port: 465, secure: true },
    },
  },
  {
    name: "QIP.ru",
    domains: ["qip.ru"],
    config: {
      imap: { host: "imap.qip.ru", port: 993, secure: true },
      smtp: { host: "smtp.qip.ru", port: 465, secure: true },
    },
  },
  {
    name: "Pochta.ru",
    domains: ["pochta.ru"],
    config: {
      imap: { host: "imap.pochta.ru", port: 993, secure: true },
      smtp: { host: "smtp.pochta.ru", port: 465, secure: true },
    },
  },
  {
    name: "Fastmail",
    domains: ["fastmail.com", "fastmail.fm"],
    config: {
      imap: { host: "imap.fastmail.com", port: 993, secure: true },
      smtp: { host: "smtp.fastmail.com", port: 465, secure: true },
    },
  },
]; 

---

// FILE: src\shared\store\logStore.ts

/**
 * @file Zustand store for managing a persistent log of application events.
 */
import { v4 as uuidv4 } from 'uuid';
import { create } from 'zustand';

export type LogType = 'success' | 'error' | 'info';

export interface LogMessage {
  id: string;
  message: string;
  type: LogType;
  timestamp: number;
}

export interface LogState {
  logs: LogMessage[];
  addLog: (message: string, type?: LogType) => void;
  clearLogs: () => void;
}

export const useLogStore = create<LogState>((set) => ({
  logs: [],
  addLog: (message, type = 'info'): void => {
    const newLog: LogMessage = {
      id: uuidv4(),
      message,
      type,
      timestamp: Date.now(),
    };
    // Keep only the last 500 logs to prevent memory issues
    set((state) => ({ logs: [...state.logs, newLog].slice(-500) }));
  },
  clearLogs: (): void => set({ logs: [] }),
})); 

---

// FILE: src\shared\store\mainSettingsStore.ts

/**
 * @file Main application settings store
 */
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface MainSettings {
  // Logger settings
  hideEventLogger: boolean;

  // Auto-login settings
  autoLoginOnStartup: boolean;

  // Interface settings
  compactAccountView: boolean;

  // Future settings can be added here
  // theme: 'light' | 'dark' | 'system';
  // language: string;
  // notifications: boolean;
}

interface MainSettingsState {
  settings: MainSettings;
  
  // Actions
  updateSettings: (updates: Partial<MainSettings>) => void;
  resetSettings: () => void;
  
  // Individual setting updaters for convenience
  setHideEventLogger: (hide: boolean) => void;
  setAutoLoginOnStartup: (autoLogin: boolean) => void;
  setCompactAccountView: (compact: boolean) => void;
}

const defaultSettings: MainSettings = {
  hideEventLogger: false,
  autoLoginOnStartup: false,
  compactAccountView: false,
};

export const useMainSettingsStore = create<MainSettingsState>()(
  persist(
    (set, get) => ({
      settings: defaultSettings,
      
      updateSettings: (updates: Partial<MainSettings>) => {
        set((state) => ({
          settings: { ...state.settings, ...updates }
        }));
      },
      
      resetSettings: () => {
        set({ settings: defaultSettings });
      },
      
      setHideEventLogger: (hide: boolean) => {
        get().updateSettings({ hideEventLogger: hide });
      },
      
      setAutoLoginOnStartup: (autoLogin: boolean) => {
        get().updateSettings({ autoLoginOnStartup: autoLogin });
      },

      setCompactAccountView: (compact: boolean) => {
        get().updateSettings({ compactAccountView: compact });
      },
    }),
    {
      name: 'main-settings-storage',
      version: 1,
    }
  )
);


---

// FILE: src\shared\store\proxyListStore.ts

/**
 * @file Zustand store for managing proxy list state.
 */
import { create } from 'zustand';

interface ProxyItem {
  host: string;
  port: number;
  username?: string;
  password?: string;
  type?: 'http' | 'https' | 'socks4' | 'socks5';
}

interface TestResult {
  success: boolean;
  error?: string;
  responseTime?: number;
  loading?: boolean;
  timestamp: number;
}

interface ProxyListState {
  proxies: ProxyItem[];
  currentProxyIndex: number;
  testResults: Record<number, TestResult>;
  isLoading: boolean;

  // Actions
  setProxies: (proxies: ProxyItem[]) => void;
  addProxy: (proxy: ProxyItem) => void;
  updateProxy: (index: number, proxy: ProxyItem) => void;
  deleteProxy: (index: number) => void;
  setCurrentProxyIndex: (index: number) => void;
  setTestResult: (index: number, result: TestResult) => void;
  setLoading: (loading: boolean) => void;
  removeDuplicates: () => number;

  // Async actions
  loadProxies: () => Promise<void>;
  saveProxies: () => Promise<void>;
  testProxy: (index: number) => Promise<void>;
}

// Helper functions to reduce main function complexity
const createProxyActions = (
  set: (partial: Partial<ProxyListState> | ((_state: ProxyListState) => Partial<ProxyListState>)) => void,
  get: () => ProxyListState
): {
  setProxies: (_proxies: ProxyItem[]) => void;
  addProxy: (_proxy: ProxyItem) => void;
  updateProxy: (_index: number, _proxy: ProxyItem) => void;
  deleteProxy: (_index: number) => void;
  setCurrentProxyIndex: (_index: number) => void;
  setTestResult: (_index: number, _result: TestResult) => void;
  setLoading: (_loading: boolean) => void;
} => ({
  setProxies: (proxies: ProxyItem[]): void => set({ proxies }),

  addProxy: (proxy: ProxyItem): void => {
    const state = get();
    const newProxies = [...state.proxies, proxy];
    set({ proxies: newProxies });
    void get().saveProxies();
  },

  updateProxy: (index: number, proxy: ProxyItem): void => {
    const state = get();
    const newProxies = [...state.proxies];
    newProxies[index] = proxy;
    set({ proxies: newProxies });
    void get().saveProxies();
  },

  deleteProxy: (index: number): void => {
    const state = get();
    const newProxies = state.proxies.filter((_: ProxyItem, i: number) => i !== index);
    set({
      proxies: newProxies,
      currentProxyIndex: state.currentProxyIndex >= newProxies.length ? 0 : state.currentProxyIndex
    });
    void get().saveProxies();
  },

  setCurrentProxyIndex: (index: number): void => set({ currentProxyIndex: index }),
  setTestResult: (index: number, result: TestResult): void => set((state: ProxyListState) => ({
    testResults: { ...state.testResults, [index]: result }
  })),
  setLoading: (loading: boolean): void => set({ isLoading: loading }),
});

// Utility functions for proxy operations
const createRemoveDuplicates = (
  set: (_partial: Partial<ProxyListState>) => void,
  get: () => ProxyListState
) => (): number => {
  const state = get();
  const uniqueProxies: ProxyItem[] = [];
  const seen = new Set<string>();

  for (const proxy of state.proxies) {
    const key = `${proxy.host.toLowerCase().trim()}:${proxy.port}:${(proxy.username ?? '').toLowerCase().trim()}:${proxy.type ?? 'socks5'}`;
    if (!seen.has(key)) {
      seen.add(key);
      uniqueProxies.push(proxy);
    }
  }

  const removedCount = state.proxies.length - uniqueProxies.length;
  if (removedCount > 0) {
    set({ proxies: uniqueProxies });
    void get().saveProxies();
  }
  return removedCount;
};

const createLoadProxies = (
  set: (_partial: Partial<ProxyListState>) => void
) => async (): Promise<void> => {
  try {
    const proxyConfigs = await window.ipcApi.getProxyList();
    const proxies: ProxyItem[] = proxyConfigs.map(config => {
      const [host, port] = (config.hostPort ?? `${config.host}:${config.port}`).split(':');
      return {
        host,
        port: parseInt(port),
        username: config.username,
        password: config.password,
        type: config.type ?? 'socks5',
      };
    });
    set({ proxies });
  } catch {
    // Failed to load proxies
  }
};

const createSaveProxies = (
  get: () => ProxyListState
) => async (): Promise<void> => {
  try {
    const state = get();
    const proxyConfigs = state.proxies.map((proxy: ProxyItem) => ({
      host: proxy.host,
      port: proxy.port,
      hostPort: `${proxy.host}:${proxy.port}`,
      auth: !!(proxy.username !== null && proxy.username !== undefined && proxy.username.length > 0 &&
               proxy.password !== null && proxy.password !== undefined && proxy.password.length > 0),
      username: proxy.username,
      password: proxy.password,
      type: proxy.type ?? 'socks5',
    }));
    await window.ipcApi.saveProxyList(proxyConfigs);
  } catch {
    // Failed to save proxies
  }
};

const createTestProxy = (
  set: (_partial: Partial<ProxyListState> | ((_state: ProxyListState) => Partial<ProxyListState>)) => void,
  get: () => ProxyListState
) => async (index: number): Promise<void> => {
  const state = get();
  const proxy = state.proxies[index];
  if (proxy === undefined || proxy === null) return;

  set((currentState: ProxyListState) => ({
    testResults: {
      ...currentState.testResults,
      [index]: { success: false, loading: true, timestamp: Date.now() }
    }
  }));

  try {
    const startTime = Date.now();
    const result = await window.ipcApi.testProxy({
      host: proxy.host,
      port: proxy.port,
      username: proxy.username,
      password: proxy.password,
      type: proxy.type ?? 'socks5',
    });
    const responseTime = Date.now() - startTime;

    set((currentState: ProxyListState) => ({
      testResults: {
        ...currentState.testResults,
        [index]: {
          success: result.success,
          loading: false,
          error: result.success === true ? undefined : (result.error ?? 'Connection failed'),
          responseTime,
          timestamp: Date.now(),
        }
      }
    }));
  } catch (error) {
    set((currentState: ProxyListState) => ({
      testResults: {
        ...currentState.testResults,
        [index]: {
          success: false,
          loading: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: Date.now(),
        }
      }
    }));
  }
};

const createUtilityActions = (
  set: (_partial: Partial<ProxyListState> | ((_state: ProxyListState) => Partial<ProxyListState>)) => void,
  get: () => ProxyListState
): {
  removeDuplicates: () => number;
  loadProxies: () => Promise<void>;
  saveProxies: () => Promise<void>;
  testProxy: (_index: number) => Promise<void>;
} => ({
  removeDuplicates: createRemoveDuplicates(set, get),
  loadProxies: createLoadProxies(set),
  saveProxies: createSaveProxies(get),
  testProxy: createTestProxy(set, get),
});

export const useProxyListStore = create<ProxyListState>((set, get) => ({
  proxies: [],
  currentProxyIndex: 0,
  testResults: {},
  isLoading: false,
  
  ...createProxyActions(set, get),
  ...createUtilityActions(set, get),


}));


---

// FILE: src\shared\store\proxyStore.ts

/**
 * @file Zustand store for managing global proxy state.
 */
import { create } from 'zustand';

import type { GlobalProxyConfig } from '../types/account';
import type { ProxyStatus } from '../types/electron';

interface ProxyState {
  config: GlobalProxyConfig | null;
  status: ProxyStatus;
  error: string | null;
  externalIp: string | null;
  setConfig: (config: GlobalProxyConfig | null) => void;
  setStatus: (status: ProxyStatus, details?: { ip?: string; error?: string }) => void;
  initializeProxy: () => Promise<void>;
}

export const useProxyStore = create<ProxyState>((set, get) => ({
  config: null,
  status: 'disabled',
  error: null,
  externalIp: null,
  
  /**
   * Updates the global proxy configuration.
   */
  setConfig: (config: GlobalProxyConfig | null): void => {
    set({ config });
  },

  setStatus: (status: ProxyStatus, details: { error?: string; ip?: string } = {}): void => {
    const newState: Partial<ProxyState> = {
      status,
      error: details.error ?? null,
    };
    if (status === 'connected') {
      newState.externalIp = details.ip ?? null;
    } else if (status === 'disabled' || status === 'error') {
      newState.externalIp = null;
    }
    set(newState);
  },

  setExternalIp: (ip: string | null): void => set({ externalIp: ip }),
  setError: (error: string | null): void => set({ error }),
  
  /**
   * Fetches the initial proxy configuration from the main process.
   */
  initializeProxy: async (): Promise<void> => {
    try {
      const config = await window.ipcApi.proxy.getGlobal();
      get().setConfig(config);

      // Set up listener for proxy status updates
      window.ipcApi.proxy.onStatusUpdate((_event, status) => {
        get().setStatus(status.status, {
          ip: status.ip,
          error: status.error
        });
      });
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error('Failed to initialize proxy settings:', e);
      get().setStatus('error', { error: e instanceof Error ? e.message : 'Initialization failed' });
    }
  },
})); 

---

// FILE: src\shared\store\uiStore.ts

import { create } from 'zustand';

type ViewType = 'email' | 'settings';

interface UIState {
  isSettingsOpen: boolean;
  currentView: ViewType;
  isLeftPanelHidden: boolean;

  // Panel states
  isLogPanelCollapsed: boolean;
  isAccountPanelCollapsed: boolean;

  // Panel sizes
  leftPanelWidth: number;
  rightPanelWidth: number;
  logPanelHeight: number;

  // Actions
  openSettings: () => void;
  closeSettings: () => void;
  setLeftPanelHidden: (hidden: boolean) => void;
  toggleLogPanel: () => void;
  setAccountPanelCollapsed: (collapsed: boolean) => void;
  setPanelSizes: (sizes: { leftPanelWidth?: number; rightPanelWidth?: number; logPanelHeight?: number }) => void;
  resetConfig: () => void;
  loadConfig: () => Promise<void>;
  saveConfig: () => Promise<void>;
}

const defaultState = {
  isSettingsOpen: false,
  currentView: 'email' as ViewType,
  isLeftPanelHidden: false,
  isLogPanelCollapsed: false,
  isAccountPanelCollapsed: false,
  leftPanelWidth: 25,
  rightPanelWidth: 25,
  logPanelHeight: 25,
};

let isLoading = false; // Flag to prevent save during load
let saveConfigTimeout: NodeJS.Timeout | null = null; // Debounce timeout for config saving

// Debounced save function to prevent excessive saves during resize
const debouncedSaveConfig = (saveConfigFn: () => Promise<void>): void => {
  if (saveConfigTimeout) {
    clearTimeout(saveConfigTimeout);
  }
  saveConfigTimeout = setTimeout(() => {
    void saveConfigFn();
  }, 150); // 150ms delay for better responsiveness
};

// Load initial config synchronously if possible
const getInitialState = (): typeof defaultState => {
  try {
    // Try to load config synchronously from localStorage as fallback
    const savedConfig = localStorage.getItem('ui-config');
    if (savedConfig !== null && savedConfig !== undefined && savedConfig.length > 0) {
      const parsed = JSON.parse(savedConfig);
      return { ...defaultState, ...parsed };
    }
  } catch {
    // Silently fall back to defaults
  }
  return defaultState;
};

const initialState = getInitialState();

// Helper functions to reduce main function complexity
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createUIActions = (set: any, get: any): any => ({
  openSettings: (): void => set({ isSettingsOpen: true, currentView: 'settings', isLeftPanelHidden: true }),
  closeSettings: (): void => set({ isSettingsOpen: false, currentView: 'email', isLeftPanelHidden: false }),

  setLeftPanelHidden: (hidden: boolean): void => {
    set({ isLeftPanelHidden: hidden });
    if (!isLoading) void get().saveConfig();
  },

  toggleLogPanel: (): void => {
    set((state: UIState) => ({ isLogPanelCollapsed: !state.isLogPanelCollapsed }));
    if (!isLoading) void get().saveConfig();
  },

  setAccountPanelCollapsed: (collapsed: boolean): void => {
    set({ isAccountPanelCollapsed: collapsed });
    if (!isLoading) {
      debouncedSaveConfig(get().saveConfig);
    }
  },

  setPanelSizes: (sizes: { leftPanelWidth?: number; rightPanelWidth?: number; logPanelHeight?: number }): void => {
    set((state: UIState) => {
      const newState: Partial<UIState> = {};
      let hasChanges = false;

      if (sizes.leftPanelWidth !== undefined && sizes.leftPanelWidth !== state.leftPanelWidth) {
        newState.leftPanelWidth = sizes.leftPanelWidth;
        hasChanges = true;
      }
      if (sizes.rightPanelWidth !== undefined && sizes.rightPanelWidth !== state.rightPanelWidth) {
        newState.rightPanelWidth = sizes.rightPanelWidth;
        hasChanges = true;
      }
      if (sizes.logPanelHeight !== undefined && sizes.logPanelHeight !== state.logPanelHeight) {
        newState.logPanelHeight = sizes.logPanelHeight;
        hasChanges = true;
      }

      if (hasChanges && !isLoading) {
        debouncedSaveConfig(get().saveConfig);
      }

      return hasChanges ? newState : {};
    });
  },
});

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createConfigActions = (set: any, get: any): any => ({
  resetConfig: (): void => {
    set(initialState);
    void get().saveConfig();
  },

  loadConfig: async (): Promise<void> => {
    try {
      isLoading = true;
      const config = await window.ipcApi.getUserConfig();
      if (config !== null && config !== undefined && typeof config === 'object') {
        set({
          isLeftPanelHidden: config.isLeftPanelHidden ?? initialState.isLeftPanelHidden,
          isLogPanelCollapsed: config.isLogPanelCollapsed ?? initialState.isLogPanelCollapsed,
          isAccountPanelCollapsed: config.isAccountPanelCollapsed ?? initialState.isAccountPanelCollapsed,
          leftPanelWidth: config.leftPanelWidth ?? initialState.leftPanelWidth,
          rightPanelWidth: config.rightPanelWidth ?? initialState.rightPanelWidth,
          logPanelHeight: config.logPanelHeight ?? initialState.logPanelHeight,
        });
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to load UI config:', error);
    } finally {
      isLoading = false;
    }
  },

  saveConfig: async (): Promise<void> => {
    try {
      const state = get();
      await window.ipcApi.saveUserConfig({
        isLeftPanelHidden: state.isLeftPanelHidden,
        isLogPanelCollapsed: state.isLogPanelCollapsed,
        isAccountPanelCollapsed: state.isAccountPanelCollapsed,
        leftPanelWidth: state.leftPanelWidth,
        rightPanelWidth: state.rightPanelWidth,
        logPanelHeight: state.logPanelHeight,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to save UI config:', error);
    }
  },
});

export const useUIStore = create<UIState>((set, get) => ({
  ...initialState,

  ...createUIActions(set, get),


  ...createConfigActions(set, get),
}));

---

// FILE: src\shared\types\account.ts

/**
 * @file Account types and schemas for email account management
 */

import { z } from 'zod';

export interface ProxyConfig {
  host: string;
  port: number;
  username?: string;
  password?: string;
  type?: 'http' | 'https' | 'socks4' | 'socks5';
  // Extended properties for proxy list management
  enabled?: boolean;
  hostPort?: string;
  auth?: boolean;
}

export interface GlobalProxyConfig {
  enabled: boolean;
  type: 'http' | 'https' | 'socks4' | 'socks5';
  hostPort: string;
  auth: boolean;
  username?: string;
  password?: string;
}

export interface IncomingServerConfig {
  protocol: 'imap' | 'pop3';
  host: string;
  port: number;
  useTls: boolean;
}

export interface OutgoingServerConfig {
  protocol: 'smtp';
  host: string;
  port: number;
  useTls: boolean;
}

export interface Account {
  id: string;
  displayName?: string;
  email: string;
  password: string;
  incoming: IncomingServerConfig;
  outgoing?: OutgoingServerConfig;
  useProxy?: boolean;
  proxy?: ProxyConfig | null;
  connectionStatus?: 'connected' | 'disconnected' | 'connecting';
}

// Zod schemas for validation
export const proxyConfigSchema = z.object({
  host: z.string().min(1, 'Host is required'),
  port: z.number().min(1).max(65535),
  username: z.string().optional(),
  password: z.string().optional(),
  type: z.enum(['http', 'https', 'socks4', 'socks5']).optional(),
});

export const incomingServerConfigSchema = z.object({
  protocol: z.enum(['imap', 'pop3']),
  host: z.string().min(1, 'Host is required'),
  port: z.number().min(1).max(65535),
  useTls: z.boolean(),
});

export const outgoingServerConfigSchema = z.object({
  protocol: z.literal('smtp'),
  host: z.string().min(1, 'Host is required'),
  port: z.number().min(1).max(65535),
  useTls: z.boolean(),
});

export const accountSchema = z.object({
  id: z.string(),
  displayName: z.string().optional(),
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  incoming: incomingServerConfigSchema,
  outgoing: outgoingServerConfigSchema.optional(),
  useProxy: z.boolean().optional(),
  proxy: proxyConfigSchema.nullable().optional(),
  connectionStatus: z.enum(['connected', 'disconnected', 'connecting']).optional(),
});


---

// FILE: src\shared\types\electron.ts

/**
 * @file Electron type definitions for IPC communication
 */

import type { Account, ProxyConfig, GlobalProxyConfig } from './account';
import type { EmailHeader } from './email';

export type ProxyStatus = 'disabled' | 'enabled' | 'connecting' | 'connected' | 'error';

export interface MailBoxes {
  [key: string]: {
    attribs?: string[];
    children?: MailBoxes;
    delimiter: string;
  };
}

export interface IIpcAPI {
  // Account management
  discoverEmailConfig: (_email: string) => Promise<unknown>;
  getAccounts: () => Promise<Account[]>;
  addAccount: (_accountData: Omit<Account, 'id'>) => Promise<Account>;
  updateAccount: (_accountId: string, _accountData: Partial<Omit<Account, 'id'>>) => Promise<Account>;
  deleteAccount: (_accountId: string) => Promise<{ success: boolean }>;

  // Event listeners
  on: <T = unknown>(_event: string, _callback: (_data: T) => void) => void;
  rendererReady: () => void;

  // Import functionality
  importFromFileContent: (_content: string) => Promise<{ addedCount: number; skippedCount: number; error?: string }>;
  importFromFileInstant: (_content: string) => Promise<{ addedCount: number; skippedCount: number; error?: string }>;

  // Inbox watching
  watchInbox: (_accountId: string) => Promise<void>;

  // Mailbox operations
  getMailboxes: (_accountId: string) => Promise<MailBoxes>;
  selectMailbox: (_accountId: string, _mailboxName: string, _pageSize: number) => Promise<unknown>;

  // Email operations
  getEmails: (_accountId: string, _mailboxName: string, _offset: number, _limit: number) => Promise<EmailHeader[]>;
  getEmailBody: (_accountId: string, _mailboxName: string, _uid: number) => Promise<unknown>;
  deleteEmail: (_accountId: string, _mailboxName: string, _uid: number) => Promise<void>;
  deleteEmails: (_accountId: string, _mailboxName: string, _uids: number[]) => Promise<void>;
  markAsSeen: (_accountId: string, _emailUid: number, _mailbox: string) => Promise<void>;
  markAsUnseen: (_accountId: string, _emailUid: number, _mailbox: string) => Promise<void>;

  // Event handlers
  onNewMail: (_callback: (_event: unknown, _data: { accountId: string; mailboxName: string; newMailCount: number }) => void) => () => void;

  // Proxy management
  proxy: {
    getGlobal: () => Promise<GlobalProxyConfig | null>;
    setGlobal: (_config: GlobalProxyConfig | null) => void;
    onStatusUpdate: (_callback: (_event: unknown, _status: { status: ProxyStatus; ip?: string; error?: string }) => void) => void;
  };

  // Proxy list management
  getProxyList: () => Promise<ProxyConfig[]>;
  saveProxyList: (_proxies: ProxyConfig[]) => Promise<void>;
  testProxy: (_config: ProxyConfig) => Promise<{ success: boolean; ip?: string; error?: string }>;

  // User config management
  getUserConfig: () => Promise<Record<string, unknown> | null>;
  saveUserConfig: (_config: Record<string, unknown>) => Promise<void>;

  // IMAP operations (legacy)
  imap: {
    getMailboxes: (_accountId: string) => Promise<unknown>;
    getEmails: (_accountId: string, _mailboxName: string, _offset: number, _limit: number) => Promise<EmailHeader[]>;
    getEmailBody: (_accountId: string, _emailUid: number, _mailbox: string) => Promise<unknown>;
    deleteEmail: (_accountId: string, _emailUid: number, _mailbox: string) => Promise<void>;
    markAsSeen: (_accountId: string, _emailUid: number, _mailbox: string) => Promise<void>;
    markAsUnseen: (_accountId: string, _emailUid: number, _mailbox: string) => Promise<void>;
    deleteEmails: (_accountId: string, _emailUids: number[], _mailbox: string) => Promise<void>;
  };
}




---

// FILE: src\shared\types\email.ts

/**
 * @file Email types for headers and content
 */

export interface EmailHeader {
  uid: number;
  subject: string;
  from: {
    text: string;
  };
  date: string;
  seen: boolean;
  snippet?: string;
  flags?: string[];
  attributes?: Record<string, unknown>;
}

export interface Email extends EmailHeader {
  html?: string | false;
  text?: string;
  textAsHtml?: string;
  to?: {
    text: string;
  };
  flags?: string[];
}


---

// FILE: src\shared\types\protocol.ts

/**
 * @file Protocol types for email server configuration and discovery
 */

export interface ServerConfig {
  host: string;
  port: number;
  secure: boolean;
}

export interface DiscoveredConfig {
  imap?: ServerConfig | null;
  smtp?: ServerConfig | null;
  pop3?: ServerConfig | null;
}

export interface ProtocolConfig {
  protocol: 'imap' | 'pop3' | 'smtp';
  host: string;
  port: number;
  secure: boolean;
}


---

// FILE: src\shared\ui\Toast.tsx

/**
 * @file Toast notification system using Sonner with ShadCN UI integration
 */
import { CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

export type ToastType = 'success' | 'error' | 'info' | 'warning';

interface ToastOptions {
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * Enhanced toast functions with ShadCN styling
 */
export const showToast = {
  success: ({ title, message, duration = 5000, action }: ToastOptions): string | number => {
    return toast.success(title, {
      description: message,
      duration,
      action: action ? {
        label: action.label,
        onClick: action.onClick,
      } : undefined,
      icon: <CheckCircle size={20} />,
    });
  },

  error: ({ title, message, duration = 5000, action }: ToastOptions): string | number => {
    return toast.error(title, {
      description: message,
      duration,
      action: action ? {
        label: action.label,
        onClick: action.onClick,
      } : undefined,
      icon: <AlertCircle size={20} />,
    });
  },

  info: ({ title, message, duration = 5000, action }: ToastOptions): string | number => {
    return toast.info(title, {
      description: message,
      duration,
      action: action ? {
        label: action.label,
        onClick: action.onClick,
      } : undefined,
      icon: <Info size={20} />,
    });
  },

  warning: ({ title, message, duration = 5000, action }: ToastOptions): string | number => {
    return toast.warning(title, {
      description: message,
      duration,
      action: action ? {
        label: action.label,
        onClick: action.onClick,
      } : undefined,
      icon: <AlertTriangle size={20} />,
    });
  },
};

// Export Toaster component from Sonner
export { Toaster as ToastContainer } from 'sonner';


---

// FILE: src\shared\ui\avatar.tsx

/**
 * @file Simple avatar component without Radix UI dependency
 */
import * as React from "react";

import { cn } from "../utils/utils";

interface AvatarProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

interface AvatarImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  className?: string;
}

interface AvatarFallbackProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

const Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "relative flex h-8 w-8 shrink-0 overflow-hidden rounded-full",
        className
      )}
      {...props}
    />
  )
);
Avatar.displayName = "Avatar";

const AvatarImage = React.forwardRef<HTMLImageElement, AvatarImageProps>(
  ({ className, ...props }, ref) => (
    <img
      ref={ref}
      className={cn("aspect-square h-full w-full object-cover", className)}
      {...props}
    />
  )
);
AvatarImage.displayName = "AvatarImage";

const AvatarFallback = React.forwardRef<HTMLDivElement, AvatarFallbackProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "flex h-full w-full items-center justify-center rounded-full bg-muted text-muted-foreground",
        className
      )}
      {...props}
    />
  )
);
AvatarFallback.displayName = "AvatarFallback";

export { Avatar, AvatarImage, AvatarFallback };


---

// FILE: src\shared\ui\badge.tsx

import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import * as React from "react"

import { cn } from "../utils/utils"

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",
        destructive:
          "border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<"span"> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }): React.JSX.Element {
  const Comp = asChild ? Slot : "span"

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    />
  )
}

export { Badge, badgeVariants }


---

// FILE: src\shared\ui\button.tsx

import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import * as React from "react"

import { cn } from "../utils/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost:
          "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const Button = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<"button"> &
    VariantProps<typeof buttonVariants> & {
      asChild?: boolean
    }
>(({ className, variant, size, asChild = false, ...props }, ref) => {
  const Comp = asChild ? Slot : "button"

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      ref={ref}
      {...props}
    />
  )
})
Button.displayName = "Button"

export { Button, buttonVariants }


---

// FILE: src\shared\ui\card.tsx

import * as React from "react"

import { cn } from "../utils/utils"

function Card({ className, ...props }: React.ComponentProps<"div">): React.JSX.Element {
  return (
    <div
      data-slot="card"
      className={cn(
        "bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",
        className
      )}
      {...props}
    />
  )
}

function CardHeader({ className, ...props }: React.ComponentProps<"div">): React.JSX.Element {
  return (
    <div
      data-slot="card-header"
      className={cn(
        "@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",
        className
      )}
      {...props}
    />
  )
}

function CardTitle({ className, ...props }: React.ComponentProps<"div">): React.JSX.Element {
  return (
    <div
      data-slot="card-title"
      className={cn("leading-none font-semibold", className)}
      {...props}
    />
  )
}

function CardDescription({ className, ...props }: React.ComponentProps<"div">): React.JSX.Element {
  return (
    <div
      data-slot="card-description"
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  )
}

function CardAction({ className, ...props }: React.ComponentProps<"div">): React.JSX.Element {
  return (
    <div
      data-slot="card-action"
      className={cn(
        "col-start-2 row-span-2 row-start-1 self-start justify-self-end",
        className
      )}
      {...props}
    />
  )
}

function CardContent({ className, ...props }: React.ComponentProps<"div">): React.JSX.Element {
  return (
    <div
      data-slot="card-content"
      className={cn("px-6", className)}
      {...props}
    />
  )
}

function CardFooter({ className, ...props }: React.ComponentProps<"div">): React.JSX.Element {
  return (
    <div
      data-slot="card-footer"
      className={cn("flex items-center px-6 [.border-t]:pt-6", className)}
      {...props}
    />
  )
}

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardAction,
  CardDescription,
  CardContent,
}


---

// FILE: src\shared\ui\checkbox.tsx

import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { CheckIcon } from "lucide-react"
import * as React from "react"

import { cn } from "../utils/utils"

function Checkbox({
  className,
  ...props
}: React.ComponentProps<typeof CheckboxPrimitive.Root>): React.JSX.Element {
  return (
    <CheckboxPrimitive.Root
      data-slot="checkbox"
      className={cn(
        "peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        data-slot="checkbox-indicator"
        className="flex items-center justify-center text-current transition-none"
      >
        <CheckIcon className="size-3.5" />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  )
}

export { Checkbox }


---

// FILE: src\shared\ui\dialog.tsx

import * as DialogPrimitive from "@radix-ui/react-dialog"
import { XIcon } from "lucide-react"
import * as React from "react"

import { cn } from "../utils/utils"

function Dialog({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Root>): React.JSX.Element {
  return <DialogPrimitive.Root data-slot="dialog" {...props} />
}

function DialogTrigger({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Trigger>): React.JSX.Element {
  return <DialogPrimitive.Trigger data-slot="dialog-trigger" {...props} />
}

function DialogPortal({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Portal>): React.JSX.Element {
  return <DialogPrimitive.Portal data-slot="dialog-portal" {...props} />
}

function DialogClose({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Close>): React.JSX.Element {
  return <DialogPrimitive.Close data-slot="dialog-close" {...props} />
}

function DialogOverlay({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Overlay>): React.JSX.Element {
  return (
    <DialogPrimitive.Overlay
      data-slot="dialog-overlay"
      className={cn(
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",
        className
      )}
      {...props}
    />
  )
}

function DialogContent({
  className,
  children,
  showCloseButton = true,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Content> & {
  showCloseButton?: boolean
}): React.JSX.Element {
  return (
    <DialogPortal data-slot="dialog-portal">
      <DialogOverlay />
      <DialogPrimitive.Content
        data-slot="dialog-content"
        className={cn(
          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",
          className
        )}
        {...props}
      >
        {children}
        {showCloseButton && (
          <DialogPrimitive.Close
            data-slot="dialog-close"
            className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4"
          >
            <XIcon />
            <span className="sr-only">Close</span>
          </DialogPrimitive.Close>
        )}
      </DialogPrimitive.Content>
    </DialogPortal>
  )
}

function DialogHeader({ className, ...props }: React.ComponentProps<"div">): React.JSX.Element {
  return (
    <div
      data-slot="dialog-header"
      className={cn("flex flex-col gap-2 text-center sm:text-left", className)}
      {...props}
    />
  )
}

function DialogFooter({ className, ...props }: React.ComponentProps<"div">): React.JSX.Element {
  return (
    <div
      data-slot="dialog-footer"
      className={cn(
        "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",
        className
      )}
      {...props}
    />
  )
}

function DialogTitle({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Title>): React.JSX.Element {
  return (
    <DialogPrimitive.Title
      data-slot="dialog-title"
      className={cn("text-lg leading-none font-semibold", className)}
      {...props}
    />
  )
}

function DialogDescription({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Description>): React.JSX.Element {
  return (
    <DialogPrimitive.Description
      data-slot="dialog-description"
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  )
}

export {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
}


---

// FILE: src\shared\ui\index.ts

/**
 * @file Entry point for UI components
 */
// ShadCN UI base components
export * from './button';
export * from './input';
export * from './label';
export * from './card';
export * from './badge';
export { Avatar, AvatarImage, AvatarFallback } from './avatar';
export { Skeleton } from './skeleton';
export * from './select';
export * from './checkbox';
export * from './sonner';

// Utility functions
export { cn } from '../utils/utils';

---

// FILE: src\shared\ui\input.tsx

import * as React from "react"

import { cn } from "../utils/utils"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        data-slot="input"
        className={cn(
          "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
          "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }


---

// FILE: src\shared\ui\label.tsx

import * as LabelPrimitive from "@radix-ui/react-label"
import * as React from "react"

import { cn } from "../utils/utils"

function Label({
  className,
  ...props
}: React.ComponentProps<typeof LabelPrimitive.Root>): React.JSX.Element {
  return (
    <LabelPrimitive.Root
      data-slot="label"
      className={cn(
        "flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",
        className
      )}
      {...props}
    />
  )
}

export { Label }


---

// FILE: src\shared\ui\progress.tsx

import * as ProgressPrimitive from "@radix-ui/react-progress"
import * as React from "react"

import { cn } from "../utils/utils"

function Progress({
  className,
  value,
  ...props
}: React.ComponentProps<typeof ProgressPrimitive.Root>): React.JSX.Element {
  return (
    <ProgressPrimitive.Root
      data-slot="progress"
      className={cn(
        "bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",
        className
      )}
      {...props}
    >
      <ProgressPrimitive.Indicator
        data-slot="progress-indicator"
        className="bg-primary h-full w-full flex-1 transition-all"
        style={{ transform: `translateX(-${100 - (value ?? 0)}%)` }}
      />
    </ProgressPrimitive.Root>
  )
}

export { Progress }


---

// FILE: src\shared\ui\select.tsx

"use client"

import * as SelectPrimitive from "@radix-ui/react-select"
import { CheckIcon, ChevronDownIcon, ChevronUpIcon } from "lucide-react"
import * as React from "react"

import { cn } from "../utils/utils"

function Select({
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Root>): React.JSX.Element {
  return <SelectPrimitive.Root data-slot="select" {...props} />
}

function SelectGroup({
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Group>): React.JSX.Element {
  return <SelectPrimitive.Group data-slot="select-group" {...props} />
}

function SelectValue({
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Value>): React.JSX.Element {
  return <SelectPrimitive.Value data-slot="select-value" {...props} />
}

function SelectTrigger({
  className,
  size = "default",
  children,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {
  size?: "sm" | "default"
}): React.JSX.Element {
  return (
    <SelectPrimitive.Trigger
      data-slot="select-trigger"
      data-size={size}
      className={cn(
        "border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      )}
      {...props}
    >
      {children}
      <SelectPrimitive.Icon asChild>
        <ChevronDownIcon className="size-4 opacity-50" />
      </SelectPrimitive.Icon>
    </SelectPrimitive.Trigger>
  )
}

function SelectContent({
  className,
  children,
  position = "popper",
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Content>): React.JSX.Element {
  return (
    <SelectPrimitive.Portal>
      <SelectPrimitive.Content
        data-slot="select-content"
        className={cn(
          "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",
          position === "popper" &&
            "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
          className
        )}
        position={position}
        {...props}
      >
        <SelectScrollUpButton />
        <SelectPrimitive.Viewport
          className={cn(
            "p-1",
            position === "popper" &&
              "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"
          )}
        >
          {children}
        </SelectPrimitive.Viewport>
        <SelectScrollDownButton />
      </SelectPrimitive.Content>
    </SelectPrimitive.Portal>
  )
}

function SelectLabel({
  className,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Label>): React.JSX.Element {
  return (
    <SelectPrimitive.Label
      data-slot="select-label"
      className={cn("text-muted-foreground px-2 py-1.5 text-xs", className)}
      {...props}
    />
  )
}

function SelectItem({
  className,
  children,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Item>): React.JSX.Element {
  return (
    <SelectPrimitive.Item
      data-slot="select-item"
      className={cn(
        "focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",
        className
      )}
      {...props}
    >
      <span className="absolute right-2 flex size-3.5 items-center justify-center">
        <SelectPrimitive.ItemIndicator>
          <CheckIcon className="size-4" />
        </SelectPrimitive.ItemIndicator>
      </span>
      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
    </SelectPrimitive.Item>
  )
}

function SelectSeparator({
  className,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Separator>): React.JSX.Element {
  return (
    <SelectPrimitive.Separator
      data-slot="select-separator"
      className={cn("bg-border pointer-events-none -mx-1 my-1 h-px", className)}
      {...props}
    />
  )
}

function SelectScrollUpButton({
  className,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>): React.JSX.Element {
  return (
    <SelectPrimitive.ScrollUpButton
      data-slot="select-scroll-up-button"
      className={cn(
        "flex cursor-default items-center justify-center py-1",
        className
      )}
      {...props}
    >
      <ChevronUpIcon className="size-4" />
    </SelectPrimitive.ScrollUpButton>
  )
}

function SelectScrollDownButton({
  className,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>): React.JSX.Element {
  return (
    <SelectPrimitive.ScrollDownButton
      data-slot="select-scroll-down-button"
      className={cn(
        "flex cursor-default items-center justify-center py-1",
        className
      )}
      {...props}
    >
      <ChevronDownIcon className="size-4" />
    </SelectPrimitive.ScrollDownButton>
  )
}

export {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectScrollDownButton,
  SelectScrollUpButton,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
}


---

// FILE: src\shared\ui\skeleton.tsx

/**
 * @file Skeleton loading component using ShadCN UI
 */
import * as React from "react"

import { cn } from "../utils/utils"

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>): React.JSX.Element {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-muted", className)}
      {...props}
    />
  )
}

export { Skeleton }


---

// FILE: src\shared\ui\sonner.tsx

"use client"

import { useTheme } from "next-themes"
import React from "react"
import { Toaster as Sonner, type ToasterProps } from "sonner"

const Toaster = ({ ...props }: ToasterProps): React.JSX.Element => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      style={
        {
          "--normal-bg": "var(--popover)",
          "--normal-text": "var(--popover-foreground)",
          "--normal-border": "var(--border)",
        } as React.CSSProperties
      }
      {...props}
    />
  )
}

export { Toaster }


---

// FILE: src\shared\ui\switch.tsx

"use client"

import * as SwitchPrimitive from "@radix-ui/react-switch"
import * as React from "react"

import { cn } from "../utils/utils"

function Switch({
  className,
  ...props
}: React.ComponentProps<typeof SwitchPrimitive.Root>): React.JSX.Element {
  return (
    <SwitchPrimitive.Root
      data-slot="switch"
      className={cn(
        "peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",
        className
      )}
      {...props}
    >
      <SwitchPrimitive.Thumb
        data-slot="switch-thumb"
        className={cn(
          "pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"
        )}
      />
    </SwitchPrimitive.Root>
  )
}

export { Switch }


---

// FILE: src\shared\ui\tabs.tsx

import * as TabsPrimitive from "@radix-ui/react-tabs"
import * as React from "react"

import { cn } from "../utils/utils"

function Tabs({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Root>): React.JSX.Element {
  return (
    <TabsPrimitive.Root
      data-slot="tabs"
      className={cn("flex flex-col gap-2", className)}
      {...props}
    />
  )
}

function TabsList({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.List>): React.JSX.Element {
  return (
    <TabsPrimitive.List
      data-slot="tabs-list"
      className={cn(
        "bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",
        className
      )}
      {...props}
    />
  )
}

function TabsTrigger({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Trigger>): React.JSX.Element {
  return (
    <TabsPrimitive.Trigger
      data-slot="tabs-trigger"
      className={cn(
        "data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      )}
      {...props}
    />
  )
}

function TabsContent({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Content>): React.JSX.Element {
  return (
    <TabsPrimitive.Content
      data-slot="tabs-content"
      className={cn("flex-1 outline-none", className)}
      {...props}
    />
  )
}

export { Tabs, TabsList, TabsTrigger, TabsContent }


---

// FILE: src\shared\ui\theme-provider.tsx

import { ThemeProvider as NextThemesProvider, type ThemeProviderProps } from "next-themes"

export function ThemeProvider({ children, ...props }: ThemeProviderProps): React.JSX.Element {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>
}


---

// FILE: src\shared\ui\toggle-group.tsx

import * as ToggleGroupPrimitive from "@radix-ui/react-toggle-group"
import { type VariantProps } from "class-variance-authority"
import * as React from "react"

import { cn } from "../utils/utils"

import { toggleVariants } from "./toggle"

const ToggleGroupContext = React.createContext<
  VariantProps<typeof toggleVariants>
>({
  size: "default",
  variant: "default",
})

function ToggleGroup({
  className,
  variant,
  size,
  children,
  ...props
}: React.ComponentProps<typeof ToggleGroupPrimitive.Root> &
  VariantProps<typeof toggleVariants>): React.JSX.Element {
  return (
    <ToggleGroupPrimitive.Root
      data-slot="toggle-group"
      data-variant={variant}
      data-size={size}
      className={cn(
        "group/toggle-group flex w-fit items-center rounded-md data-[variant=outline]:shadow-xs",
        className
      )}
      {...props}
    >
      <ToggleGroupContext.Provider value={{ variant, size }}>
        {children}
      </ToggleGroupContext.Provider>
    </ToggleGroupPrimitive.Root>
  )
}

function ToggleGroupItem({
  className,
  children,
  variant,
  size,
  ...props
}: React.ComponentProps<typeof ToggleGroupPrimitive.Item> &
  VariantProps<typeof toggleVariants>): React.JSX.Element {
  const context = React.useContext(ToggleGroupContext)

  return (
    <ToggleGroupPrimitive.Item
      data-slot="toggle-group-item"
      data-variant={context.variant ?? variant}
      data-size={context.size ?? size}
      className={cn(
        toggleVariants({
          variant: context.variant ?? variant,
          size: context.size ?? size,
        }),
        "min-w-0 flex-1 shrink-0 rounded-none shadow-none first:rounded-l-md last:rounded-r-md focus:z-10 focus-visible:z-10 data-[variant=outline]:border-l-0 data-[variant=outline]:first:border-l",
        className
      )}
      {...props}
    >
      {children}
    </ToggleGroupPrimitive.Item>
  )
}

export { ToggleGroup, ToggleGroupItem }


---

// FILE: src\shared\ui\toggle.tsx

"use client"

import * as TogglePrimitive from "@radix-ui/react-toggle"
import { cva, type VariantProps } from "class-variance-authority"
import * as React from "react"

import { cn } from "../utils/utils"

const toggleVariants = cva(
  "inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap",
  {
    variants: {
      variant: {
        default: "bg-transparent",
        outline:
          "border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground",
      },
      size: {
        default: "h-9 px-2 min-w-9",
        sm: "h-8 px-1.5 min-w-8",
        lg: "h-10 px-2.5 min-w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

function Toggle({
  className,
  variant,
  size,
  ...props
}: React.ComponentProps<typeof TogglePrimitive.Root> &
  VariantProps<typeof toggleVariants>): React.JSX.Element {
  return (
    <TogglePrimitive.Root
      data-slot="toggle"
      className={cn(toggleVariants({ variant, size, className }))}
      {...props}
    />
  )
}

export { Toggle, toggleVariants }


---

// FILE: src\shared\ui\tooltip.tsx

"use client"

import * as TooltipPrimitive from "@radix-ui/react-tooltip"
import * as React from "react"

import { cn } from "../utils/utils"

const TooltipProvider = TooltipPrimitive.Provider

const TooltipRoot = TooltipPrimitive.Root

const TooltipTrigger = TooltipPrimitive.Trigger

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Content
    ref={ref}
    sideOffset={sideOffset}
    className={cn(
      "z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-xs text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      className
    )}
    {...props}
  />
))
TooltipContent.displayName = TooltipPrimitive.Content.displayName

/**
 * Tooltip component for displaying additional information on hover
 */
const Tooltip = ({ children, content, ...props }: {
  children: React.ReactNode;
  content: React.ReactNode;
  delayDuration?: number;
  side?: "top" | "right" | "bottom" | "left";
  align?: "start" | "center" | "end";
}): React.JSX.Element => (
  <TooltipProvider>
    <TooltipRoot delayDuration={props.delayDuration ?? 300}>
      <TooltipTrigger asChild>
        {children}
      </TooltipTrigger>
      <TooltipContent side={props.side} align={props.align}>
        {content}
      </TooltipContent>
    </TooltipRoot>
  </TooltipProvider>
)

export { Tooltip, TooltipProvider, TooltipRoot, TooltipTrigger, TooltipContent }


---

// FILE: src\shared\ui\top-bar-account-section.tsx

/**
 * @file Account section component for the top bar
 */
import { Settings, ChevronLeft, ChevronRight, RefreshCw, Users } from 'lucide-react';
import React from 'react';

import { cn } from '../utils/utils';

import { Button } from './button';

interface TopBarAccountSectionProps {
  accountCount: number;
  isCollapsed: boolean;
  isConnectingAll: boolean;
  isSettingsOpen?: boolean;
  onSettings: () => void;
  onToggleCollapse: () => void;
  onConnectAll: () => void;
}

/**
 * Account section for the top bar showing account count and actions
 */
export const TopBarAccountSection: React.FC<TopBarAccountSectionProps> = ({
  accountCount,
  isCollapsed,
  isConnectingAll,
  isSettingsOpen = false,
  onSettings,
  onToggleCollapse,
  onConnectAll
}) => {
  return (
    <div className="flex items-center gap-4">
      {/* Connect All + Account Status */}
      <div className="flex items-center gap-2">
        {/* Connect All Action - только иконка */}
        {accountCount > 0 && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onConnectAll}
            disabled={isConnectingAll}
            className={cn("h-8 w-8", isConnectingAll && "animate-pulse")}
            title={isConnectingAll ? "Connecting to all accounts..." : "Connect to all accounts"}
          >
            <RefreshCw size={16} className={isConnectingAll ? "animate-spin" : ""} />
          </Button>
        )}

        {/* Account Count */}
        <div className="flex items-center gap-1.5 text-sm text-muted-foreground">
          <Users size={20} className="text-primary" />
          <span className="font-medium">{accountCount}</span>
          <span>{accountCount === 1 ? 'Account' : 'Accounts'}</span>
        </div>
      </div>

      {/* Settings Button */}
      <Button
        variant={isSettingsOpen ? "default" : "ghost"}
        size="icon"
        onClick={onSettings}
        className="h-8 w-8"
        title="Settings"
      >
        <Settings size={16} />
      </Button>

      {/* Collapse/Expand Button */}
      <Button
        variant="ghost"
        size="icon"
        onClick={onToggleCollapse}
        className="h-8 w-8"
        title={isCollapsed ? "Expand account panel" : "Collapse account panel"}
      >
        {isCollapsed ? <ChevronLeft size={16} /> : <ChevronRight size={16} />}
      </Button>
    </div>
  );
};


---

// FILE: src\shared\ui\top-bar.tsx

/**
 * @file Unified top bar component for consistent application header
 */
import React from 'react';

import { cn } from '../utils/utils';

interface TopBarProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Main top bar container that spans the full width of the application
 */
export const TopBar: React.FC<TopBarProps> = ({ children, className }) => {
  return (
    <header 
      className={cn(
        // Layout
        "flex items-center justify-between",
        "w-full h-14 px-4",
        "border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
        "sticky top-0 z-50",
        
        // Custom classes
        className
      )}
    >
      {children}
    </header>
  );
};

interface TopBarSectionProps {
  children: React.ReactNode;
  className?: string;
  side?: 'left' | 'center' | 'right';
  width?: string;
}

/**
 * Section container for organizing top bar content
 */
export const TopBarSection: React.FC<TopBarSectionProps> = ({ children, className, side, width }) => {
  const sideStyles = {
    left: "justify-start",
    center: "justify-center",
    right: "justify-end"
  };

  return (
    <div
      className={cn(
        "flex items-center gap-3",
        side && sideStyles[side],
        className
      )}
      style={width !== null && width !== undefined && width !== '' ? { width } : undefined}
    >
      {children}
    </div>
  );
};

interface TopBarTitleProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Title component for the top bar
 */
export const TopBarTitle: React.FC<TopBarTitleProps> = ({ children, className, size = 'lg' }) => {
  const sizeStyles = {
    sm: "text-sm font-medium",
    md: "text-base font-semibold",
    lg: "text-lg font-semibold"
  };

  return (
    <h1 className={cn(sizeStyles[size], "text-foreground", className)}>
      {children}
    </h1>
  );
};

interface TopBarSearchProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

/**
 * Search input component for the top bar
 */
export const TopBarSearch: React.FC<TopBarSearchProps> = ({ 
  value, 
  onChange, 
  placeholder = "Search...", 
  className 
}) => {
  return (
    <input
      type="text"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      className={cn(
        "flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
    />
  );
};


---

// FILE: src\shared\utils\security.ts

/**
 * @file Security utilities for sanitizing and validating user input
 */

/**
 * Sanitizes HTML content to prevent XSS attacks
 * Removes dangerous tags and attributes while preserving safe formatting
 */
export const sanitizeHtml = (html: string): string => {
  // Create a temporary DOM element to parse HTML
  const temp = document.createElement('div');
  temp.innerHTML = html;

  // List of allowed tags
  const allowedTags = [
    'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'span', 'div',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li',
    'blockquote', 'pre', 'code',
    'a', 'img'
  ];

  // List of allowed attributes for specific tags
  const allowedAttributes: Record<string, string[]> = {
    'a': ['href', 'title'],
    'img': ['src', 'alt', 'title', 'width', 'height'],
    'span': ['style'],
    'div': ['style'],
    'p': ['style']
  };

  // TODO: Implement CSS property filtering for enhanced security

  const sanitizeElement = (element: Element): void => {
    const tagName = element.tagName.toLowerCase();

    // Remove disallowed tags
    if (!allowedTags.includes(tagName)) {
      element.remove();
      return;
    }

    // Remove disallowed attributes
    const allowedAttrs = allowedAttributes[tagName] ?? [];
    const attributes = Array.from(element.attributes);
    
    attributes.forEach(attr => {
      if (!allowedAttrs.includes(attr.name)) {
        element.removeAttribute(attr.name);
      } else if (attr.name === 'href') {
        // Sanitize URLs
        const url = attr.value;
        if (!isValidUrl(url)) {
          element.removeAttribute('href');
        }
      } else if (attr.name === 'src') {
        // Allow data URLs, HTTPS URLs, HTTP URLs, and CID URLs for images
        const url = attr.value;
        if (!url.startsWith('data:') &&
            !url.startsWith('https://') &&
            !url.startsWith('http://') &&
            !url.startsWith('cid:')) {
          // For broken images, replace with a placeholder
          element.setAttribute('src', 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjMzc0MTUxIiBzdHJva2U9IiM2Mzc0OEIiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNOSA5SDE1VjE1SDlWOVoiIGZpbGw9IiM2Mzc0OEIiLz4KPC9zdmc+');
          element.setAttribute('alt', 'Image could not be loaded');
        }
      } else if (attr.name === 'style') {
        // Sanitize inline styles
        const sanitizedStyle = sanitizeCss(attr.value);
        if (sanitizedStyle) {
          element.setAttribute('style', sanitizedStyle);
        } else {
          element.removeAttribute('style');
        }
      }
    });

    // Recursively sanitize child elements
    Array.from(element.children).forEach(child => {
      sanitizeElement(child);
    });
  };

  // Sanitize all elements
  Array.from(temp.children).forEach(child => {
    sanitizeElement(child);
  });

  return temp.innerHTML;
};

/**
 * Sanitizes CSS properties to prevent CSS injection
 */
const sanitizeCss = (css: string): string => {
  const allowedCssProperties = [
    'color', 'background-color', 'font-size', 'font-weight', 'font-style',
    'text-align', 'text-decoration', 'margin', 'padding', 'border'
  ];

  // Parse CSS properties
  const properties = css.split(';').map(prop => prop.trim()).filter(Boolean);
  const sanitizedProperties: string[] = [];

  properties.forEach(property => {
    const [name, value] = property.split(':').map(part => part.trim());
    
    if (name && value && allowedCssProperties.includes(name.toLowerCase())) {
      // Additional validation for specific properties
      if (name.toLowerCase() === 'color' || name.toLowerCase() === 'background-color') {
        if (isValidColor(value)) {
          sanitizedProperties.push(`${name}: ${value}`);
        }
      } else if (name.toLowerCase().includes('font-size')) {
        if (isValidFontSize(value)) {
          sanitizedProperties.push(`${name}: ${value}`);
        }
      } else {
        // Basic validation - no javascript: or expression() calls
        if (!value.toLowerCase().includes('javascript:') && 
            !value.toLowerCase().includes('expression(') &&
            !value.toLowerCase().includes('url(')) {
          sanitizedProperties.push(`${name}: ${value}`);
        }
      }
    }
  });

  return sanitizedProperties.join('; ');
};

/**
 * Validates if a URL is safe
 */
const isValidUrl = (url: string): boolean => {
  try {
    const parsed = new URL(url);
    // Only allow HTTP, HTTPS, and mailto protocols
    return ['http:', 'https:', 'mailto:'].includes(parsed.protocol);
  } catch {
    return false;
  }
};

/**
 * Validates if a color value is safe
 */
const isValidColor = (color: string): boolean => {
  // Allow hex colors, rgb/rgba, hsl/hsla, and named colors
  const colorRegex = /^(#[0-9a-f]{3,8}|rgb\([^)]+\)|rgba\([^)]+\)|hsl\([^)]+\)|hsla\([^)]+\)|[a-z]+)$/i;
  return colorRegex.test(color.trim());
};

/**
 * Validates if a font size value is safe
 */
const isValidFontSize = (size: string): boolean => {
  // Allow px, em, rem, %, pt units
  const sizeRegex = /^\d+(\.\d+)?(px|em|rem|%|pt)$/i;
  return sizeRegex.test(size.trim());
};

/**
 * Escapes HTML entities to prevent XSS
 */
export const escapeHtml = (text: string): string => {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};

/**
 * Validates email address format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validates if a string contains only safe characters for file names
 */
export const isSafeFileName = (fileName: string): boolean => {
  // Allow alphanumeric, dots, hyphens, underscores, and spaces
  const safeFileNameRegex = /^[a-zA-Z0-9.\-_ ]+$/;
  return safeFileNameRegex.test(fileName) && 
         !fileName.includes('..') && 
         fileName.length > 0 && 
         fileName.length <= 255;
};

/**
 * Removes potentially dangerous characters from user input
 */
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

/**
 * Rate limiting utility for preventing abuse
 */
export class RateLimiter {
  private readonly attempts: Map<string, number[]> = new Map();

  constructor(
    private readonly maxAttempts: number = 5,
    private readonly windowMs: number = 15 * 60 * 1000 // 15 minutes
  ) {}

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(identifier) ?? [];
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(time => now - time < this.windowMs);
    
    if (validAttempts.length >= this.maxAttempts) {
      return false;
    }

    // Add current attempt
    validAttempts.push(now);
    this.attempts.set(identifier, validAttempts);
    
    return true;
  }

  reset(identifier: string): void {
    this.attempts.delete(identifier);
  }
}

/**
 * Content Security Policy helper
 */
export const getCSPDirectives = (): string => {
  return [
    "default-src 'self'",
    "script-src 'self'",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: http:",
    "connect-src 'self'",
    "frame-src 'none'",
    "object-src 'none'",
    "base-uri 'self'"
  ].join('; ');
};


---

// FILE: src\shared\utils\utils.ts

/**
 * @file Utility functions for ShadCN UI components and general use
 */
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Combines class names using clsx and tailwind-merge for optimal Tailwind CSS class handling
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs))
}


---

// FILE: tsconfig.json

{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "node",
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "strict": true,
    "jsx": "react-jsx",
    "resolveJsonModule": true,
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "src/*"
      ]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "forge.config.ts",
    "forge.env.d.ts"
  ]
}
