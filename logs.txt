7:55:28
Initializing application...

07:55:28
Loaded 0 accounts

07:55:28
Application initialized successfully

07:55:28
Renderer process is ready and listening for events.

07:55:28
User configuration saved successfully

07:55:31
Credentials detected in clipboard, pre-filling form.

07:55:31
No saved config for vargosmail.com. Running auto-discovery...

07:55:31
Starting email discovery for domain: vargosmail.com

07:55:31
Normalized domain: vargosmail.com

07:55:31
Final domain for discovery: vargosmail.com

07:55:31
[Discovery/S1-Providers] Checking known providers for vargosmail.com

07:55:31
[Discovery/S1-Providers] No known provider found for vargosmail.com

07:55:31
[Discovery/S2-DNS] Starting DNS discovery for vargosmail.com

07:55:31
[Discovery/S2-DNS] Will test hosts: imap.vargosmail.com, mail.vargosmail.com, smtp.vargosmail.com, pop.vargosmail.com, pop3.vargosmail.com, mx.vargosmail.com, email.vargosmail.com, mailserver.vargosmail.com, vargosmail.com

07:55:31
[Discovery/S3-Exchange] Starting Exchange Autodiscover for vargosmail.com

07:55:31
Running 3 discovery strategies for vargosmail.com

07:55:31
Strategy 1 completed for vargosmail.com: FAILED

07:55:32
No saved config for vargosmail.com. Running auto-discovery...

07:55:32
Starting email discovery for domain: vargosmail.com

07:55:32
Normalized domain: vargosmail.com

07:55:32
Final domain for discovery: vargosmail.com

07:55:32
[Discovery/S1-Providers] Checking known providers for vargosmail.com

07:55:32
[Discovery/S1-Providers] No known provider found for vargosmail.com

07:55:32
[Discovery/S2-DNS] Starting DNS discovery for vargosmail.com

07:55:32
[Discovery/S2-DNS] Will test hosts: imap.vargosmail.com, mail.vargosmail.com, smtp.vargosmail.com, pop.vargosmail.com, pop3.vargosmail.com, mx.vargosmail.com, email.vargosmail.com, mailserver.vargosmail.com, vargosmail.com

07:55:32
[Discovery/S3-Exchange] Starting Exchange Autodiscover for vargosmail.com

07:55:32
Running 3 discovery strategies for vargosmail.com

07:55:32
Strategy 1 completed for vargosmail.com: FAILED

07:55:37
User configuration saved successfully

07:55:37
User configuration saved successfully

07:55:56
Credentials detected in clipboard, pre-filling form.

07:55:56
No saved config for vargosmail.com. Running auto-discovery...

07:55:56
Starting email discovery for domain: vargosmail.com

07:55:56
Normalized domain: vargosmail.com

07:55:56
Final domain for discovery: vargosmail.com

07:55:56
[Discovery/S1-Providers] Checking known providers for vargosmail.com

07:55:56
[Discovery/S1-Providers] No known provider found for vargosmail.com

07:55:56
[Discovery/S2-DNS] Starting DNS discovery for vargosmail.com

07:55:56
[Discovery/S2-DNS] Will test hosts: imap.vargosmail.com, mail.vargosmail.com, smtp.vargosmail.com, pop.vargosmail.com, pop3.vargosmail.com, mx.vargosmail.com, email.vargosmail.com, mailserver.vargosmail.com, vargosmail.com

07:55:56
[Discovery/S3-Exchange] Starting Exchange Autodiscover for vargosmail.com

07:55:56
Running 3 discovery strategies for vargosmail.com

07:55:56
Strategy 1 completed for vargosmail.com: FAILED

07:55:57
No saved config for vargosmail.com. Running auto-discovery...

07:55:57
Starting email discovery for domain: vargosmail.com

07:55:57
Normalized domain: vargosmail.com

07:55:57
Final domain for discovery: vargosmail.com

07:55:57
[Discovery/S1-Providers] Checking known providers for vargosmail.com

07:55:57
[Discovery/S1-Providers] No known provider found for vargosmail.com

07:55:57
[Discovery/S2-DNS] Starting DNS discovery for vargosmail.com

07:55:57
[Discovery/S2-DNS] Will test hosts: imap.vargosmail.com, mail.vargosmail.com, smtp.vargosmail.com, pop.vargosmail.com, pop3.vargosmail.com, mx.vargosmail.com, email.vargosmail.com, mailserver.vargosmail.com, vargosmail.com

07:55:57
[Discovery/S3-Exchange] Starting Exchange Autodiscover for vargosmail.com

07:55:57
Running 3 discovery strategies for vargosmail.com

07:55:57
Strategy 1 completed for vargosmail.com: FAILED

07:56:15
[Discovery/S2-DNS-SRV] SRV lookup for _imaps._tcp.vargosmail.com failed: querySrv ETIMEOUT _imaps._tcp.vargosmail.com

07:56:21
[Discovery/S2-DNS-SRV] SRV lookup for _submission._tcp.vargosmail.com failed: querySrv ETIMEOUT _submission._tcp.vargosmail.com

07:56:21
[Discovery/S2-DNS-SRV] SRV lookup for _pop3s._tcp.vargosmail.com failed: querySrv ETIMEOUT _pop3s._tcp.vargosmail.com

07:56:22
[Discovery/S2-DNS-SRV] SRV lookup for _pop3._tcp.vargosmail.com failed: querySrv ETIMEOUT _pop3._tcp.vargosmail.com

07:56:22
[Discovery/S2-DNS-SRV] SRV lookup for _imap._tcp.vargosmail.com failed: querySrv ETIMEOUT _imap._tcp.vargosmail.com

07:56:22
[Discovery/S2-DNS-SRV] SRV lookup for _imap._tcp.vargosmail.com failed: querySrv ETIMEOUT _imap._tcp.vargosmail.com

07:56:23
[Discovery/S2-DNS-SRV] SRV lookup for _pop3s._tcp.vargosmail.com failed: querySrv ETIMEOUT _pop3s._tcp.vargosmail.com

07:56:23
[Discovery/S3-Exchange] SRV lookup failed: querySrv ETIMEOUT _autodiscover._tcp.vargosmail.com

07:56:23
[Discovery/S3-Exchange] Trying URL: https://autodiscover.vargosmail.com/autodiscover/autodiscover.xml

07:56:23
[Discovery/S3-Exchange] Trying URL: https://vargosmail.com/autodiscover/autodiscover.xml

07:56:23
[Discovery/S3-Exchange] Request to https://autodiscover.vargosmail.com/autodiscover/autodiscover.xml failed: fetch failed

07:56:23
[Discovery/S3-Exchange] Request to https://vargosmail.com/autodiscover/autodiscover.xml failed: fetch failed

07:56:23
[Discovery/S3-Exchange] No working Exchange configuration found for vargosmail.com

07:56:23
Strategy 3 completed for vargosmail.com: FAILED

07:56:25
[Discovery/S2-DNS-SRV] SRV lookup for _submission._tcp.vargosmail.com failed: querySrv ETIMEOUT _submission._tcp.vargosmail.com

07:56:26
[Discovery/S3-Exchange] SRV lookup failed: querySrv ETIMEOUT _autodiscover._tcp.vargosmail.com

07:56:26
[Discovery/S3-Exchange] Trying URL: https://autodiscover.vargosmail.com/autodiscover/autodiscover.xml

07:56:26
[Discovery/S3-Exchange] Trying URL: https://vargosmail.com/autodiscover/autodiscover.xml

07:56:26
[Discovery/S3-Exchange] Request to https://autodiscover.vargosmail.com/autodiscover/autodiscover.xml failed: fetch failed

07:56:26
[Discovery/S3-Exchange] Request to https://vargosmail.com/autodiscover/autodiscover.xml failed: fetch failed

07:56:26
[Discovery/S3-Exchange] No working Exchange configuration found for vargosmail.com

07:56:26
Strategy 3 completed for vargosmail.com: FAILED

07:56:29
[Discovery/S2-DNS-SRV] SRV lookup for _pop3._tcp.vargosmail.com failed: querySrv ETIMEOUT _pop3._tcp.vargosmail.com

07:56:29
[Discovery/S2-DNS-SRV] SRV lookup for _imaps._tcp.vargosmail.com failed: querySrv ETIMEOUT _imaps._tcp.vargosmail.com

07:56:29
[Discovery/S2-DNS] SRV lookup failed, falling back to DNS guessing for vargosmail.com

07:56:29
[Discovery/S2-DNS-MX] Looking up MX records for vargosmail.com

07:56:29
[Discovery/S2-DNS] SRV lookup failed, falling back to DNS guessing for vargosmail.com

07:56:29
[Discovery/S2-DNS-MX] Looking up MX records for vargosmail.com

07:56:37
[Discovery/S2-DNS-SRV] SRV lookup for _submission._tcp.vargosmail.com failed: querySrv ETIMEOUT _submission._tcp.vargosmail.com

07:56:39
[Discovery/S2-DNS-MX] Failed to resolve MX records for vargosmail.com: Error: MX lookup timeout

07:56:39
[Discovery/S2-DNS-Guess] Testing 17 hostnames: imap.vargosmail.com, mail.vargosmail.com, smtp.vargosmail.com, pop.vargosmail.com, pop3.vargosmail.com, mx.vargosmail.com, email.vargosmail.com, mailserver.vargosmail.com, vargosmail.com, webmail.vargosmail.com, server.vargosmail.com, mail1.vargosmail.com, mail2.vargosmail.com, imap1.vargosmail.com, imap2.vargosmail.com, secure.vargosmail.com, ssl.vargosmail.com

07:56:39
[Discovery/S2-DNS-Guess] Starting parallel testing of 17 hosts for IMAP/POP3/SMTP

07:56:39
[Discovery/S2-DNS-Guess] Testing IMAP servers first (priority)

07:56:39
[Discovery/S2-DNS-Guess] Testing IMAP host: imap.vargosmail.com

07:56:39
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:993 (secure: true)

07:56:39
[Discovery/S2-DNS-MX] Failed to resolve MX records for vargosmail.com: Error: MX lookup timeout

07:56:39
[Discovery/S2-DNS-Guess] Testing 17 hostnames: imap.vargosmail.com, mail.vargosmail.com, smtp.vargosmail.com, pop.vargosmail.com, pop3.vargosmail.com, mx.vargosmail.com, email.vargosmail.com, mailserver.vargosmail.com, vargosmail.com, webmail.vargosmail.com, server.vargosmail.com, mail1.vargosmail.com, mail2.vargosmail.com, imap1.vargosmail.com, imap2.vargosmail.com, secure.vargosmail.com, ssl.vargosmail.com

07:56:39
[Discovery/S2-DNS-Guess] Starting parallel testing of 17 hosts for IMAP/POP3/SMTP

07:56:39
[Discovery/S2-DNS-Guess] Testing IMAP servers first (priority)

07:56:39
[Discovery/S2-DNS-Guess] Testing IMAP host: imap.vargosmail.com

07:56:39
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:993 (secure: true)

07:56:39
Connection failed to imap.vargosmail.com:993 - connect ECONNREFUSED *************:993

07:56:39
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:143 (secure: false)

07:56:39
Connection failed to imap.vargosmail.com:993 - connect ECONNREFUSED *************:993

07:56:39
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:143 (secure: false)

07:56:39
Connection failed to imap.vargosmail.com:143 - connect ECONNREFUSED *************:143

07:56:39
[Discovery/S2-DNS-Guess] No working IMAP server found for host: imap.vargosmail.com

07:56:39
[Discovery/S2-DNS-Guess] Testing IMAP host: mail.vargosmail.com

07:56:39
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:993 (secure: true)

07:56:39
Connection failed to imap.vargosmail.com:143 - connect ECONNREFUSED *************:143

07:56:39
[Discovery/S2-DNS-Guess] No working IMAP server found for host: imap.vargosmail.com

07:56:39
[Discovery/S2-DNS-Guess] Testing IMAP host: mail.vargosmail.com

07:56:39
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:993 (secure: true)

07:56:40
Host mail.vargosmail.com:993 redirects to real hostname: *.firstmail.ltd

07:56:40
[Discovery/S2-DNS-Guess] Found working IMAP server: *.firstmail.ltd:993

07:56:40
[Discovery/S2-DNS-Guess] Using real hostname from TLS certificate: *.firstmail.ltd

07:56:40
[Discovery/S2-DNS-Guess] Found IMAP server, stopping further IMAP tests

07:56:40
[Discovery/S2-DNS-Guess] Testing SMTP servers

07:56:40
[Discovery/S2-DNS-Guess] Testing SMTP host: imap.vargosmail.com

07:56:40
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:587 (secure: true)

07:56:40
Host mail.vargosmail.com:993 redirects to real hostname: *.firstmail.ltd

07:56:40
[Discovery/S2-DNS-Guess] Found working IMAP server: *.firstmail.ltd:993

07:56:40
[Discovery/S2-DNS-Guess] Using real hostname from TLS certificate: *.firstmail.ltd

07:56:40
[Discovery/S2-DNS-Guess] Found IMAP server, stopping further IMAP tests

07:56:40
[Discovery/S2-DNS-Guess] Testing SMTP servers

07:56:40
[Discovery/S2-DNS-Guess] Testing SMTP host: imap.vargosmail.com

07:56:40
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:587 (secure: true)

07:56:40
Connection failed to imap.vargosmail.com:587 - connect ECONNREFUSED *************:587

07:56:40
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:465 (secure: true)

07:56:40
Connection failed to imap.vargosmail.com:587 - connect ECONNREFUSED *************:587

07:56:40
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:465 (secure: true)

07:56:40
Connection failed to imap.vargosmail.com:465 - connect ECONNREFUSED *************:465

07:56:40
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:25 (secure: false)

07:56:40
Connection failed to imap.vargosmail.com:465 - connect ECONNREFUSED *************:465

07:56:40
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:25 (secure: false)

07:56:40
Connection failed to imap.vargosmail.com:25 - connect ECONNREFUSED *************:25

07:56:40
[Discovery/S2-DNS-Guess] No working SMTP server found for host: imap.vargosmail.com

07:56:40
[Discovery/S2-DNS-Guess] Testing SMTP host: mail.vargosmail.com

07:56:40
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:587 (secure: true)

07:56:40
Connection failed to imap.vargosmail.com:25 - connect ECONNREFUSED *************:25

07:56:40
[Discovery/S2-DNS-Guess] No working SMTP server found for host: imap.vargosmail.com

07:56:40
[Discovery/S2-DNS-Guess] Testing SMTP host: mail.vargosmail.com

07:56:40
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:587 (secure: true)

07:56:40
Connection failed to mail.vargosmail.com:587 - connect ETIMEDOUT *************:587

07:56:40
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:465 (secure: true)

07:56:40
Connection failed to mail.vargosmail.com:587 - connect ETIMEDOUT *************:587

07:56:40
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:465 (secure: true)

07:56:40
Connection failed to mail.vargosmail.com:465 - connect ETIMEDOUT *************:465

07:56:40
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:25 (secure: false)

07:56:41
Host mail.vargosmail.com:25 is reachable (secure: false)

07:56:41
[Discovery/S2-DNS-Guess] Found working SMTP server: mail.vargosmail.com:25

07:56:41
[Discovery/S2-DNS-Guess] Found SMTP server, stopping further SMTP tests

07:56:41
[Discovery/S2-DNS-Guess] Sequential testing completed for vargosmail.com

07:56:41
[Discovery/S2-DNS-Guess] DNS guessing completed for vargosmail.com. Found: YES

07:56:41
[Discovery/S2-DNS-Guess] IMAP: *.firstmail.ltd:993

07:56:41
[Discovery/S2-DNS-Guess] SMTP: mail.vargosmail.com:25

07:56:41
[Discovery/S2-DNS] Found config via DNS guessing for vargosmail.com

07:56:41
Strategy 2 completed for vargosmail.com: SUCCESS

07:56:41
Discovery successful for vargosmail.com

07:56:41
Successfully discovered config for vargosmail.com. Saving...

07:56:41
Connection failed to mail.vargosmail.com:465 - connect ETIMEDOUT *************:465

07:56:41
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:25 (secure: false)

07:56:41
Host mail.vargosmail.com:25 is reachable (secure: false)

07:56:41
[Discovery/S2-DNS-Guess] Found working SMTP server: mail.vargosmail.com:25

07:56:41
[Discovery/S2-DNS-Guess] Found SMTP server, stopping further SMTP tests

07:56:41
[Discovery/S2-DNS-Guess] Sequential testing completed for vargosmail.com

07:56:41
[Discovery/S2-DNS-Guess] DNS guessing completed for vargosmail.com. Found: YES

07:56:41
[Discovery/S2-DNS-Guess] IMAP: *.firstmail.ltd:993

07:56:41
[Discovery/S2-DNS-Guess] SMTP: mail.vargosmail.com:25

07:56:41
[Discovery/S2-DNS] Found config via DNS guessing for vargosmail.com

07:56:41
Strategy 2 completed for vargosmail.com: SUCCESS

07:56:41
Discovery successful for vargosmail.com

07:56:41
Successfully discovered config for vargosmail.com. Saving...

07:56:41
[Discovery/S2-DNS-SRV] SRV lookup for _imap._tcp.vargosmail.com failed: querySrv ETIMEOUT _imap._tcp.vargosmail.com

07:56:41
[Discovery/S2-DNS-SRV] SRV lookup for _imaps._tcp.vargosmail.com failed: querySrv ETIMEOUT _imaps._tcp.vargosmail.com

07:56:42
[Discovery/S2-DNS-SRV] SRV lookup for _imap._tcp.vargosmail.com failed: querySrv ETIMEOUT _imap._tcp.vargosmail.com

07:56:42
[Discovery/S2-DNS-SRV] SRV lookup for _pop3._tcp.vargosmail.com failed: querySrv ETIMEOUT _pop3._tcp.vargosmail.com

07:56:44
[Discovery/S2-DNS-SRV] SRV lookup for _pop3._tcp.vargosmail.com failed: querySrv ETIMEOUT _pop3._tcp.vargosmail.com

07:56:44
[Discovery/S3-Exchange] SRV lookup failed: querySrv ETIMEOUT _autodiscover._tcp.vargosmail.com

07:56:44
[Discovery/S3-Exchange] Trying URL: https://autodiscover.vargosmail.com/autodiscover/autodiscover.xml

07:56:44
[Discovery/S3-Exchange] Trying URL: https://vargosmail.com/autodiscover/autodiscover.xml

07:56:44
[Discovery/S3-Exchange] Request to https://autodiscover.vargosmail.com/autodiscover/autodiscover.xml failed: fetch failed

07:56:44
[Discovery/S3-Exchange] Request to https://vargosmail.com/autodiscover/autodiscover.xml failed: fetch failed

07:56:44
[Discovery/S3-Exchange] No working Exchange configuration found for vargosmail.com

07:56:44
Strategy 3 completed for vargosmail.com: FAILED

07:56:46
[Discovery/S2-DNS-SRV] SRV lookup for _submission._tcp.vargosmail.com failed: querySrv ETIMEOUT _submission._tcp.vargosmail.com

07:56:47
[Discovery/S2-DNS-SRV] SRV lookup for _pop3s._tcp.vargosmail.com failed: querySrv ETIMEOUT _pop3s._tcp.vargosmail.com

07:56:47
[Discovery/S2-DNS-SRV] SRV lookup for _pop3s._tcp.vargosmail.com failed: querySrv ETIMEOUT _pop3s._tcp.vargosmail.com

07:56:47
[Discovery/S2-DNS] SRV lookup failed, falling back to DNS guessing for vargosmail.com

07:56:47
[Discovery/S2-DNS-MX] Looking up MX records for vargosmail.com

07:56:49
[Discovery/S2-DNS-SRV] SRV lookup for _imaps._tcp.vargosmail.com failed: querySrv ETIMEOUT _imaps._tcp.vargosmail.com

07:56:49
[Discovery/S2-DNS] SRV lookup failed, falling back to DNS guessing for vargosmail.com

07:56:49
[Discovery/S2-DNS-MX] Looking up MX records for vargosmail.com

07:56:50
[Discovery/S3-Exchange] SRV lookup failed: querySrv ETIMEOUT _autodiscover._tcp.vargosmail.com

07:56:50
[Discovery/S3-Exchange] Trying URL: https://autodiscover.vargosmail.com/autodiscover/autodiscover.xml

07:56:50
[Discovery/S3-Exchange] Trying URL: https://vargosmail.com/autodiscover/autodiscover.xml

07:56:50
[Discovery/S3-Exchange] Request to https://autodiscover.vargosmail.com/autodiscover/autodiscover.xml failed: fetch failed

07:56:50
[Discovery/S3-Exchange] Request to https://vargosmail.com/autodiscover/autodiscover.xml failed: fetch failed

07:56:50
[Discovery/S3-Exchange] No working Exchange configuration found for vargosmail.com

07:56:51
Strategy 3 completed for vargosmail.com: FAILED

07:56:57
[Discovery/S2-DNS-MX] Failed to resolve MX records for vargosmail.com: Error: MX lookup timeout

07:56:57
[Discovery/S2-DNS-Guess] Testing 17 hostnames: imap.vargosmail.com, mail.vargosmail.com, smtp.vargosmail.com, pop.vargosmail.com, pop3.vargosmail.com, mx.vargosmail.com, email.vargosmail.com, mailserver.vargosmail.com, vargosmail.com, webmail.vargosmail.com, server.vargosmail.com, mail1.vargosmail.com, mail2.vargosmail.com, imap1.vargosmail.com, imap2.vargosmail.com, secure.vargosmail.com, ssl.vargosmail.com

07:56:57
[Discovery/S2-DNS-Guess] Starting parallel testing of 17 hosts for IMAP/POP3/SMTP

07:56:57
[Discovery/S2-DNS-Guess] Testing IMAP servers first (priority)

07:56:57
[Discovery/S2-DNS-Guess] Testing IMAP host: imap.vargosmail.com

07:56:57
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:993 (secure: true)

07:56:57
Connection failed to imap.vargosmail.com:993 - connect ECONNREFUSED *************:993

07:56:57
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:143 (secure: false)

07:56:57
Connection failed to imap.vargosmail.com:143 - connect ECONNREFUSED *************:143

07:56:57
[Discovery/S2-DNS-Guess] No working IMAP server found for host: imap.vargosmail.com

07:56:57
[Discovery/S2-DNS-Guess] Testing IMAP host: mail.vargosmail.com

07:56:57
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:993 (secure: true)

07:56:58
Host mail.vargosmail.com:993 redirects to real hostname: *.firstmail.ltd

07:56:58
[Discovery/S2-DNS-Guess] Found working IMAP server: *.firstmail.ltd:993

07:56:58
[Discovery/S2-DNS-Guess] Using real hostname from TLS certificate: *.firstmail.ltd

07:56:58
[Discovery/S2-DNS-Guess] Found IMAP server, stopping further IMAP tests

07:56:58
[Discovery/S2-DNS-Guess] Testing SMTP servers

07:56:58
[Discovery/S2-DNS-Guess] Testing SMTP host: imap.vargosmail.com

07:56:58
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:587 (secure: true)

07:56:58
Connection failed to imap.vargosmail.com:587 - connect ECONNREFUSED *************:587

07:56:58
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:465 (secure: true)

07:56:58
Connection failed to imap.vargosmail.com:465 - connect ECONNREFUSED *************:465

07:56:58
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:25 (secure: false)

07:56:58
Connection failed to imap.vargosmail.com:25 - connect ECONNREFUSED *************:25

07:56:58
[Discovery/S2-DNS-Guess] No working SMTP server found for host: imap.vargosmail.com

07:56:58
[Discovery/S2-DNS-Guess] Testing SMTP host: mail.vargosmail.com

07:56:58
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:587 (secure: true)

07:56:58
Connection failed to mail.vargosmail.com:587 - connect ETIMEDOUT *************:587

07:56:58
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:465 (secure: true)

07:56:58
Connection failed to mail.vargosmail.com:465 - connect ETIMEDOUT *************:465

07:56:59
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:25 (secure: false)

07:56:59
Host mail.vargosmail.com:25 is reachable (secure: false)

07:56:59
[Discovery/S2-DNS-Guess] Found working SMTP server: mail.vargosmail.com:25

07:56:59
[Discovery/S2-DNS-Guess] Found SMTP server, stopping further SMTP tests

07:56:59
[Discovery/S2-DNS-Guess] Sequential testing completed for vargosmail.com

07:56:59
[Discovery/S2-DNS-Guess] DNS guessing completed for vargosmail.com. Found: YES

07:56:59
[Discovery/S2-DNS-Guess] IMAP: *.firstmail.ltd:993

07:56:59
[Discovery/S2-DNS-Guess] SMTP: mail.vargosmail.com:25

07:56:59
[Discovery/S2-DNS] Found config via DNS guessing for vargosmail.com

07:56:59
Strategy 2 completed for vargosmail.com: SUCCESS

07:56:59
Discovery successful for vargosmail.com

07:56:59
Successfully discovered config for vargosmail.com. Saving...

07:56:59
[Discovery/S2-DNS-MX] Failed to resolve MX records for vargosmail.com: Error: MX lookup timeout

07:56:59
[Discovery/S2-DNS-Guess] Testing 17 hostnames: imap.vargosmail.com, mail.vargosmail.com, smtp.vargosmail.com, pop.vargosmail.com, pop3.vargosmail.com, mx.vargosmail.com, email.vargosmail.com, mailserver.vargosmail.com, vargosmail.com, webmail.vargosmail.com, server.vargosmail.com, mail1.vargosmail.com, mail2.vargosmail.com, imap1.vargosmail.com, imap2.vargosmail.com, secure.vargosmail.com, ssl.vargosmail.com

07:56:59
[Discovery/S2-DNS-Guess] Starting parallel testing of 17 hosts for IMAP/POP3/SMTP

07:56:59
[Discovery/S2-DNS-Guess] Testing IMAP servers first (priority)

07:56:59
[Discovery/S2-DNS-Guess] Testing IMAP host: imap.vargosmail.com

07:56:59
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:993 (secure: true)

07:56:59
Connection failed to imap.vargosmail.com:993 - connect ECONNREFUSED *************:993

07:56:59
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:143 (secure: false)

07:56:59
Connection failed to imap.vargosmail.com:143 - connect ECONNREFUSED *************:143

07:56:59
[Discovery/S2-DNS-Guess] No working IMAP server found for host: imap.vargosmail.com

07:57:00
[Discovery/S2-DNS-Guess] Testing IMAP host: mail.vargosmail.com

07:57:00
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:993 (secure: true)

07:57:00
Host mail.vargosmail.com:993 redirects to real hostname: *.firstmail.ltd

07:57:00
[Discovery/S2-DNS-Guess] Found working IMAP server: *.firstmail.ltd:993

07:57:00
[Discovery/S2-DNS-Guess] Using real hostname from TLS certificate: *.firstmail.ltd

07:57:00
[Discovery/S2-DNS-Guess] Found IMAP server, stopping further IMAP tests

07:57:00
[Discovery/S2-DNS-Guess] Testing SMTP servers

07:57:00
[Discovery/S2-DNS-Guess] Testing SMTP host: imap.vargosmail.com

07:57:00
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:587 (secure: true)

07:57:00
Connection failed to imap.vargosmail.com:587 - connect ECONNREFUSED *************:587

07:57:00
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:465 (secure: true)

07:57:00
Connection failed to imap.vargosmail.com:465 - connect ECONNREFUSED *************:465

07:57:00
[Discovery/S2-DNS-Guess] Testing imap.vargosmail.com:25 (secure: false)

07:57:00
Connection failed to imap.vargosmail.com:25 - connect ECONNREFUSED *************:25

07:57:00
[Discovery/S2-DNS-Guess] No working SMTP server found for host: imap.vargosmail.com

07:57:00
[Discovery/S2-DNS-Guess] Testing SMTP host: mail.vargosmail.com

07:57:00
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:587 (secure: true)

07:57:00
Connection failed to mail.vargosmail.com:587 - connect ETIMEDOUT *************:587

07:57:00
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:465 (secure: true)

07:57:01
Connection failed to mail.vargosmail.com:465 - connect ETIMEDOUT *************:465

07:57:01
[Discovery/S2-DNS-Guess] Testing mail.vargosmail.com:25 (secure: false)

07:57:01
Host mail.vargosmail.com:25 is reachable (secure: false)

07:57:01
[Discovery/S2-DNS-Guess] Found working SMTP server: mail.vargosmail.com:25

07:57:01
[Discovery/S2-DNS-Guess] Found SMTP server, stopping further SMTP tests

07:57:01
[Discovery/S2-DNS-Guess] Sequential testing completed for vargosmail.com

07:57:01
[Discovery/S2-DNS-Guess] DNS guessing completed for vargosmail.com. Found: YES

07:57:01
[Discovery/S2-DNS-Guess] IMAP: *.firstmail.ltd:993

07:57:01
[Discovery/S2-DNS-Guess] SMTP: mail.vargosmail.com:25

07:57:01
[Discovery/S2-DNS] Found config via DNS guessing for vargosmail.com

07:57:01
Strategy 2 completed for vargosmail.com: SUCCESS

07:57:01
Discovery successful for vargosmail.com

07:57:01
Successfully discovered config for vargosmail.com. Saving...