/**
 * @file Service for auto-discovering email server settings, now with parallel execution and caching.
 */

import { discoverViaDns } from './discovery/dnsDiscovery';
import { discoverViaExchangeAutodiscover } from './discovery/exchangeDiscovery';
import { discoverViaProviderList } from './discovery/providerDiscovery';
import type { Logger, DiscoveredConfig, DiscoveryOptions } from './discovery/types';

// In-memory cache for discovered configurations
const configCache = new Map<string, DiscoveredConfig>();

/**
 * Main email configuration discovery function.
 * Tries multiple strategies in parallel and returns the first successful result.
 */
export const discoverEmailConfig = async (
  domain: string,
  logger: Logger = () => { /* default no-op logger */ },
  options: DiscoveryOptions = {}
): Promise<DiscoveredConfig | null> => {
  logger('info', `Starting email discovery for domain: ${domain}`);

  if (!domain || domain.length === 0) {
    logger('error', 'Invalid domain provided');
    return null;
  }

  const normalizedDomain = domain.replace(/^(https?:\/\/)?(www\.)?/, '').toLowerCase().trim();
  logger('info', `Normalized domain: ${normalizedDomain}`);

  const finalDomain = normalizedDomain;

  logger('info', `Final domain for discovery: ${finalDomain}`);

  // Check cache first
  if (configCache.has(finalDomain)) {
    logger('info', `Returning cached configuration for ${finalDomain}`);
    return configCache.get(finalDomain)!;
  }

  const discoveryStrategies = [];

  if (!options.skipProviderList) {
    discoveryStrategies.push(discoverViaProviderList(finalDomain, logger));
  }
  if (!options.skipDnsGuessing) {
    discoveryStrategies.push(discoverViaDns(finalDomain, logger));
  }
  if (!options.skipExchangeAutodiscover) {
    discoveryStrategies.push(discoverViaExchangeAutodiscover(finalDomain, logger));
  }

  // Race all strategies and take the first one that resolves with a non-null value
  logger('info', `Running ${discoveryStrategies.length} discovery strategies for ${finalDomain}`);

  try {
    const result = await Promise.any(discoveryStrategies.map((p, index) => p.then(res => {
        logger('info', `Strategy ${index + 1} completed for ${finalDomain}: ${res ? 'SUCCESS' : 'FAILED'}`);
        if (res) return res;
        throw new Error('Strategy failed');
    })));

    if (result) {
        logger('success', `Discovery successful for ${finalDomain}`);
        configCache.set(finalDomain, result); // Cache the successful result
        return result;
    }
  } catch (error) {
    // All strategies failed
    logger('info', `All discovery strategies failed for ${finalDomain}: ${error}`);
  }

  logger('info', `No email configuration found for ${finalDomain}`);
  return null;
};

/**
 * Simplified discovery function for common use cases.
 */
export const quickDiscoverEmailConfig = async (
  domain: string,
  logger?: Logger
): Promise<DiscoveredConfig | null> => {
  return discoverEmailConfig(domain, logger, {
    skipExchangeAutodiscover: true, // Skip complex Exchange discovery for quick results
  });
};

// Re-export types for convenience
export type { Logger, DiscoveredConfig, DiscoveryOptions } from './discovery/types';